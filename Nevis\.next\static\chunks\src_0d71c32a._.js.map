{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/hooks/use-brand-profiles.ts"], "sourcesContent": ["// Hook for managing brand profiles with Firestore\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { brandProfileFirebaseService } from '@/lib/firebase/services/brand-profile-service';\r\nimport { useUserId } from './use-firebase-auth';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\n\r\nexport interface BrandProfilesState {\r\n  profiles: CompleteBrandProfile[];\r\n  currentProfile: CompleteBrandProfile | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  saving: boolean;\r\n}\r\n\r\nexport function useBrandProfiles() {\r\n  const userId = useUserId();\r\n  const [state, setState] = useState<BrandProfilesState>({\r\n    profiles: [],\r\n    currentProfile: null,\r\n    loading: true,\r\n    error: null,\r\n    saving: false,\r\n  });\r\n\r\n  // Load brand profiles\r\n  const loadProfiles = useCallback(async () => {\r\n    if (!userId) {\r\n      setState(prev => ({ ...prev, loading: false, profiles: [], currentProfile: null }));\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n\r\n      // Try to load from Firestore, fallback to localStorage\r\n      let profiles: CompleteBrandProfile[] = [];\r\n      try {\r\n        profiles = await brandProfileFirebaseService.getUserBrandProfiles(userId);\r\n      } catch (firebaseError) {\r\n        console.log('🔄 Firebase unavailable, using localStorage fallback');\r\n        // Fallback to localStorage\r\n        const stored = localStorage.getItem('completeBrandProfile');\r\n        if (stored) {\r\n          const profile = JSON.parse(stored);\r\n          profiles = [profile];\r\n        }\r\n      }\r\n\r\n      const currentProfile = profiles.length > 0 ? profiles[0] : null;\r\n\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles,\r\n        currentProfile,\r\n        loading: false,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to load profiles',\r\n      }));\r\n    }\r\n  }, [userId]);\r\n\r\n  // Save brand profile\r\n  const saveProfile = useCallback(async (profile: CompleteBrandProfile): Promise<string> => {\r\n    if (!userId) {\r\n      throw new Error('User must be authenticated to save profile');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, saving: true, error: null }));\r\n\r\n      const profileId = await brandProfileFirebaseService.saveBrandProfile(profile, userId);\r\n\r\n      // Reload profiles to get the updated list\r\n      await loadProfiles();\r\n\r\n      setState(prev => ({ ...prev, saving: false }));\r\n      return profileId;\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to save profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId, loadProfiles]);\r\n\r\n  // Update brand profile\r\n  const updateProfile = useCallback(async (\r\n    profileId: string,\r\n    updates: Partial<CompleteBrandProfile>\r\n  ): Promise<void> => {\r\n    if (!userId) {\r\n      throw new Error('User must be authenticated to update profile');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, saving: true, error: null }));\r\n\r\n      await brandProfileFirebaseService.updateBrandProfile(profileId, updates);\r\n\r\n      // Update local state optimistically\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles: prev.profiles.map(p =>\r\n          p.id === profileId ? { ...p, ...updates } : p\r\n        ),\r\n        currentProfile: prev.currentProfile?.id === profileId\r\n          ? { ...prev.currentProfile, ...updates }\r\n          : prev.currentProfile,\r\n        saving: false,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to update profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId]);\r\n\r\n  // Delete brand profile\r\n  const deleteProfile = useCallback(async (profileId: string): Promise<void> => {\r\n    if (!userId) {\r\n      throw new Error('User must be authenticated to delete profile');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, error: null }));\r\n\r\n      await brandProfileFirebaseService.delete(profileId);\r\n\r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles: prev.profiles.filter(p => p.id !== profileId),\r\n        currentProfile: prev.currentProfile?.id === profileId ? null : prev.currentProfile,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        error: error instanceof Error ? error.message : 'Failed to delete profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId]);\r\n\r\n  // Set current profile\r\n  const setCurrentProfile = useCallback((profile: CompleteBrandProfile | null) => {\r\n    console.log('🎯 setCurrentProfile called with:', profile?.businessName || 'null');\r\n    setState(prev => {\r\n      console.log('📊 Previous current profile:', prev.currentProfile?.businessName || 'none');\r\n      return { ...prev, currentProfile: profile };\r\n    });\r\n  }, []);\r\n\r\n  // Get profile by ID\r\n  const getProfileById = useCallback(async (profileId: string): Promise<CompleteBrandProfile | null> => {\r\n    try {\r\n      return await brandProfileFirebaseService.getBrandProfileById(profileId);\r\n    } catch (error) {\r\n      console.error('Failed to get profile by ID:', error);\r\n      return null;\r\n    }\r\n  }, []);\r\n\r\n  // Load profiles when userId changes\r\n  useEffect(() => {\r\n    loadProfiles();\r\n  }, [loadProfiles]);\r\n\r\n  // Set up real-time listener\r\n  useEffect(() => {\r\n    if (!userId) return;\r\n\r\n    const unsubscribe = brandProfileFirebaseService.onUserDocumentsChange(\r\n      userId,\r\n      (profiles) => {\r\n        console.log('🔄 Real-time profiles update received:', profiles.length, 'profiles');\r\n        setState(prev => {\r\n          // Preserve the current profile if it still exists in the updated profiles\r\n          let preservedCurrentProfile = prev.currentProfile;\r\n\r\n          if (prev.currentProfile) {\r\n            // Check if current profile still exists in the updated list\r\n            const stillExists = profiles.find(p => p.id === (prev.currentProfile as any)?.id);\r\n            if (!stillExists) {\r\n              console.log('⚠️ Current profile no longer exists, clearing selection');\r\n              preservedCurrentProfile = null;\r\n            } else {\r\n              // Update with the latest version of the current profile\r\n              const updatedProfile = profiles.find(p => p.id === (prev.currentProfile as any)?.id);\r\n              if (updatedProfile) {\r\n                console.log('✅ Current profile updated with latest data:', updatedProfile.businessName);\r\n                preservedCurrentProfile = updatedProfile;\r\n              }\r\n            }\r\n          }\r\n\r\n          // Only auto-select first profile if there's no current profile at all AND this is the initial load\r\n          const finalCurrentProfile = preservedCurrentProfile ||\r\n            (!prev.currentProfile && profiles.length > 0 ? profiles[0] : null);\r\n\r\n          if (finalCurrentProfile && !prev.currentProfile) {\r\n            console.log('🎯 Auto-selecting first profile on initial load:', finalCurrentProfile.businessName);\r\n          }\r\n\r\n          return {\r\n            ...prev,\r\n            profiles,\r\n            currentProfile: finalCurrentProfile,\r\n          };\r\n        });\r\n      },\r\n      { orderBy: 'updatedAt', orderDirection: 'desc' }\r\n    );\r\n\r\n    return unsubscribe;\r\n  }, [userId]);\r\n\r\n  return {\r\n    ...state,\r\n    saveProfile,\r\n    updateProfile,\r\n    deleteProfile,\r\n    setCurrentProfile,\r\n    getProfileById,\r\n    reload: loadProfiles,\r\n  };\r\n}\r\n\r\n// Hook for getting the current/latest brand profile\r\nexport function useCurrentBrandProfile() {\r\n  const { currentProfile, loading, error } = useBrandProfiles();\r\n\r\n  return {\r\n    profile: currentProfile,\r\n    loading,\r\n    error,\r\n  };\r\n}\r\n\r\n// Hook for checking if user has a complete brand profile\r\nexport function useHasCompleteBrandProfile(): boolean {\r\n  const { currentProfile, loading } = useBrandProfiles();\r\n\r\n  if (loading || !currentProfile) return false;\r\n\r\n  // Check if profile has required fields\r\n  const requiredFields = [\r\n    'businessName',\r\n    'businessType',\r\n    'location',\r\n    'description',\r\n    'services',\r\n  ];\r\n\r\n  return requiredFields.every(field => {\r\n    const value = currentProfile[field as keyof CompleteBrandProfile];\r\n    return value && (\r\n      typeof value === 'string' ? value.trim().length > 0 :\r\n        Array.isArray(value) ? value.length > 0 :\r\n          true\r\n    );\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;AAClD;AACA;AACA;;;;;AAWO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;QACrD,UAAU,EAAE;QACZ,gBAAgB;QAChB,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,sBAAsB;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC/B,IAAI,CAAC,QAAQ;gBACX;kEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;4BAAO,UAAU,EAAE;4BAAE,gBAAgB;wBAAK,CAAC;;gBACjF;YACF;YAEA,IAAI;gBACF;kEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;4BAAM,OAAO;wBAAK,CAAC;;gBAEzD,uDAAuD;gBACvD,IAAI,WAAmC,EAAE;gBACzC,IAAI;oBACF,WAAW,MAAM,oKAAA,CAAA,8BAA2B,CAAC,oBAAoB,CAAC;gBACpE,EAAE,OAAO,eAAe;oBACtB,QAAQ,GAAG,CAAC;oBACZ,2BAA2B;oBAC3B,MAAM,SAAS,aAAa,OAAO,CAAC;oBACpC,IAAI,QAAQ;wBACV,MAAM,UAAU,KAAK,KAAK,CAAC;wBAC3B,WAAW;4BAAC;yBAAQ;oBACtB;gBACF;gBAEA,MAAM,iBAAiB,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG;gBAE3D;kEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP;4BACA;4BACA,SAAS;wBACX,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd;kEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;YACH;QACF;qDAAG;QAAC;KAAO;IAEX,qBAAqB;IACrB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YACrC,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;gBACF;iEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;4BAAM,OAAO;wBAAK,CAAC;;gBAExD,MAAM,YAAY,MAAM,oKAAA,CAAA,8BAA2B,CAAC,gBAAgB,CAAC,SAAS;gBAE9E,0CAA0C;gBAC1C,MAAM;gBAEN;iEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;wBAAM,CAAC;;gBAC5C,OAAO;YACT,EAAE,OAAO,OAAO;gBACd;iEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,QAAQ;4BACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBACD,MAAM;YACR;QACF;oDAAG;QAAC;QAAQ;KAAa;IAEzB,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAChC,WACA;YAEA,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;gBACF;mEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;4BAAM,OAAO;wBAAK,CAAC;;gBAExD,MAAM,oKAAA,CAAA,8BAA2B,CAAC,kBAAkB,CAAC,WAAW;gBAEhE,oCAAoC;gBACpC;mEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,UAAU,KAAK,QAAQ,CAAC,GAAG;+EAAC,CAAA,IAC1B,EAAE,EAAE,KAAK,YAAY;wCAAE,GAAG,CAAC;wCAAE,GAAG,OAAO;oCAAC,IAAI;;4BAE9C,gBAAgB,KAAK,cAAc,EAAE,OAAO,YACxC;gCAAE,GAAG,KAAK,cAAc;gCAAE,GAAG,OAAO;4BAAC,IACrC,KAAK,cAAc;4BACvB,QAAQ;wBACV,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd;mEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,QAAQ;4BACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBACD,MAAM;YACR;QACF;sDAAG;QAAC;KAAO;IAEX,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAAO;YACvC,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;gBACF;mEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,OAAO;wBAAK,CAAC;;gBAE1C,MAAM,oKAAA,CAAA,8BAA2B,CAAC,MAAM,CAAC;gBAEzC,qBAAqB;gBACrB;mEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,UAAU,KAAK,QAAQ,CAAC,MAAM;+EAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;4BAC7C,gBAAgB,KAAK,cAAc,EAAE,OAAO,YAAY,OAAO,KAAK,cAAc;wBACpF,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd;mEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBACD,MAAM;YACR;QACF;sDAAG;QAAC;KAAO;IAEX,sBAAsB;IACtB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACrC,QAAQ,GAAG,CAAC,qCAAqC,SAAS,gBAAgB;YAC1E;mEAAS,CAAA;oBACP,QAAQ,GAAG,CAAC,gCAAgC,KAAK,cAAc,EAAE,gBAAgB;oBACjF,OAAO;wBAAE,GAAG,IAAI;wBAAE,gBAAgB;oBAAQ;gBAC5C;;QACF;0DAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO;YACxC,IAAI;gBACF,OAAO,MAAM,oKAAA,CAAA,8BAA2B,CAAC,mBAAmB,CAAC;YAC/D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO;YACT;QACF;uDAAG,EAAE;IAEL,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAa;IAEjB,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,QAAQ;YAEb,MAAM,cAAc,oKAAA,CAAA,8BAA2B,CAAC,qBAAqB,CACnE;0DACA,CAAC;oBACC,QAAQ,GAAG,CAAC,0CAA0C,SAAS,MAAM,EAAE;oBACvE;kEAAS,CAAA;4BACP,0EAA0E;4BAC1E,IAAI,0BAA0B,KAAK,cAAc;4BAEjD,IAAI,KAAK,cAAc,EAAE;gCACvB,4DAA4D;gCAC5D,MAAM,cAAc,SAAS,IAAI;0FAAC,CAAA,IAAK,EAAE,EAAE,KAAM,KAAK,cAAc,EAAU;;gCAC9E,IAAI,CAAC,aAAa;oCAChB,QAAQ,GAAG,CAAC;oCACZ,0BAA0B;gCAC5B,OAAO;oCACL,wDAAwD;oCACxD,MAAM,iBAAiB,SAAS,IAAI;iGAAC,CAAA,IAAK,EAAE,EAAE,KAAM,KAAK,cAAc,EAAU;;oCACjF,IAAI,gBAAgB;wCAClB,QAAQ,GAAG,CAAC,+CAA+C,eAAe,YAAY;wCACtF,0BAA0B;oCAC5B;gCACF;4BACF;4BAEA,mGAAmG;4BACnG,MAAM,sBAAsB,2BAC1B,CAAC,CAAC,KAAK,cAAc,IAAI,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG,IAAI;4BAEnE,IAAI,uBAAuB,CAAC,KAAK,cAAc,EAAE;gCAC/C,QAAQ,GAAG,CAAC,oDAAoD,oBAAoB,YAAY;4BAClG;4BAEA,OAAO;gCACL,GAAG,IAAI;gCACP;gCACA,gBAAgB;4BAClB;wBACF;;gBACF;yDACA;gBAAE,SAAS;gBAAa,gBAAgB;YAAO;YAGjD,OAAO;QACT;qCAAG;QAAC;KAAO;IAEX,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA,QAAQ;IACV;AACF;GA5NgB;;QACC,0IAAA,CAAA,YAAS;;;AA8NnB,SAAS;;IACd,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG;IAE3C,OAAO;QACL,SAAS;QACT;QACA;IACF;AACF;IARgB;;QAC6B;;;AAUtC,SAAS;;IACd,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG;IAEpC,IAAI,WAAW,CAAC,gBAAgB,OAAO;IAEvC,uCAAuC;IACvC,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,eAAe,KAAK,CAAC,CAAA;QAC1B,MAAM,QAAQ,cAAc,CAAC,MAAoC;QACjE,OAAO,SAAS,CACd,OAAO,UAAU,WAAW,MAAM,IAAI,GAAG,MAAM,GAAG,IAChD,MAAM,OAAO,CAAC,SAAS,MAAM,MAAM,GAAG,IACpC,IACN;IACF;AACF;IAtBgB;;QACsB", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/hooks/use-generated-posts.ts"], "sourcesContent": ["// Hook for managing generated posts with Firestore\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { generatedPostFirebaseService } from '@/lib/firebase/services/generated-post-service';\r\nimport { useUserId } from './use-firebase-auth';\r\nimport { useCurrentBrandProfile } from './use-brand-profiles';\r\nimport type { GeneratedPost, Platform } from '@/lib/types';\r\n\r\nexport interface GeneratedPostsState {\r\n  posts: GeneratedPost[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  saving: boolean;\r\n}\r\n\r\nexport function useGeneratedPosts(limit: number = 10) {\r\n  const userId = useUserId();\r\n  const { profile: currentProfile } = useCurrentBrandProfile();\r\n  const [state, setState] = useState<GeneratedPostsState>({\r\n    posts: [],\r\n    loading: true,\r\n    error: null,\r\n    saving: false,\r\n  });\r\n\r\n  // Load generated posts\r\n  const loadPosts = useCallback(async () => {\r\n    if (!userId) {\r\n      setState(prev => ({ ...prev, loading: false, posts: [] }));\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n      \r\n      const posts = await generatedPostFirebaseService.getUserGeneratedPosts(userId, { limit });\r\n      \r\n      setState(prev => ({\r\n        ...prev,\r\n        posts,\r\n        loading: false,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to load posts',\r\n      }));\r\n    }\r\n  }, [userId, limit]);\r\n\r\n  // Save generated post\r\n  const savePost = useCallback(async (post: GeneratedPost): Promise<string> => {\r\n    if (!userId || !currentProfile) {\r\n      throw new Error('User must be authenticated and have a brand profile to save posts');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, saving: true, error: null }));\r\n      \r\n      const postId = await generatedPostFirebaseService.saveGeneratedPost(post, userId, currentProfile.id);\r\n      \r\n      // Add to local state optimistically\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: [{ ...post, id: postId }, ...prev.posts].slice(0, limit),\r\n        saving: false,\r\n      }));\r\n      \r\n      return postId;\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to save post',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId, currentProfile, limit]);\r\n\r\n  // Update post analytics\r\n  const updatePostAnalytics = useCallback(async (\r\n    postId: string,\r\n    analytics: {\r\n      views?: number;\r\n      likes?: number;\r\n      shares?: number;\r\n      comments?: number;\r\n      qualityScore?: number;\r\n      engagementPrediction?: number;\r\n      brandAlignmentScore?: number;\r\n    }\r\n  ): Promise<void> => {\r\n    try {\r\n      await generatedPostFirebaseService.updatePostAnalytics(postId, analytics);\r\n      \r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: prev.posts.map(post => \r\n          post.id === postId \r\n            ? { ...post, ...analytics }\r\n            : post\r\n        ),\r\n      }));\r\n    } catch (error) {\r\n      console.error('Failed to update post analytics:', error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Update post status\r\n  const updatePostStatus = useCallback(async (\r\n    postId: string,\r\n    status: 'generated' | 'edited' | 'posted'\r\n  ): Promise<void> => {\r\n    try {\r\n      const firestoreStatus = status === 'posted' ? 'published' : 'draft';\r\n      const publishedAt = status === 'posted' ? new Date() : undefined;\r\n      \r\n      await generatedPostFirebaseService.updatePostStatus(postId, firestoreStatus, undefined, publishedAt);\r\n      \r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: prev.posts.map(post => \r\n          post.id === postId \r\n            ? { ...post, status }\r\n            : post\r\n        ),\r\n      }));\r\n    } catch (error) {\r\n      console.error('Failed to update post status:', error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Delete post\r\n  const deletePost = useCallback(async (postId: string): Promise<void> => {\r\n    try {\r\n      await generatedPostFirebaseService.delete(postId);\r\n      \r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: prev.posts.filter(post => post.id !== postId),\r\n      }));\r\n    } catch (error) {\r\n      console.error('Failed to delete post:', error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Get posts by platform\r\n  const getPostsByPlatform = useCallback(async (platform: Platform): Promise<GeneratedPost[]> => {\r\n    if (!userId) return [];\r\n    \r\n    try {\r\n      return await generatedPostFirebaseService.getUserGeneratedPosts(userId, { platform, limit });\r\n    } catch (error) {\r\n      console.error('Failed to get posts by platform:', error);\r\n      return [];\r\n    }\r\n  }, [userId, limit]);\r\n\r\n  // Get posts by status\r\n  const getPostsByStatus = useCallback(async (status: 'generated' | 'edited' | 'posted'): Promise<GeneratedPost[]> => {\r\n    if (!userId) return [];\r\n    \r\n    try {\r\n      const firestoreStatus = status === 'posted' ? 'published' : 'draft';\r\n      return await generatedPostFirebaseService.getPostsByStatus(userId, firestoreStatus);\r\n    } catch (error) {\r\n      console.error('Failed to get posts by status:', error);\r\n      return [];\r\n    }\r\n  }, [userId]);\r\n\r\n  // Load posts when dependencies change\r\n  useEffect(() => {\r\n    loadPosts();\r\n  }, [loadPosts]);\r\n\r\n  // Set up real-time listener\r\n  useEffect(() => {\r\n    if (!userId) return;\r\n\r\n    const unsubscribe = generatedPostFirebaseService.onUserDocumentsChange(\r\n      userId,\r\n      (posts) => {\r\n        setState(prev => ({\r\n          ...prev,\r\n          posts: posts.slice(0, limit),\r\n        }));\r\n      },\r\n      { limit, orderBy: 'createdAt', orderDirection: 'desc' }\r\n    );\r\n\r\n    return unsubscribe;\r\n  }, [userId, limit]);\r\n\r\n  return {\r\n    ...state,\r\n    savePost,\r\n    updatePostAnalytics,\r\n    updatePostStatus,\r\n    deletePost,\r\n    getPostsByPlatform,\r\n    getPostsByStatus,\r\n    reload: loadPosts,\r\n  };\r\n}\r\n\r\n// Hook for getting posts for a specific brand profile\r\nexport function useGeneratedPostsForBrand(brandProfileId: string, limit: number = 10) {\r\n  const userId = useUserId();\r\n  const [posts, setPosts] = useState<GeneratedPost[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const loadPosts = useCallback(async () => {\r\n    if (!userId || !brandProfileId) {\r\n      setPosts([]);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      const brandPosts = await generatedPostFirebaseService.getRecentPostsForBrand(\r\n        userId, \r\n        brandProfileId, \r\n        limit\r\n      );\r\n      \r\n      setPosts(brandPosts);\r\n      setLoading(false);\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to load posts');\r\n      setLoading(false);\r\n    }\r\n  }, [userId, brandProfileId, limit]);\r\n\r\n  useEffect(() => {\r\n    loadPosts();\r\n  }, [loadPosts]);\r\n\r\n  return {\r\n    posts,\r\n    loading,\r\n    error,\r\n    reload: loadPosts,\r\n  };\r\n}\r\n\r\n// Hook for post statistics\r\nexport function usePostStatistics() {\r\n  const { posts } = useGeneratedPosts(100); // Get more posts for statistics\r\n  \r\n  const statistics = {\r\n    total: posts.length,\r\n    byPlatform: posts.reduce((acc, post) => {\r\n      acc[post.platform] = (acc[post.platform] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<Platform, number>),\r\n    byStatus: posts.reduce((acc, post) => {\r\n      acc[post.status] = (acc[post.status] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<string, number>),\r\n    averageQuality: posts.length > 0 \r\n      ? posts.reduce((sum, post) => sum + (post.qualityScore || 0), 0) / posts.length \r\n      : 0,\r\n    averageEngagement: posts.length > 0 \r\n      ? posts.reduce((sum, post) => sum + (post.engagementPrediction || 0), 0) / posts.length \r\n      : 0,\r\n  };\r\n\r\n  return statistics;\r\n}\r\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;;;AACnD;AACA;AACA;AACA;;;;;;AAUO,SAAS,kBAAkB,QAAgB,EAAE;;IAClD,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,SAAS,cAAc,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,yBAAsB,AAAD;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,OAAO,EAAE;QACT,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,uBAAuB;IACvB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC5B,IAAI,CAAC,QAAQ;gBACX;gEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;4BAAO,OAAO,EAAE;wBAAC,CAAC;;gBACxD;YACF;YAEA,IAAI;gBACF;gEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;4BAAM,OAAO;wBAAK,CAAC;;gBAEzD,MAAM,QAAQ,MAAM,qKAAA,CAAA,+BAA4B,CAAC,qBAAqB,CAAC,QAAQ;oBAAE;gBAAM;gBAEvF;gEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP;4BACA,SAAS;wBACX,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd;gEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;YACH;QACF;mDAAG;QAAC;QAAQ;KAAM;IAElB,sBAAsB;IACtB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO;YAClC,IAAI,CAAC,UAAU,CAAC,gBAAgB;gBAC9B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;gBACF;+DAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;4BAAM,OAAO;wBAAK,CAAC;;gBAExD,MAAM,SAAS,MAAM,qKAAA,CAAA,+BAA4B,CAAC,iBAAiB,CAAC,MAAM,QAAQ,eAAe,EAAE;gBAEnG,oCAAoC;gBACpC;+DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO;gCAAC;oCAAE,GAAG,IAAI;oCAAE,IAAI;gCAAO;mCAAM,KAAK,KAAK;6BAAC,CAAC,KAAK,CAAC,GAAG;4BACzD,QAAQ;wBACV,CAAC;;gBAED,OAAO;YACT,EAAE,OAAO,OAAO;gBACd;+DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,QAAQ;4BACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBACD,MAAM;YACR;QACF;kDAAG;QAAC;QAAQ;QAAgB;KAAM;IAElC,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,OACtC,QACA;YAUA,IAAI;gBACF,MAAM,qKAAA,CAAA,+BAA4B,CAAC,mBAAmB,CAAC,QAAQ;gBAE/D,qBAAqB;gBACrB;0EAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO,KAAK,KAAK,CAAC,GAAG;sFAAC,CAAA,OACpB,KAAK,EAAE,KAAK,SACR;wCAAE,GAAG,IAAI;wCAAE,GAAG,SAAS;oCAAC,IACxB;;wBAER,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM;YACR;QACF;6DAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OACnC,QACA;YAEA,IAAI;gBACF,MAAM,kBAAkB,WAAW,WAAW,cAAc;gBAC5D,MAAM,cAAc,WAAW,WAAW,IAAI,SAAS;gBAEvD,MAAM,qKAAA,CAAA,+BAA4B,CAAC,gBAAgB,CAAC,QAAQ,iBAAiB,WAAW;gBAExF,qBAAqB;gBACrB;uEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO,KAAK,KAAK,CAAC,GAAG;mFAAC,CAAA,OACpB,KAAK,EAAE,KAAK,SACR;wCAAE,GAAG,IAAI;wCAAE;oCAAO,IAClB;;wBAER,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM;YACR;QACF;0DAAG,EAAE;IAEL,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YACpC,IAAI;gBACF,MAAM,qKAAA,CAAA,+BAA4B,CAAC,MAAM,CAAC;gBAE1C,qBAAqB;gBACrB;iEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO,KAAK,KAAK,CAAC,MAAM;6EAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;wBAC/C,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM;YACR;QACF;oDAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,OAAO;YAC5C,IAAI,CAAC,QAAQ,OAAO,EAAE;YAEtB,IAAI;gBACF,OAAO,MAAM,qKAAA,CAAA,+BAA4B,CAAC,qBAAqB,CAAC,QAAQ;oBAAE;oBAAU;gBAAM;YAC5F,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,OAAO,EAAE;YACX;QACF;4DAAG;QAAC;QAAQ;KAAM;IAElB,sBAAsB;IACtB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO;YAC1C,IAAI,CAAC,QAAQ,OAAO,EAAE;YAEtB,IAAI;gBACF,MAAM,kBAAkB,WAAW,WAAW,cAAc;gBAC5D,OAAO,MAAM,qKAAA,CAAA,+BAA4B,CAAC,gBAAgB,CAAC,QAAQ;YACrE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,OAAO,EAAE;YACX;QACF;0DAAG;QAAC;KAAO;IAEX,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAU;IAEd,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,QAAQ;YAEb,MAAM,cAAc,qKAAA,CAAA,+BAA4B,CAAC,qBAAqB,CACpE;2DACA,CAAC;oBACC;mEAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,OAAO,MAAM,KAAK,CAAC,GAAG;4BACxB,CAAC;;gBACH;0DACA;gBAAE;gBAAO,SAAS;gBAAa,gBAAgB;YAAO;YAGxD,OAAO;QACT;sCAAG;QAAC;QAAQ;KAAM;IAElB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA;QACA,QAAQ;IACV;AACF;GApMgB;;QACC,0IAAA,CAAA,YAAS;QACY,2IAAA,CAAA,yBAAsB;;;AAqMrD,SAAS,0BAA0B,cAAsB,EAAE,QAAgB,EAAE;;IAClF,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,gBAAgB;gBAC9B,SAAS,EAAE;gBACX,WAAW;gBACX;YACF;YAEA,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,MAAM,aAAa,MAAM,qKAAA,CAAA,+BAA4B,CAAC,sBAAsB,CAC1E,QACA,gBACA;gBAGF,SAAS;gBACT,WAAW;YACb,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC9C,WAAW;YACb;QACF;2DAAG;QAAC;QAAQ;QAAgB;KAAM;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR;QACF;8CAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;QACA;QACA,QAAQ;IACV;AACF;IAzCgB;;QACC,0IAAA,CAAA,YAAS;;;AA2CnB,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,kBAAkB,MAAM,gCAAgC;IAE1E,MAAM,aAAa;QACjB,OAAO,MAAM,MAAM;QACnB,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK;YAC7B,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI;YACjD,OAAO;QACT,GAAG,CAAC;QACJ,UAAU,MAAM,MAAM,CAAC,CAAC,KAAK;YAC3B,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI;YAC7C,OAAO;QACT,GAAG,CAAC;QACJ,gBAAgB,MAAM,MAAM,GAAG,IAC3B,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG,KAAK,MAAM,MAAM,GAC7E;QACJ,mBAAmB,MAAM,MAAM,GAAG,IAC9B,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,oBAAoB,IAAI,CAAC,GAAG,KAAK,MAAM,MAAM,GACrF;IACN;IAEA,OAAO;AACT;IAtBgB;;QACI", "debugId": null}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/debug-database/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Database, RefreshCw, User, FileText, Image, BarChart3 } from 'lucide-react';\r\nimport { useFirebaseAuth } from '@/hooks/use-firebase-auth';\r\nimport { useBrandProfiles } from '@/hooks/use-brand-profiles';\r\nimport { useGeneratedPosts } from '@/hooks/use-generated-posts';\r\n\r\nexport default function DatabaseDebugPage() {\r\n  const { user, loading: authLoading } = useFirebaseAuth();\r\n  const { profiles, loading: profilesLoading } = useBrandProfiles();\r\n  const { posts, loading: postsLoading } = useGeneratedPosts(50); // Get more posts for debugging\r\n  const [refresh<PERSON><PERSON>, setRefreshKey] = useState(0);\r\n\r\n  const refresh = () => {\r\n    setRefreshKey(prev => prev + 1);\r\n  };\r\n\r\n  if (authLoading) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <Database className=\"h-8 w-8 animate-spin mx-auto mb-4\" />\r\n          <p>Loading authentication...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50 p-4\">\r\n      <div className=\"max-w-6xl mx-auto\">\r\n        <div className=\"mb-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <h1 className=\"text-3xl font-bold flex items-center gap-2\">\r\n                <Database className=\"h-8 w-8\" />\r\n                Database Debug Console\r\n              </h1>\r\n              <p className=\"text-gray-600 mt-2\">\r\n                View your Firestore database data in real-time\r\n              </p>\r\n            </div>\r\n            <Button onClick={refresh} variant=\"outline\">\r\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\r\n              Refresh\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* User Info */}\r\n        <Card className=\"mb-6\">\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <User className=\"h-5 w-5\" />\r\n              Current User\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            {user ? (\r\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n                <div>\r\n                  <p className=\"text-sm text-gray-600\">User ID</p>\r\n                  <p className=\"font-mono text-xs bg-gray-100 p-1 rounded\">{user.uid}</p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm text-gray-600\">Email</p>\r\n                  <p className=\"font-medium\">{user.email || 'N/A'}</p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm text-gray-600\">Display Name</p>\r\n                  <p className=\"font-medium\">{user.displayName || 'N/A'}</p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm text-gray-600\">Type</p>\r\n                  <Badge variant={user.isAnonymous ? 'secondary' : 'default'}>\r\n                    {user.isAnonymous ? 'Anonymous' : 'Authenticated'}\r\n                  </Badge>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <p className=\"text-gray-500\">No user authenticated</p>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Tabs defaultValue=\"posts\" className=\"space-y-4\">\r\n          <TabsList className=\"grid w-full grid-cols-3\">\r\n            <TabsTrigger value=\"posts\" className=\"flex items-center gap-2\">\r\n              <FileText className=\"h-4 w-4\" />\r\n              Generated Posts ({posts.length})\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"profiles\" className=\"flex items-center gap-2\">\r\n              <User className=\"h-4 w-4\" />\r\n              Brand Profiles ({profiles.length})\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"analytics\" className=\"flex items-center gap-2\">\r\n              <BarChart3 className=\"h-4 w-4\" />\r\n              Analytics\r\n            </TabsTrigger>\r\n          </TabsList>\r\n\r\n          {/* Generated Posts */}\r\n          <TabsContent value=\"posts\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle>Generated Posts Collection</CardTitle>\r\n                <CardDescription>\r\n                  All posts saved to Firestore database\r\n                  {postsLoading && <span className=\"ml-2\">Loading...</span>}\r\n                </CardDescription>\r\n              </CardHeader>\r\n              <CardContent>\r\n                {posts.length === 0 ? (\r\n                  <div className=\"text-center py-8\">\r\n                    <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n                    <p className=\"text-gray-500\">No posts found in database</p>\r\n                    <p className=\"text-sm text-gray-400 mt-2\">\r\n                      Generate some content to see posts here\r\n                    </p>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"space-y-4\">\r\n                    {posts.map((post, index) => (\r\n                      <div key={post.id} className=\"border rounded-lg p-4 bg-white\">\r\n                        <div className=\"flex items-start justify-between mb-3\">\r\n                          <div>\r\n                            <h3 className=\"font-medium\">Post #{index + 1}</h3>\r\n                            <p className=\"text-xs text-gray-500 font-mono\">{post.id}</p>\r\n                          </div>\r\n                          <div className=\"flex gap-2\">\r\n                            <Badge variant=\"outline\">{post.platform}</Badge>\r\n                            <Badge variant={post.status === 'posted' ? 'default' : 'secondary'}>\r\n                              {post.status}\r\n                            </Badge>\r\n                          </div>\r\n                        </div>\r\n                        \r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                          <div>\r\n                            <p className=\"text-sm text-gray-600 mb-1\">Content</p>\r\n                            <p className=\"text-sm bg-gray-50 p-2 rounded max-h-20 overflow-y-auto\">\r\n                              {post.content}\r\n                            </p>\r\n                          </div>\r\n                          <div>\r\n                            <p className=\"text-sm text-gray-600 mb-1\">Hashtags</p>\r\n                            <p className=\"text-sm bg-gray-50 p-2 rounded\">\r\n                              {Array.isArray(post.hashtags) ? post.hashtags.join(' ') : post.hashtags}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n\r\n                        {(post.qualityScore || post.engagementPrediction) && (\r\n                          <div className=\"mt-3 pt-3 border-t\">\r\n                            <p className=\"text-sm text-gray-600 mb-2\">AI Metrics</p>\r\n                            <div className=\"flex gap-4 text-sm\">\r\n                              {post.qualityScore && (\r\n                                <span>Quality: <strong>{post.qualityScore}/10</strong></span>\r\n                              )}\r\n                              {post.engagementPrediction && (\r\n                                <span>Engagement: <strong>{post.engagementPrediction}/10</strong></span>\r\n                              )}\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n\r\n                        <div className=\"mt-3 pt-3 border-t text-xs text-gray-500\">\r\n                          Created: {new Date(post.date).toLocaleString()}\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          {/* Brand Profiles */}\r\n          <TabsContent value=\"profiles\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle>Brand Profiles Collection</CardTitle>\r\n                <CardDescription>\r\n                  All brand profiles saved to Firestore database\r\n                  {profilesLoading && <span className=\"ml-2\">Loading...</span>}\r\n                </CardDescription>\r\n              </CardHeader>\r\n              <CardContent>\r\n                {profiles.length === 0 ? (\r\n                  <div className=\"text-center py-8\">\r\n                    <User className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n                    <p className=\"text-gray-500\">No brand profiles found in database</p>\r\n                    <p className=\"text-sm text-gray-400 mt-2\">\r\n                      Create a brand profile to see it here\r\n                    </p>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"space-y-4\">\r\n                    {profiles.map((profile, index) => (\r\n                      <div key={profile.id} className=\"border rounded-lg p-4 bg-white\">\r\n                        <div className=\"flex items-start justify-between mb-3\">\r\n                          <div>\r\n                            <h3 className=\"font-medium\">{profile.businessName}</h3>\r\n                            <p className=\"text-xs text-gray-500 font-mono\">{profile.id}</p>\r\n                          </div>\r\n                          <Badge variant=\"outline\">{profile.businessType}</Badge>\r\n                        </div>\r\n                        \r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                          <div>\r\n                            <p className=\"text-sm text-gray-600 mb-1\">Description</p>\r\n                            <p className=\"text-sm bg-gray-50 p-2 rounded max-h-20 overflow-y-auto\">\r\n                              {profile.description}\r\n                            </p>\r\n                          </div>\r\n                          <div>\r\n                            <p className=\"text-sm text-gray-600 mb-1\">Location</p>\r\n                            <p className=\"text-sm bg-gray-50 p-2 rounded\">\r\n                              {profile.location}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n\r\n                        {profile.services && profile.services.length > 0 && (\r\n                          <div className=\"mt-3 pt-3 border-t\">\r\n                            <p className=\"text-sm text-gray-600 mb-2\">Services ({profile.services.length})</p>\r\n                            <div className=\"flex flex-wrap gap-1\">\r\n                              {profile.services.slice(0, 3).map((service, idx) => (\r\n                                <Badge key={idx} variant=\"secondary\" className=\"text-xs\">\r\n                                  {service.name}\r\n                                </Badge>\r\n                              ))}\r\n                              {profile.services.length > 3 && (\r\n                                <Badge variant=\"outline\" className=\"text-xs\">\r\n                                  +{profile.services.length - 3} more\r\n                                </Badge>\r\n                              )}\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n\r\n                        <div className=\"mt-3 pt-3 border-t text-xs text-gray-500\">\r\n                          Created: {new Date(profile.createdAt).toLocaleString()}\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          {/* Analytics */}\r\n          <TabsContent value=\"analytics\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle>Database Analytics</CardTitle>\r\n                <CardDescription>\r\n                  Overview of your database usage\r\n                </CardDescription>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n                  <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\r\n                    <FileText className=\"h-8 w-8 text-blue-600 mx-auto mb-2\" />\r\n                    <p className=\"text-2xl font-bold text-blue-600\">{posts.length}</p>\r\n                    <p className=\"text-sm text-blue-600\">Generated Posts</p>\r\n                  </div>\r\n                  <div className=\"text-center p-4 bg-green-50 rounded-lg\">\r\n                    <User className=\"h-8 w-8 text-green-600 mx-auto mb-2\" />\r\n                    <p className=\"text-2xl font-bold text-green-600\">{profiles.length}</p>\r\n                    <p className=\"text-sm text-green-600\">Brand Profiles</p>\r\n                  </div>\r\n                  <div className=\"text-center p-4 bg-purple-50 rounded-lg\">\r\n                    <BarChart3 className=\"h-8 w-8 text-purple-600 mx-auto mb-2\" />\r\n                    <p className=\"text-2xl font-bold text-purple-600\">\r\n                      {posts.filter(p => p.qualityScore).length}\r\n                    </p>\r\n                    <p className=\"text-sm text-purple-600\">With AI Metrics</p>\r\n                  </div>\r\n                  <div className=\"text-center p-4 bg-orange-50 rounded-lg\">\r\n                    <Database className=\"h-8 w-8 text-orange-600 mx-auto mb-2\" />\r\n                    <p className=\"text-2xl font-bold text-orange-600\">\r\n                      {user?.isAnonymous ? 'Demo' : 'Live'}\r\n                    </p>\r\n                    <p className=\"text-sm text-orange-600\">Database Mode</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\r\n                  <h4 className=\"font-medium mb-2\">Database Status</h4>\r\n                  <div className=\"space-y-2 text-sm\">\r\n                    <div className=\"flex justify-between\">\r\n                      <span>Firebase Connection:</span>\r\n                      <Badge variant=\"default\">✅ Connected</Badge>\r\n                    </div>\r\n                    <div className=\"flex justify-between\">\r\n                      <span>Authentication:</span>\r\n                      <Badge variant={user ? 'default' : 'destructive'}>\r\n                        {user ? '✅ Active' : '❌ Not authenticated'}\r\n                      </Badge>\r\n                    </div>\r\n                    <div className=\"flex justify-between\">\r\n                      <span>Data Sync:</span>\r\n                      <Badge variant=\"default\">✅ Real-time</Badge>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n        </Tabs>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IACrD,MAAM,EAAE,QAAQ,EAAE,SAAS,eAAe,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,mBAAgB,AAAD;IAC9D,MAAM,EAAE,KAAK,EAAE,SAAS,YAAY,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,+BAA+B;IAC/F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,UAAU;QACd,cAAc,CAAA,OAAQ,OAAO;IAC/B;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAIpC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAS,SAAQ;;kDAChC,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAO5C,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIhC,6LAAC,mIAAA,CAAA,cAAW;sCACT,qBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAA6C,KAAK,GAAG;;;;;;;;;;;;kDAEpE,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAe,KAAK,KAAK,IAAI;;;;;;;;;;;;kDAE5C,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAe,KAAK,WAAW,IAAI;;;;;;;;;;;;kDAElD,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAS,KAAK,WAAW,GAAG,cAAc;0DAC9C,KAAK,WAAW,GAAG,cAAc;;;;;;;;;;;;;;;;;qDAKxC,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;8BAKnC,6LAAC,mIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAQ,WAAU;;sCACnC,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAQ,WAAU;;sDACnC,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;wCACd,MAAM,MAAM;wCAAC;;;;;;;8CAEjC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;;sDACtC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;wCACX,SAAS,MAAM;wCAAC;;;;;;;8CAEnC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,WAAU;;sDACvC,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAMrC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,mIAAA,CAAA,kBAAe;;oDAAC;oDAEd,8BAAgB,6LAAC;wDAAK,WAAU;kEAAO;;;;;;;;;;;;;;;;;;kDAG5C,6LAAC,mIAAA,CAAA,cAAW;kDACT,MAAM,MAAM,KAAK,kBAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;iEAK5C,6LAAC;4CAAI,WAAU;sDACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;oDAAkB,WAAU;;sEAC3B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;;gFAAc;gFAAO,QAAQ;;;;;;;sFAC3C,6LAAC;4EAAE,WAAU;sFAAmC,KAAK,EAAE;;;;;;;;;;;;8EAEzD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAW,KAAK,QAAQ;;;;;;sFACvC,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAS,KAAK,MAAM,KAAK,WAAW,YAAY;sFACpD,KAAK,MAAM;;;;;;;;;;;;;;;;;;sEAKlB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAA6B;;;;;;sFAC1C,6LAAC;4EAAE,WAAU;sFACV,KAAK,OAAO;;;;;;;;;;;;8EAGjB,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAA6B;;;;;;sFAC1C,6LAAC;4EAAE,WAAU;sFACV,MAAM,OAAO,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,QAAQ;;;;;;;;;;;;;;;;;;wDAK5E,CAAC,KAAK,YAAY,IAAI,KAAK,oBAAoB,mBAC9C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAC1C,6LAAC;oEAAI,WAAU;;wEACZ,KAAK,YAAY,kBAChB,6LAAC;;gFAAK;8FAAS,6LAAC;;wFAAQ,KAAK,YAAY;wFAAC;;;;;;;;;;;;;wEAE3C,KAAK,oBAAoB,kBACxB,6LAAC;;gFAAK;8FAAY,6LAAC;;wFAAQ,KAAK,oBAAoB;wFAAC;;;;;;;;;;;;;;;;;;;;;;;;;sEAM7D,6LAAC;4DAAI,WAAU;;gEAA2C;gEAC9C,IAAI,KAAK,KAAK,IAAI,EAAE,cAAc;;;;;;;;mDA5CtC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;sCAuD7B,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,mIAAA,CAAA,kBAAe;;oDAAC;oDAEd,iCAAmB,6LAAC;wDAAK,WAAU;kEAAO;;;;;;;;;;;;;;;;;;kDAG/C,6LAAC,mIAAA,CAAA,cAAW;kDACT,SAAS,MAAM,KAAK,kBACnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;iEAK5C,6LAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;oDAAqB,WAAU;;sEAC9B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAAe,QAAQ,YAAY;;;;;;sFACjD,6LAAC;4EAAE,WAAU;sFAAmC,QAAQ,EAAE;;;;;;;;;;;;8EAE5D,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAW,QAAQ,YAAY;;;;;;;;;;;;sEAGhD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAA6B;;;;;;sFAC1C,6LAAC;4EAAE,WAAU;sFACV,QAAQ,WAAW;;;;;;;;;;;;8EAGxB,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAA6B;;;;;;sFAC1C,6LAAC;4EAAE,WAAU;sFACV,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;wDAKtB,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBAC7C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEAA6B;wEAAW,QAAQ,QAAQ,CAAC,MAAM;wEAAC;;;;;;;8EAC7E,6LAAC;oEAAI,WAAU;;wEACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,oBAC1C,6LAAC,oIAAA,CAAA,QAAK;gFAAW,SAAQ;gFAAY,WAAU;0FAC5C,QAAQ,IAAI;+EADH;;;;;wEAIb,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;;gFAAU;gFACzC,QAAQ,QAAQ,CAAC,MAAM,GAAG;gFAAE;;;;;;;;;;;;;;;;;;;sEAOxC,6LAAC;4DAAI,WAAU;;gEAA2C;gEAC9C,IAAI,KAAK,QAAQ,SAAS,EAAE,cAAc;;;;;;;;mDA3C9C,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;sCAsDhC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEAAE,WAAU;0EAAoC,MAAM,MAAM;;;;;;0EAC7D,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEAAE,WAAU;0EAAqC,SAAS,MAAM;;;;;;0EACjE,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;kEAExC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEAAE,WAAU;0EACV,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,EAAE,MAAM;;;;;;0EAE3C,6LAAC;gEAAE,WAAU;0EAA0B;;;;;;;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEAAE,WAAU;0EACV,MAAM,cAAc,SAAS;;;;;;0EAEhC,6LAAC;gEAAE,WAAU;0EAA0B;;;;;;;;;;;;;;;;;;0DAI3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAU;;;;;;;;;;;;0EAE3B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAS,OAAO,YAAY;kFAChC,OAAO,aAAa;;;;;;;;;;;;0EAGzB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/C;GApTwB;;QACiB,0IAAA,CAAA,kBAAe;QACP,2IAAA,CAAA,mBAAgB;QACtB,4IAAA,CAAA,oBAAiB;;;KAHpC", "debugId": null}}]}