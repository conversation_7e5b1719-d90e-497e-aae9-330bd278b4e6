module.exports = {

"[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/ai/flows/generate-creative-asset.ts
__turbopack_context__.s({});
"use turbopack no side effects";
;
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/ai/flows/data:cc3707 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40515da45dbc1e56b768ff9d60386bd092ddf23aa4":"generateCreativeAsset"},"src/ai/flows/generate-creative-asset.ts",""] */ __turbopack_context__.s({
    "generateCreativeAsset": (()=>generateCreativeAsset)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateCreativeAsset = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("40515da45dbc1e56b768ff9d60386bd092ddf23aa4", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateCreativeAsset"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateCreativeAsset": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$data$3a$cc3707__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateCreativeAsset"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$data$3a$cc3707__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/data:cc3707 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateCreativeAsset": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateCreativeAsset"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <exports>");
}}),
"[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// This file must be bundled in the app's client layer, it shouldn't be directly
// imported by the server.
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    callServer: null,
    createServerReference: null,
    findSourceMapURL: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    callServer: function() {
        return _appcallserver.callServer;
    },
    createServerReference: function() {
        return createServerReference;
    },
    findSourceMapURL: function() {
        return _appfindsourcemapurl.findSourceMapURL;
    }
});
const _appcallserver = __turbopack_context__.r("[project]/node_modules/next/dist/client/app-call-server.js [app-ssr] (ecmascript)");
const _appfindsourcemapurl = __turbopack_context__.r("[project]/node_modules/next/dist/client/app-find-source-map-url.js [app-ssr] (ecmascript)");
const createServerReference = (("TURBOPACK compile-time truthy", 1) ? __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client-edge.js [app-ssr] (ecmascript)") : ("TURBOPACK unreachable", undefined)).createServerReference; //# sourceMappingURL=action-client-wrapper.js.map
}}),

};

//# sourceMappingURL=_2084dabe._.js.map