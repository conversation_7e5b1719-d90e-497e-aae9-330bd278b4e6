{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/icon.tsx"], "sourcesContent": ["import { ImageResponse } from 'next/og'\r\n \r\n// Route segment config\r\nexport const runtime = 'edge'\r\n \r\n// Image metadata\r\nexport const size = {\r\n  width: 32,\r\n  height: 32,\r\n}\r\nexport const contentType = 'image/png'\r\n \r\n// Image generation\r\nexport default function Icon() {\r\n  return new ImageResponse(\r\n    (\r\n      // ImageResponse JSX element\r\n      <div\r\n        style={{\r\n          fontSize: 24,\r\n          background: 'linear-gradient(90deg, #2563eb 0%, #3b82f6 100%)',\r\n          width: '100%',\r\n          height: '100%',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          color: 'white',\r\n          borderRadius: '6px',\r\n          fontWeight: 'bold',\r\n          fontFamily: 'system-ui, sans-serif',\r\n        }}\r\n      >\r\n        N\r\n      </div>\r\n    ),\r\n    // ImageResponse options\r\n    {\r\n      // For convenience, we can re-use the exported icons size metadata\r\n      // config to also set the ImageResponse's width and height.\r\n      ...size,\r\n    }\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;;AAGO,MAAM,UAAU;AAGhB,MAAM,OAAO;IAClB,OAAO;IACP,QAAQ;AACV;AACO,MAAM,cAAc;AAGZ,SAAS;IACtB,OAAO,IAAI,gLAAA,CAAA,gBAAa,CAEpB,4BAA4B;kBAC5B,qNAAC;QACC,OAAO;YACL,UAAU;YACV,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,cAAc;YACd,YAAY;YACZ,YAAY;QACd;kBACD;;;;;cAIH,wBAAwB;IACxB;QACE,kEAAkE;QAClE,2DAA2D;QAC3D,GAAG,IAAI;IACT;AAEJ"}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/icon--route-entry.js"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport * as _imageModule from \"./icon.tsx\"\n\nconst imageModule = { ..._imageModule }\n\nconst handler = imageModule.default\nconst generateImageMetadata = imageModule.generateImageMetadata\n\nif (typeof handler !== 'function') {\n    throw new Error('Default export is missing in \"./icon.tsx\"')\n}\n\nexport async function GET(_, ctx) {\n    const params = await ctx.params\n    const { __metadata_id__, ...rest } = params || {}\n    const restParams = params ? rest : undefined\n    const targetId = __metadata_id__\n    let id = undefined\n\n    if (generateImageMetadata) {\n        const imageMetadata = await generateImageMetadata({ params: restParams })\n        id = imageMetadata.find((item) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if (item?.id == null) {\n                    throw new Error('id property is required for every item returned from generateImageMetadata')\n                }\n            }\n            return item.id.toString() === targetId\n        })?.id\n\n        if (id == null) {\n            return new NextResponse('Not Found', {\n                status: 404,\n            })\n        }\n    }\n\n    return handler({ params: restParams, id })\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA,MAAM,cAAc;IAAE,GAAG,2HAAY;AAAC;AAEtC,MAAM,UAAU,YAAY,OAAO;AACnC,MAAM,wBAAwB,YAAY,qBAAqB;AAE/D,IAAI,OAAO,YAAY,YAAY;IAC/B,MAAM,IAAI,MAAM;AACpB;AAEO,eAAe,IAAI,CAAC,EAAE,GAAG;IAC5B,MAAM,SAAS,MAAM,IAAI,MAAM;IAC/B,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,GAAG,UAAU,CAAC;IAChD,MAAM,aAAa,SAAS,OAAO;IACnC,MAAM,WAAW;IACjB,IAAI,KAAK;IAET,IAAI,uBAAuB;QACvB,MAAM,gBAAgB,MAAM,sBAAsB;YAAE,QAAQ;QAAW;QACvE,KAAK,cAAc,IAAI,CAAC,CAAC;YACrB,wCAA2C;gBACvC,IAAI,MAAM,MAAM,MAAM;oBAClB,MAAM,IAAI,MAAM;gBACpB;YACJ;YACA,OAAO,KAAK,EAAE,CAAC,QAAQ,OAAO;QAClC,IAAI;QAEJ,IAAI,MAAM,MAAM;YACZ,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,aAAa;gBACjC,QAAQ;YACZ;QACJ;IACJ;IAEA,OAAO,QAAQ;QAAE,QAAQ;QAAY;IAAG;AAC5C"}}]}