{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/flows/generate-creative-asset.ts"], "sourcesContent": ["\r\n// src/ai/flows/generate-creative-asset.ts\r\n'use server';\r\n\r\n/**\r\n * @fileOverview A Genkit flow for generating a creative asset (image or video)\r\n * based on a user's prompt, an optional reference image, and brand profile settings.\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\nimport type { BrandProfile } from '@/lib/types';\r\nimport { MediaPart } from 'genkit';\r\nimport { GenerateRequest } from 'genkit/generate';\r\nimport {\r\n    ADVANCED_DESIGN_PRINCIPLES,\r\n    PLATFORM_SPECIFIC_GUIDELINES,\r\n    BUSINESS_TYPE_DESIGN_DNA,\r\n    QUALITY_ENHANCEMENT_INSTRUCTIONS\r\n} from '@/ai/prompts/advanced-design-prompts';\r\nimport {\r\n    analyzeDesignExample,\r\n    selectOptimalDesignExamples,\r\n    extractDesignDNA,\r\n    type DesignAnalysis\r\n} from '@/ai/utils/design-analysis';\r\nimport {\r\n    assessDesignQuality,\r\n    generateImprovementPrompt,\r\n    meetsQualityStandards,\r\n    type DesignQuality\r\n} from '@/ai/utils/design-quality';\r\n\r\n// Define the input schema for the creative asset generation flow.\r\nconst CreativeAssetInputSchema = z.object({\r\n    prompt: z.string().describe('The main text prompt describing the desired asset.'),\r\n    outputType: z.enum(['image', 'video']).describe('The type of asset to generate.'),\r\n    referenceAssetUrl: z.string().nullable().describe('An optional reference image or video as a data URI.'),\r\n    useBrandProfile: z.boolean().describe('Whether to apply the brand profile.'),\r\n    brandProfile: z.custom<BrandProfile>().nullable().describe('The brand profile object.'),\r\n    maskDataUrl: z.string().nullable().optional().describe('An optional mask image for inpainting as a data URI.'),\r\n    aspectRatio: z.enum(['16:9', '9:16']).optional().describe('The aspect ratio for video generation.'),\r\n});\r\nexport type CreativeAssetInput = z.infer<typeof CreativeAssetInputSchema>;\r\n\r\n// Define the output schema for the creative asset generation flow.\r\nconst CreativeAssetOutputSchema = z.object({\r\n    imageUrl: z.string().nullable().describe('The data URI of the generated image, if applicable.'),\r\n    videoUrl: z.string().nullable().describe('The data URI of the generated video, if applicable.'),\r\n    aiExplanation: z.string().describe('A brief explanation from the AI about what it created.'),\r\n});\r\nexport type CreativeAsset = z.infer<typeof CreativeAssetOutputSchema>;\r\n\r\n/**\r\n * An exported wrapper function that calls the creative asset generation flow.\r\n * @param input - The input data for asset generation.\r\n * @returns A promise that resolves to the generated asset details.\r\n */\r\nexport async function generateCreativeAsset(input: CreativeAssetInput): Promise<CreativeAsset> {\r\n    return generateCreativeAssetFlow(input);\r\n}\r\n\r\n\r\n/**\r\n * Helper function to download video and convert to data URI\r\n */\r\nasync function videoToDataURI(videoPart: MediaPart): Promise<string> {\r\n    if (!videoPart.media || !videoPart.media.url) {\r\n        throw new Error('Media URL not found in video part.');\r\n    }\r\n\r\n    const fetch = (await import('node-fetch')).default;\r\n    const videoDownloadResponse = await fetch(\r\n        `${videoPart.media.url}&key=${process.env.GEMINI_API_KEY}`\r\n    );\r\n\r\n    if (!videoDownloadResponse.ok) {\r\n        throw new Error(`Failed to download video: ${videoDownloadResponse.statusText}`);\r\n    }\r\n\r\n    const videoBuffer = await videoDownloadResponse.arrayBuffer();\r\n    const base64Video = Buffer.from(videoBuffer).toString('base64');\r\n    const contentType = videoPart.media.contentType || 'video/mp4';\r\n\r\n    return `data:${contentType};base64,${base64Video}`;\r\n}\r\n\r\n/**\r\n * Extracts text in quotes and the remaining prompt.\r\n */\r\nconst extractQuotedText = (prompt: string): { imageText: string | null; remainingPrompt: string } => {\r\n    const quoteRegex = /\"([^\"]*)\"/;\r\n    const match = prompt.match(quoteRegex);\r\n    if (match) {\r\n        return {\r\n            imageText: match[1],\r\n            remainingPrompt: prompt.replace(quoteRegex, '').trim()\r\n        };\r\n    }\r\n    return {\r\n        imageText: null,\r\n        remainingPrompt: prompt\r\n    };\r\n};\r\n\r\n/**\r\n * Wraps ai.generate with retry logic for 503 errors.\r\n */\r\nasync function generateWithRetry(request: GenerateRequest, retries = 3, delay = 1000) {\r\n    for (let i = 0; i < retries; i++) {\r\n        try {\r\n            const result = await ai.generate(request);\r\n            return result;\r\n        } catch (e: any) {\r\n            if (e.message && e.message.includes('503') && i < retries - 1) {\r\n                console.log(`Attempt ${i + 1} failed with 503. Retrying in ${delay}ms...`);\r\n                await new Promise(resolve => setTimeout(resolve, delay));\r\n            } else {\r\n                if (e.message && e.message.includes('503')) {\r\n                    throw new Error(\"The AI model is currently overloaded. Please try again in a few moments.\");\r\n                }\r\n                if (e.message && e.message.includes('429')) {\r\n                    throw new Error(\"You've exceeded your request limit for the AI model. Please check your plan or try again later.\");\r\n                }\r\n                throw e; // Rethrow other errors immediately\r\n            }\r\n        }\r\n    }\r\n    // This line should not be reachable if retries are configured, but as a fallback:\r\n    throw new Error(\"The AI model is currently overloaded after multiple retries. Please try again later.\");\r\n}\r\n\r\nconst getMimeTypeFromDataURI = (dataURI: string): string => {\r\n    const match = dataURI.match(/^data:(.*?);/);\r\n    return match ? match[1] : 'application/octet-stream'; // Default if no match\r\n};\r\n\r\n\r\n/**\r\n * The core Genkit flow for generating a creative asset.\r\n */\r\nconst generateCreativeAssetFlow = ai.defineFlow(\r\n    {\r\n        name: 'generateCreativeAssetFlow',\r\n        inputSchema: CreativeAssetInputSchema,\r\n        outputSchema: CreativeAssetOutputSchema,\r\n    },\r\n    async (input) => {\r\n        const promptParts: (string | { text: string } | { media: { url: string; contentType?: string } })[] = [];\r\n        let textPrompt = '';\r\n\r\n        const { imageText, remainingPrompt } = extractQuotedText(input.prompt);\r\n\r\n        if (input.maskDataUrl && input.referenceAssetUrl) {\r\n            // This is an inpainting request.\r\n            textPrompt = `You are an expert image editor performing a precise inpainting task.\r\nYou will be given an original image, a mask, and a text prompt.\r\nYour task is to modify the original image *only* in the areas designated by the black region of the mask.\r\nThe rest of the image must remain absolutely unchanged.\r\nIf the prompt is a \"remove\" or \"delete\" instruction, perform a seamless, content-aware fill to replace the masked object with a photorealistic background that matches the surrounding area.\r\nThe user's instruction for the masked area is: \"${remainingPrompt}\".\r\nRecreate the content within the black-masked region based on this instruction, ensuring a seamless and photorealistic blend with the surrounding, untouched areas of the image.`;\r\n\r\n            promptParts.push({ text: textPrompt });\r\n            promptParts.push({ media: { url: input.referenceAssetUrl, contentType: getMimeTypeFromDataURI(input.referenceAssetUrl) } });\r\n            promptParts.push({ media: { url: input.maskDataUrl, contentType: getMimeTypeFromDataURI(input.maskDataUrl) } });\r\n\r\n        } else if (input.referenceAssetUrl) {\r\n            // This is a generation prompt with a reference asset (image or video).\r\n            let referencePrompt = `You are an expert creative director specializing in high-end advertisements. You will be given a reference asset and a text prompt with instructions.\r\nYour task is to generate a new asset that is inspired by the reference asset and follows the new instructions.\r\n\r\nYour primary goal is to intelligently interpret the user's request, considering the provided reference asset. Do not just copy the reference.\r\nAnalyze the user's prompt for common editing terminology and apply it creatively. For example:\r\n- If asked to \"change the background,\" intelligently isolate the main subject and replace the background with a new one that matches the prompt, preserving the foreground subject.\r\n- If asked to \"make the logo bigger\" or \"change the text color,\" perform those specific edits while maintaining the overall composition.\r\n- If the prompt is more general, use the reference asset for style, color, and subject inspiration to create a new, distinct asset.\r\n\r\nThe user's instruction is: \"${remainingPrompt}\"`;\r\n\r\n            if (imageText) {\r\n                referencePrompt += `\\n\\n**Explicit Text Overlay:** The user has provided specific text in quotes: \"${imageText}\". You MUST overlay this text on the image. If there was existing text, replace it. Ensure the new text is readable and well-composed.`\r\n            }\r\n\r\n            if (input.outputType === 'video') {\r\n                referencePrompt += `\\n\\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;\r\n                if (imageText) {\r\n                    referencePrompt += `\\n\\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: \"${imageText}\". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;\r\n                }\r\n            }\r\n\r\n            if (input.useBrandProfile && input.brandProfile) {\r\n                const bp = input.brandProfile;\r\n                let brandGuidelines = '\\n\\n**Brand Guidelines:**';\r\n\r\n                if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    promptParts.push({ media: { url: bp.logoDataUrl, contentType: getMimeTypeFromDataURI(bp.logoDataUrl) } });\r\n                    brandGuidelines += ` A logo has also been provided. Integrate it naturally into the new design.`\r\n                } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    brandGuidelines += ` Create a design that represents the brand identity (SVG logo format not supported by AI model).`\r\n                }\r\n                referencePrompt += brandGuidelines;\r\n            }\r\n\r\n            textPrompt = referencePrompt;\r\n            if (textPrompt) {\r\n                promptParts.push({ text: textPrompt });\r\n            }\r\n            promptParts.push({ media: { url: input.referenceAssetUrl, contentType: getMimeTypeFromDataURI(input.referenceAssetUrl) } });\r\n\r\n        } else if (input.useBrandProfile && input.brandProfile) {\r\n            // This is a new, on-brand asset generation with advanced design principles.\r\n            const bp = input.brandProfile;\r\n\r\n            // Get business-specific design DNA\r\n            const businessDNA = BUSINESS_TYPE_DESIGN_DNA[bp.businessType as keyof typeof BUSINESS_TYPE_DESIGN_DNA] || BUSINESS_TYPE_DESIGN_DNA.default;\r\n\r\n            let onBrandPrompt = `Create a stunning, professional social media ${input.outputType} for ${bp.businessName || 'this business'}.\r\n\r\nBUSINESS: ${bp.businessName || 'Professional Business'} (${bp.businessType})\r\nCONTENT: \"${remainingPrompt}\"\r\nSTYLE: ${bp.visualStyle}, modern, clean, professional\r\n\r\nFORMAT: ${input.aspectRatio ? `${input.aspectRatio} aspect ratio` : 'Square 1:1 format'}\r\n\r\nBRAND COLORS (use prominently):\r\n${bp.primaryColor ? `- Primary: ${bp.primaryColor}` : ''}\r\n${bp.accentColor ? `- Accent: ${bp.accentColor}` : ''}\r\n${bp.backgroundColor ? `- Background: ${bp.backgroundColor}` : ''}\r\n\r\nREQUIREMENTS:\r\n- High-quality, professional design\r\n- ${bp.visualStyle} aesthetic\r\n- Clean, modern layout\r\n- Perfect for ${bp.businessType} business\r\n- Brand colors prominently featured\r\n- Professional social media appearance`;\r\n\r\n            // Intelligent design examples processing\r\n            let designDNA = '';\r\n            let selectedExamples: string[] = [];\r\n\r\n            if (bp.designExamples && bp.designExamples.length > 0) {\r\n                try {\r\n                    // Analyze design examples for intelligent processing\r\n                    const analyses: DesignAnalysis[] = [];\r\n                    for (const example of bp.designExamples.slice(0, 3)) { // Limit for performance\r\n                        try {\r\n                            const analysis = await analyzeDesignExample(\r\n                                example,\r\n                                bp.businessType,\r\n                                'creative-studio',\r\n                                `${bp.visualStyle} ${input.outputType} for ${remainingPrompt}`\r\n                            );\r\n                            analyses.push(analysis);\r\n                        } catch (error) {\r\n                            console.warn('Design analysis failed for example, skipping:', error);\r\n                        }\r\n                    }\r\n\r\n                    if (analyses.length > 0) {\r\n                        // Extract design DNA from analyzed examples\r\n                        designDNA = extractDesignDNA(analyses);\r\n\r\n                        // Select optimal examples based on analysis\r\n                        selectedExamples = selectOptimalDesignExamples(\r\n                            bp.designExamples,\r\n                            analyses,\r\n                            remainingPrompt,\r\n                            'creative-studio',\r\n                            2\r\n                        );\r\n                    } else {\r\n                        selectedExamples = bp.designExamples.slice(0, 2);\r\n                    }\r\n                } catch (error) {\r\n                    console.warn('Design analysis system failed, using fallback:', error);\r\n                    selectedExamples = bp.designExamples.slice(0, 2);\r\n                }\r\n\r\n                onBrandPrompt += `\\n**STYLE REFERENCE:**\r\nUse the provided design examples as style reference to create a similar visual aesthetic, color scheme, typography, and overall design approach. Match the style, mood, and visual characteristics of the reference designs while creating new content.\r\n\r\n${designDNA}`;\r\n            }\r\n\r\n            if (input.outputType === 'image') {\r\n                onBrandPrompt += `\\n- **Text Overlay Requirements:** ${imageText ? `\r\n                  * Display this EXACT text: \"${imageText}\"\r\n                  * Use ENGLISH ONLY - no foreign languages, symbols, or corrupted characters\r\n                  * Make text LARGE and BOLD for mobile readability\r\n                  * Apply high contrast (minimum 4.5:1 ratio) between text and background\r\n                  * Add text shadows, outlines, or semi-transparent backgrounds for readability\r\n                  * Position text using rule of thirds for optimal composition\r\n                  * Ensure text is the primary focal point of the design` : 'No text should be added to the asset.'}`;\r\n                onBrandPrompt += `\\n- **Logo Placement:** The provided logo must be integrated naturally into the design (e.g., on a product, a sign, or as a subtle watermark).`;\r\n                onBrandPrompt += `\\n- **Critical Language Rule:** ALL text must be in clear, readable ENGLISH only. Never use foreign languages, corrupted text, or unreadable symbols.`;\r\n\r\n                if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    promptParts.push({ media: { url: bp.logoDataUrl, contentType: getMimeTypeFromDataURI(bp.logoDataUrl) } });\r\n                }\r\n                textPrompt = onBrandPrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            } else { // Video\r\n                onBrandPrompt += `\\n- **Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;\r\n                if (input.aspectRatio === '16:9') {\r\n                    onBrandPrompt += ' The video should have relevant sound.';\r\n                }\r\n                if (imageText) {\r\n                    onBrandPrompt += `\\n- **Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: \"${imageText}\". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`\r\n                }\r\n                if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    onBrandPrompt += `\\n- **Logo Placement:** The provided logo must be integrated naturally into the design.`;\r\n                    promptParts.push({ media: { url: bp.logoDataUrl, contentType: getMimeTypeFromDataURI(bp.logoDataUrl) } });\r\n                } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    onBrandPrompt += `\\n- **Brand Identity:** Create a design that represents the brand identity and style.`;\r\n                }\r\n\r\n                // Add selected design examples as reference\r\n                selectedExamples.forEach(designExample => {\r\n                    promptParts.push({ media: { url: designExample, contentType: getMimeTypeFromDataURI(designExample) } });\r\n                });\r\n\r\n                textPrompt = onBrandPrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            }\r\n        } else {\r\n            // This is a new, un-branded, creative prompt.\r\n            let creativePrompt = `You are an expert creative director specializing in high-end advertisements. Generate a compelling, high-quality social media advertisement ${input.outputType} based on the following instruction: \"${remainingPrompt}\".\r\n\r\n⚡ GEMINI 2.0 FLASH HD QUALITY ENHANCEMENTS:\r\n- MAXIMUM RESOLUTION: Ultra-high definition rendering (4K+ quality)\r\n- SMALL FONT SIZE EXCELLENCE: Perfect rendering at 8pt, 10pt, 12pt, and all small font sizes\r\n- TINY TEXT PRECISION: Every character sharp and legible even when font size is very small\r\n- HIGH-DPI SMALL TEXT: Render small fonts as if on 300+ DPI display for maximum sharpness\r\n- PERFECT ANATOMY: Complete, symmetrical faces with natural expressions\r\n- SHARP DETAILS: Crystal-clear textures, no blur or artifacts\r\n- PROFESSIONAL LIGHTING: Studio-quality lighting with proper shadows\r\n- PREMIUM COMPOSITION: Golden ratio layouts with perfect balance\r\n- ADVANCED COLOR THEORY: Perfect contrast ratios (7:1 minimum) with vibrant, accurate colors`;\r\n\r\n            if (input.outputType === 'image' && imageText) {\r\n                creativePrompt += `\r\n\r\n🚨🚨🚨 EMERGENCY OVERRIDE - CRITICAL TEXT CONTROL 🚨🚨🚨\r\n\r\n⛔ ABSOLUTE PROHIBITION - NO EXCEPTIONS:\r\n- NEVER add \"Flex Your Finances\" or any financial terms\r\n- NEVER add \"Payroll Banking Simplified\" or banking phrases\r\n- NEVER add \"Banking Made Easy\" or similar taglines\r\n- NEVER add company descriptions or service explanations\r\n- NEVER add marketing copy or promotional text\r\n- NEVER add placeholder text or sample content\r\n- NEVER create fake headlines or taglines\r\n- NEVER add descriptive text about the business\r\n- NEVER add ANY text except what is specified below\r\n\r\n🎯 ONLY THIS TEXT IS ALLOWED: \"${imageText}\"\r\n🎯 REPEAT: ONLY THIS TEXT: \"${imageText}\"\r\n🎯 NO OTHER TEXT PERMITTED: \"${imageText}\"\r\n\r\n🌍 ENGLISH ONLY REQUIREMENT:\r\n- ALL text must be in clear, readable English\r\n- NO foreign languages (Arabic, Chinese, Hindi, etc.)\r\n- NO special characters, symbols, or corrupted text\r\n- NO accents or diacritical marks\r\n\r\nOverlay ONLY the following text onto the asset: \"${imageText}\".\r\nDO NOT ADD ANY OTHER TEXT.\r\nEnsure the text is readable and well-composed.`\r\n                textPrompt = creativePrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            } else { // Video\r\n                creativePrompt += `\\n\\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;\r\n                if (input.aspectRatio === '16:9') {\r\n                    creativePrompt += ' The video should have relevant sound.';\r\n                }\r\n                if (imageText) {\r\n                    creativePrompt += `\\n\\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: \"${imageText}\". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;\r\n                }\r\n                textPrompt = creativePrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            }\r\n        }\r\n\r\n        const aiExplanationPrompt = ai.definePrompt({\r\n            name: 'creativeAssetExplanationPrompt',\r\n            prompt: `Based on the generated ${input.outputType}, write a very brief, one-sentence explanation of the creative choices made. For example: \"I created a modern, vibrant image of a coffee shop, using your brand's primary color for the logo.\"`\r\n        });\r\n\r\n        const explanationResult = await aiExplanationPrompt();\r\n\r\n        try {\r\n            if (input.outputType === 'image') {\r\n                // Generate image with quality validation\r\n                let finalImageUrl: string | null = null;\r\n                let attempts = 0;\r\n                const maxAttempts = 2;\r\n\r\n                while (attempts < maxAttempts && !finalImageUrl) {\r\n                    attempts++;\r\n\r\n                    const { media } = await generateWithRetry({\r\n                        model: 'googleai/gemini-2.5-flash-preview',\r\n                        prompt: promptParts,\r\n                        config: {\r\n                            responseModalities: ['TEXT', 'IMAGE'],\r\n                        },\r\n                    });\r\n\r\n                    let imageUrl = media?.url ?? null;\r\n                    if (!imageUrl) {\r\n                        if (attempts === maxAttempts) {\r\n                            throw new Error('Failed to generate image');\r\n                        }\r\n                        continue;\r\n                    }\r\n\r\n                    // Apply aspect ratio correction if needed\r\n                    if (input.aspectRatio && input.aspectRatio !== '1:1') {\r\n                        console.log(`🖼️ Applying aspect ratio correction for ${input.aspectRatio}...`);\r\n                        try {\r\n                            const { cropImageFromUrl } = await import('@/lib/image-processing');\r\n                            // Map aspect ratio to platform for cropping\r\n                            const platformForCropping = input.aspectRatio === '16:9' ? 'linkedin' :\r\n                                input.aspectRatio === '9:16' ? 'story' : 'instagram';\r\n                            imageUrl = await cropImageFromUrl(imageUrl, platformForCropping);\r\n                            console.log(`✅ Image cropped successfully for ${input.aspectRatio}`);\r\n                        } catch (cropError) {\r\n                            console.warn('⚠️ Image cropping failed, using original:', cropError);\r\n                            // Continue with original image if cropping fails\r\n                        }\r\n                    }\r\n\r\n                    // Quality validation for brand profile designs\r\n                    if (input.useBrandProfile && input.brandProfile && attempts === 1) {\r\n                        try {\r\n                            const quality = await assessDesignQuality(\r\n                                imageUrl,\r\n                                input.brandProfile.businessType,\r\n                                'creative-studio',\r\n                                input.brandProfile.visualStyle,\r\n                                undefined,\r\n                                `Creative asset: ${remainingPrompt}`\r\n                            );\r\n\r\n                            // If quality is acceptable, use this design\r\n                            if (meetsQualityStandards(quality, 6)) { // Slightly lower threshold for creative assets\r\n                                finalImageUrl = imageUrl;\r\n                                break;\r\n                            }\r\n\r\n                            // If quality is poor and we have attempts left, try to improve\r\n                            if (attempts < maxAttempts) {\r\n                                console.log(`Creative asset quality score: ${quality.overall.score}/10. Attempting improvement...`);\r\n\r\n                                // Add improvement instructions to prompt\r\n                                const improvementInstructions = generateImprovementPrompt(quality);\r\n                                const improvedPrompt = `${promptParts[0].text}\\n\\n${improvementInstructions}`;\r\n                                promptParts[0] = { text: improvedPrompt };\r\n                                continue;\r\n                            } else {\r\n                                finalImageUrl = imageUrl;\r\n                                break;\r\n                            }\r\n                        } catch (qualityError) {\r\n                            console.warn('Quality assessment failed for creative asset, using generated design:', qualityError);\r\n                            finalImageUrl = imageUrl;\r\n                            break;\r\n                        }\r\n                    } else {\r\n                        finalImageUrl = imageUrl;\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                return {\r\n                    imageUrl: finalImageUrl,\r\n                    videoUrl: null,\r\n                    aiExplanation: explanationResult.output ?? \"Here is the generated image based on your prompt.\"\r\n                };\r\n            } else { // Video generation\r\n                const isVertical = input.aspectRatio === '9:16';\r\n\r\n                const model = isVertical ? 'googleai/veo-2.0-generate-001' : 'googleai/veo-3.0-generate-preview';\r\n                const config: Record<string, any> = {};\r\n                if (isVertical) {\r\n                    config.aspectRatio = '9:16';\r\n                    config.durationSeconds = 8;\r\n                }\r\n\r\n                const result = await generateWithRetry({\r\n                    model,\r\n                    prompt: promptParts,\r\n                    config,\r\n                });\r\n\r\n                let operation = result.operation;\r\n\r\n                if (!operation) {\r\n                    throw new Error('The video generation process did not start correctly. Please try again.');\r\n                }\r\n\r\n                // Poll for completion\r\n                while (!operation.done) {\r\n                    await new Promise(resolve => setTimeout(resolve, 5000)); // wait 5s\r\n                    operation = await ai.checkOperation(operation);\r\n                }\r\n\r\n                if (operation.error) {\r\n                    console.error(\"Video generation operation failed\", operation.error);\r\n                    throw new Error(`Video generation failed: ${operation.error.message}. Please try again.`);\r\n                }\r\n\r\n                const videoPart = operation.output?.message?.content.find(p => !!p.media);\r\n                if (!videoPart || !videoPart.media) {\r\n                    throw new Error('Video generation completed, but the final video file could not be found.');\r\n                }\r\n\r\n                const videoDataUrl = await videoToDataURI(videoPart);\r\n\r\n                return {\r\n                    imageUrl: null,\r\n                    videoUrl: videoDataUrl,\r\n                    aiExplanation: explanationResult.output ?? \"Here is the generated video based on your prompt.\"\r\n                };\r\n            }\r\n        } catch (e: any) {\r\n            console.error(\"Error during creative asset generation:\", e);\r\n            // Ensure a user-friendly error is thrown\r\n            const message = e.message || \"An unknown error occurred during asset generation.\";\r\n            throw new Error(message);\r\n        }\r\n    }\r\n);\r\n\r\n\r\n"], "names": [], "mappings": "AACA,0CAA0C", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/flows/generate-creative-asset.ts"], "sourcesContent": ["\r\n// src/ai/flows/generate-creative-asset.ts\r\n'use server';\r\n\r\n/**\r\n * @fileOverview A Genkit flow for generating a creative asset (image or video)\r\n * based on a user's prompt, an optional reference image, and brand profile settings.\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\nimport type { BrandProfile } from '@/lib/types';\r\nimport { MediaPart } from 'genkit';\r\nimport { GenerateRequest } from 'genkit/generate';\r\nimport {\r\n    ADVANCED_DESIGN_PRINCIPLES,\r\n    PLATFORM_SPECIFIC_GUIDELINES,\r\n    BUSINESS_TYPE_DESIGN_DNA,\r\n    QUALITY_ENHANCEMENT_INSTRUCTIONS\r\n} from '@/ai/prompts/advanced-design-prompts';\r\nimport {\r\n    analyzeDesignExample,\r\n    selectOptimalDesignExamples,\r\n    extractDesignDNA,\r\n    type DesignAnalysis\r\n} from '@/ai/utils/design-analysis';\r\nimport {\r\n    assessDesignQuality,\r\n    generateImprovementPrompt,\r\n    meetsQualityStandards,\r\n    type DesignQuality\r\n} from '@/ai/utils/design-quality';\r\n\r\n// Define the input schema for the creative asset generation flow.\r\nconst CreativeAssetInputSchema = z.object({\r\n    prompt: z.string().describe('The main text prompt describing the desired asset.'),\r\n    outputType: z.enum(['image', 'video']).describe('The type of asset to generate.'),\r\n    referenceAssetUrl: z.string().nullable().describe('An optional reference image or video as a data URI.'),\r\n    useBrandProfile: z.boolean().describe('Whether to apply the brand profile.'),\r\n    brandProfile: z.custom<BrandProfile>().nullable().describe('The brand profile object.'),\r\n    maskDataUrl: z.string().nullable().optional().describe('An optional mask image for inpainting as a data URI.'),\r\n    aspectRatio: z.enum(['16:9', '9:16']).optional().describe('The aspect ratio for video generation.'),\r\n});\r\nexport type CreativeAssetInput = z.infer<typeof CreativeAssetInputSchema>;\r\n\r\n// Define the output schema for the creative asset generation flow.\r\nconst CreativeAssetOutputSchema = z.object({\r\n    imageUrl: z.string().nullable().describe('The data URI of the generated image, if applicable.'),\r\n    videoUrl: z.string().nullable().describe('The data URI of the generated video, if applicable.'),\r\n    aiExplanation: z.string().describe('A brief explanation from the AI about what it created.'),\r\n});\r\nexport type CreativeAsset = z.infer<typeof CreativeAssetOutputSchema>;\r\n\r\n/**\r\n * An exported wrapper function that calls the creative asset generation flow.\r\n * @param input - The input data for asset generation.\r\n * @returns A promise that resolves to the generated asset details.\r\n */\r\nexport async function generateCreativeAsset(input: CreativeAssetInput): Promise<CreativeAsset> {\r\n    return generateCreativeAssetFlow(input);\r\n}\r\n\r\n\r\n/**\r\n * Helper function to download video and convert to data URI\r\n */\r\nasync function videoToDataURI(videoPart: MediaPart): Promise<string> {\r\n    if (!videoPart.media || !videoPart.media.url) {\r\n        throw new Error('Media URL not found in video part.');\r\n    }\r\n\r\n    const fetch = (await import('node-fetch')).default;\r\n    const videoDownloadResponse = await fetch(\r\n        `${videoPart.media.url}&key=${process.env.GEMINI_API_KEY}`\r\n    );\r\n\r\n    if (!videoDownloadResponse.ok) {\r\n        throw new Error(`Failed to download video: ${videoDownloadResponse.statusText}`);\r\n    }\r\n\r\n    const videoBuffer = await videoDownloadResponse.arrayBuffer();\r\n    const base64Video = Buffer.from(videoBuffer).toString('base64');\r\n    const contentType = videoPart.media.contentType || 'video/mp4';\r\n\r\n    return `data:${contentType};base64,${base64Video}`;\r\n}\r\n\r\n/**\r\n * Extracts text in quotes and the remaining prompt.\r\n */\r\nconst extractQuotedText = (prompt: string): { imageText: string | null; remainingPrompt: string } => {\r\n    const quoteRegex = /\"([^\"]*)\"/;\r\n    const match = prompt.match(quoteRegex);\r\n    if (match) {\r\n        return {\r\n            imageText: match[1],\r\n            remainingPrompt: prompt.replace(quoteRegex, '').trim()\r\n        };\r\n    }\r\n    return {\r\n        imageText: null,\r\n        remainingPrompt: prompt\r\n    };\r\n};\r\n\r\n/**\r\n * Wraps ai.generate with retry logic for 503 errors.\r\n */\r\nasync function generateWithRetry(request: GenerateRequest, retries = 3, delay = 1000) {\r\n    for (let i = 0; i < retries; i++) {\r\n        try {\r\n            const result = await ai.generate(request);\r\n            return result;\r\n        } catch (e: any) {\r\n            if (e.message && e.message.includes('503') && i < retries - 1) {\r\n                console.log(`Attempt ${i + 1} failed with 503. Retrying in ${delay}ms...`);\r\n                await new Promise(resolve => setTimeout(resolve, delay));\r\n            } else {\r\n                if (e.message && e.message.includes('503')) {\r\n                    throw new Error(\"The AI model is currently overloaded. Please try again in a few moments.\");\r\n                }\r\n                if (e.message && e.message.includes('429')) {\r\n                    throw new Error(\"You've exceeded your request limit for the AI model. Please check your plan or try again later.\");\r\n                }\r\n                throw e; // Rethrow other errors immediately\r\n            }\r\n        }\r\n    }\r\n    // This line should not be reachable if retries are configured, but as a fallback:\r\n    throw new Error(\"The AI model is currently overloaded after multiple retries. Please try again later.\");\r\n}\r\n\r\nconst getMimeTypeFromDataURI = (dataURI: string): string => {\r\n    const match = dataURI.match(/^data:(.*?);/);\r\n    return match ? match[1] : 'application/octet-stream'; // Default if no match\r\n};\r\n\r\n\r\n/**\r\n * The core Genkit flow for generating a creative asset.\r\n */\r\nconst generateCreativeAssetFlow = ai.defineFlow(\r\n    {\r\n        name: 'generateCreativeAssetFlow',\r\n        inputSchema: CreativeAssetInputSchema,\r\n        outputSchema: CreativeAssetOutputSchema,\r\n    },\r\n    async (input) => {\r\n        const promptParts: (string | { text: string } | { media: { url: string; contentType?: string } })[] = [];\r\n        let textPrompt = '';\r\n\r\n        const { imageText, remainingPrompt } = extractQuotedText(input.prompt);\r\n\r\n        if (input.maskDataUrl && input.referenceAssetUrl) {\r\n            // This is an inpainting request.\r\n            textPrompt = `You are an expert image editor performing a precise inpainting task.\r\nYou will be given an original image, a mask, and a text prompt.\r\nYour task is to modify the original image *only* in the areas designated by the black region of the mask.\r\nThe rest of the image must remain absolutely unchanged.\r\nIf the prompt is a \"remove\" or \"delete\" instruction, perform a seamless, content-aware fill to replace the masked object with a photorealistic background that matches the surrounding area.\r\nThe user's instruction for the masked area is: \"${remainingPrompt}\".\r\nRecreate the content within the black-masked region based on this instruction, ensuring a seamless and photorealistic blend with the surrounding, untouched areas of the image.`;\r\n\r\n            promptParts.push({ text: textPrompt });\r\n            promptParts.push({ media: { url: input.referenceAssetUrl, contentType: getMimeTypeFromDataURI(input.referenceAssetUrl) } });\r\n            promptParts.push({ media: { url: input.maskDataUrl, contentType: getMimeTypeFromDataURI(input.maskDataUrl) } });\r\n\r\n        } else if (input.referenceAssetUrl) {\r\n            // This is a generation prompt with a reference asset (image or video).\r\n            let referencePrompt = `You are an expert creative director specializing in high-end advertisements. You will be given a reference asset and a text prompt with instructions.\r\nYour task is to generate a new asset that is inspired by the reference asset and follows the new instructions.\r\n\r\nYour primary goal is to intelligently interpret the user's request, considering the provided reference asset. Do not just copy the reference.\r\nAnalyze the user's prompt for common editing terminology and apply it creatively. For example:\r\n- If asked to \"change the background,\" intelligently isolate the main subject and replace the background with a new one that matches the prompt, preserving the foreground subject.\r\n- If asked to \"make the logo bigger\" or \"change the text color,\" perform those specific edits while maintaining the overall composition.\r\n- If the prompt is more general, use the reference asset for style, color, and subject inspiration to create a new, distinct asset.\r\n\r\nThe user's instruction is: \"${remainingPrompt}\"`;\r\n\r\n            if (imageText) {\r\n                referencePrompt += `\\n\\n**Explicit Text Overlay:** The user has provided specific text in quotes: \"${imageText}\". You MUST overlay this text on the image. If there was existing text, replace it. Ensure the new text is readable and well-composed.`\r\n            }\r\n\r\n            if (input.outputType === 'video') {\r\n                referencePrompt += `\\n\\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;\r\n                if (imageText) {\r\n                    referencePrompt += `\\n\\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: \"${imageText}\". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;\r\n                }\r\n            }\r\n\r\n            if (input.useBrandProfile && input.brandProfile) {\r\n                const bp = input.brandProfile;\r\n                let brandGuidelines = '\\n\\n**Brand Guidelines:**';\r\n\r\n                if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    promptParts.push({ media: { url: bp.logoDataUrl, contentType: getMimeTypeFromDataURI(bp.logoDataUrl) } });\r\n                    brandGuidelines += ` A logo has also been provided. Integrate it naturally into the new design.`\r\n                } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    brandGuidelines += ` Create a design that represents the brand identity (SVG logo format not supported by AI model).`\r\n                }\r\n                referencePrompt += brandGuidelines;\r\n            }\r\n\r\n            textPrompt = referencePrompt;\r\n            if (textPrompt) {\r\n                promptParts.push({ text: textPrompt });\r\n            }\r\n            promptParts.push({ media: { url: input.referenceAssetUrl, contentType: getMimeTypeFromDataURI(input.referenceAssetUrl) } });\r\n\r\n        } else if (input.useBrandProfile && input.brandProfile) {\r\n            // This is a new, on-brand asset generation with advanced design principles.\r\n            const bp = input.brandProfile;\r\n\r\n            // Get business-specific design DNA\r\n            const businessDNA = BUSINESS_TYPE_DESIGN_DNA[bp.businessType as keyof typeof BUSINESS_TYPE_DESIGN_DNA] || BUSINESS_TYPE_DESIGN_DNA.default;\r\n\r\n            let onBrandPrompt = `Create a stunning, professional social media ${input.outputType} for ${bp.businessName || 'this business'}.\r\n\r\nBUSINESS: ${bp.businessName || 'Professional Business'} (${bp.businessType})\r\nCONTENT: \"${remainingPrompt}\"\r\nSTYLE: ${bp.visualStyle}, modern, clean, professional\r\n\r\nFORMAT: ${input.aspectRatio ? `${input.aspectRatio} aspect ratio` : 'Square 1:1 format'}\r\n\r\nBRAND COLORS (use prominently):\r\n${bp.primaryColor ? `- Primary: ${bp.primaryColor}` : ''}\r\n${bp.accentColor ? `- Accent: ${bp.accentColor}` : ''}\r\n${bp.backgroundColor ? `- Background: ${bp.backgroundColor}` : ''}\r\n\r\nREQUIREMENTS:\r\n- High-quality, professional design\r\n- ${bp.visualStyle} aesthetic\r\n- Clean, modern layout\r\n- Perfect for ${bp.businessType} business\r\n- Brand colors prominently featured\r\n- Professional social media appearance`;\r\n\r\n            // Intelligent design examples processing\r\n            let designDNA = '';\r\n            let selectedExamples: string[] = [];\r\n\r\n            if (bp.designExamples && bp.designExamples.length > 0) {\r\n                try {\r\n                    // Analyze design examples for intelligent processing\r\n                    const analyses: DesignAnalysis[] = [];\r\n                    for (const example of bp.designExamples.slice(0, 3)) { // Limit for performance\r\n                        try {\r\n                            const analysis = await analyzeDesignExample(\r\n                                example,\r\n                                bp.businessType,\r\n                                'creative-studio',\r\n                                `${bp.visualStyle} ${input.outputType} for ${remainingPrompt}`\r\n                            );\r\n                            analyses.push(analysis);\r\n                        } catch (error) {\r\n                            console.warn('Design analysis failed for example, skipping:', error);\r\n                        }\r\n                    }\r\n\r\n                    if (analyses.length > 0) {\r\n                        // Extract design DNA from analyzed examples\r\n                        designDNA = extractDesignDNA(analyses);\r\n\r\n                        // Select optimal examples based on analysis\r\n                        selectedExamples = selectOptimalDesignExamples(\r\n                            bp.designExamples,\r\n                            analyses,\r\n                            remainingPrompt,\r\n                            'creative-studio',\r\n                            2\r\n                        );\r\n                    } else {\r\n                        selectedExamples = bp.designExamples.slice(0, 2);\r\n                    }\r\n                } catch (error) {\r\n                    console.warn('Design analysis system failed, using fallback:', error);\r\n                    selectedExamples = bp.designExamples.slice(0, 2);\r\n                }\r\n\r\n                onBrandPrompt += `\\n**STYLE REFERENCE:**\r\nUse the provided design examples as style reference to create a similar visual aesthetic, color scheme, typography, and overall design approach. Match the style, mood, and visual characteristics of the reference designs while creating new content.\r\n\r\n${designDNA}`;\r\n            }\r\n\r\n            if (input.outputType === 'image') {\r\n                onBrandPrompt += `\\n- **Text Overlay Requirements:** ${imageText ? `\r\n                  * Display this EXACT text: \"${imageText}\"\r\n                  * Use ENGLISH ONLY - no foreign languages, symbols, or corrupted characters\r\n                  * Make text LARGE and BOLD for mobile readability\r\n                  * Apply high contrast (minimum 4.5:1 ratio) between text and background\r\n                  * Add text shadows, outlines, or semi-transparent backgrounds for readability\r\n                  * Position text using rule of thirds for optimal composition\r\n                  * Ensure text is the primary focal point of the design` : 'No text should be added to the asset.'}`;\r\n                onBrandPrompt += `\\n- **Logo Placement:** The provided logo must be integrated naturally into the design (e.g., on a product, a sign, or as a subtle watermark).`;\r\n                onBrandPrompt += `\\n- **Critical Language Rule:** ALL text must be in clear, readable ENGLISH only. Never use foreign languages, corrupted text, or unreadable symbols.`;\r\n\r\n                if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    promptParts.push({ media: { url: bp.logoDataUrl, contentType: getMimeTypeFromDataURI(bp.logoDataUrl) } });\r\n                }\r\n                textPrompt = onBrandPrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            } else { // Video\r\n                onBrandPrompt += `\\n- **Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;\r\n                if (input.aspectRatio === '16:9') {\r\n                    onBrandPrompt += ' The video should have relevant sound.';\r\n                }\r\n                if (imageText) {\r\n                    onBrandPrompt += `\\n- **Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: \"${imageText}\". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`\r\n                }\r\n                if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    onBrandPrompt += `\\n- **Logo Placement:** The provided logo must be integrated naturally into the design.`;\r\n                    promptParts.push({ media: { url: bp.logoDataUrl, contentType: getMimeTypeFromDataURI(bp.logoDataUrl) } });\r\n                } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    onBrandPrompt += `\\n- **Brand Identity:** Create a design that represents the brand identity and style.`;\r\n                }\r\n\r\n                // Add selected design examples as reference\r\n                selectedExamples.forEach(designExample => {\r\n                    promptParts.push({ media: { url: designExample, contentType: getMimeTypeFromDataURI(designExample) } });\r\n                });\r\n\r\n                textPrompt = onBrandPrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            }\r\n        } else {\r\n            // This is a new, un-branded, creative prompt.\r\n            let creativePrompt = `You are an expert creative director specializing in high-end advertisements. Generate a compelling, high-quality social media advertisement ${input.outputType} based on the following instruction: \"${remainingPrompt}\".\r\n\r\n⚡ GEMINI 2.0 FLASH HD QUALITY ENHANCEMENTS:\r\n- MAXIMUM RESOLUTION: Ultra-high definition rendering (4K+ quality)\r\n- SMALL FONT SIZE EXCELLENCE: Perfect rendering at 8pt, 10pt, 12pt, and all small font sizes\r\n- TINY TEXT PRECISION: Every character sharp and legible even when font size is very small\r\n- HIGH-DPI SMALL TEXT: Render small fonts as if on 300+ DPI display for maximum sharpness\r\n- PERFECT ANATOMY: Complete, symmetrical faces with natural expressions\r\n- SHARP DETAILS: Crystal-clear textures, no blur or artifacts\r\n- PROFESSIONAL LIGHTING: Studio-quality lighting with proper shadows\r\n- PREMIUM COMPOSITION: Golden ratio layouts with perfect balance\r\n- ADVANCED COLOR THEORY: Perfect contrast ratios (7:1 minimum) with vibrant, accurate colors`;\r\n\r\n            if (input.outputType === 'image' && imageText) {\r\n                creativePrompt += `\r\n\r\n🚨🚨🚨 EMERGENCY OVERRIDE - CRITICAL TEXT CONTROL 🚨🚨🚨\r\n\r\n⛔ ABSOLUTE PROHIBITION - NO EXCEPTIONS:\r\n- NEVER add \"Flex Your Finances\" or any financial terms\r\n- NEVER add \"Payroll Banking Simplified\" or banking phrases\r\n- NEVER add \"Banking Made Easy\" or similar taglines\r\n- NEVER add company descriptions or service explanations\r\n- NEVER add marketing copy or promotional text\r\n- NEVER add placeholder text or sample content\r\n- NEVER create fake headlines or taglines\r\n- NEVER add descriptive text about the business\r\n- NEVER add ANY text except what is specified below\r\n\r\n🎯 ONLY THIS TEXT IS ALLOWED: \"${imageText}\"\r\n🎯 REPEAT: ONLY THIS TEXT: \"${imageText}\"\r\n🎯 NO OTHER TEXT PERMITTED: \"${imageText}\"\r\n\r\n🌍 ENGLISH ONLY REQUIREMENT:\r\n- ALL text must be in clear, readable English\r\n- NO foreign languages (Arabic, Chinese, Hindi, etc.)\r\n- NO special characters, symbols, or corrupted text\r\n- NO accents or diacritical marks\r\n\r\nOverlay ONLY the following text onto the asset: \"${imageText}\".\r\nDO NOT ADD ANY OTHER TEXT.\r\nEnsure the text is readable and well-composed.`\r\n                textPrompt = creativePrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            } else { // Video\r\n                creativePrompt += `\\n\\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;\r\n                if (input.aspectRatio === '16:9') {\r\n                    creativePrompt += ' The video should have relevant sound.';\r\n                }\r\n                if (imageText) {\r\n                    creativePrompt += `\\n\\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: \"${imageText}\". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;\r\n                }\r\n                textPrompt = creativePrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            }\r\n        }\r\n\r\n        const aiExplanationPrompt = ai.definePrompt({\r\n            name: 'creativeAssetExplanationPrompt',\r\n            prompt: `Based on the generated ${input.outputType}, write a very brief, one-sentence explanation of the creative choices made. For example: \"I created a modern, vibrant image of a coffee shop, using your brand's primary color for the logo.\"`\r\n        });\r\n\r\n        const explanationResult = await aiExplanationPrompt();\r\n\r\n        try {\r\n            if (input.outputType === 'image') {\r\n                // Generate image with quality validation\r\n                let finalImageUrl: string | null = null;\r\n                let attempts = 0;\r\n                const maxAttempts = 2;\r\n\r\n                while (attempts < maxAttempts && !finalImageUrl) {\r\n                    attempts++;\r\n\r\n                    const { media } = await generateWithRetry({\r\n                        model: 'googleai/gemini-2.5-flash-preview',\r\n                        prompt: promptParts,\r\n                        config: {\r\n                            responseModalities: ['TEXT', 'IMAGE'],\r\n                        },\r\n                    });\r\n\r\n                    let imageUrl = media?.url ?? null;\r\n                    if (!imageUrl) {\r\n                        if (attempts === maxAttempts) {\r\n                            throw new Error('Failed to generate image');\r\n                        }\r\n                        continue;\r\n                    }\r\n\r\n                    // Apply aspect ratio correction if needed\r\n                    if (input.aspectRatio && input.aspectRatio !== '1:1') {\r\n                        console.log(`🖼️ Applying aspect ratio correction for ${input.aspectRatio}...`);\r\n                        try {\r\n                            const { cropImageFromUrl } = await import('@/lib/image-processing');\r\n                            // Map aspect ratio to platform for cropping\r\n                            const platformForCropping = input.aspectRatio === '16:9' ? 'linkedin' :\r\n                                input.aspectRatio === '9:16' ? 'story' : 'instagram';\r\n                            imageUrl = await cropImageFromUrl(imageUrl, platformForCropping);\r\n                            console.log(`✅ Image cropped successfully for ${input.aspectRatio}`);\r\n                        } catch (cropError) {\r\n                            console.warn('⚠️ Image cropping failed, using original:', cropError);\r\n                            // Continue with original image if cropping fails\r\n                        }\r\n                    }\r\n\r\n                    // Quality validation for brand profile designs\r\n                    if (input.useBrandProfile && input.brandProfile && attempts === 1) {\r\n                        try {\r\n                            const quality = await assessDesignQuality(\r\n                                imageUrl,\r\n                                input.brandProfile.businessType,\r\n                                'creative-studio',\r\n                                input.brandProfile.visualStyle,\r\n                                undefined,\r\n                                `Creative asset: ${remainingPrompt}`\r\n                            );\r\n\r\n                            // If quality is acceptable, use this design\r\n                            if (meetsQualityStandards(quality, 6)) { // Slightly lower threshold for creative assets\r\n                                finalImageUrl = imageUrl;\r\n                                break;\r\n                            }\r\n\r\n                            // If quality is poor and we have attempts left, try to improve\r\n                            if (attempts < maxAttempts) {\r\n                                console.log(`Creative asset quality score: ${quality.overall.score}/10. Attempting improvement...`);\r\n\r\n                                // Add improvement instructions to prompt\r\n                                const improvementInstructions = generateImprovementPrompt(quality);\r\n                                const improvedPrompt = `${promptParts[0].text}\\n\\n${improvementInstructions}`;\r\n                                promptParts[0] = { text: improvedPrompt };\r\n                                continue;\r\n                            } else {\r\n                                finalImageUrl = imageUrl;\r\n                                break;\r\n                            }\r\n                        } catch (qualityError) {\r\n                            console.warn('Quality assessment failed for creative asset, using generated design:', qualityError);\r\n                            finalImageUrl = imageUrl;\r\n                            break;\r\n                        }\r\n                    } else {\r\n                        finalImageUrl = imageUrl;\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                return {\r\n                    imageUrl: finalImageUrl,\r\n                    videoUrl: null,\r\n                    aiExplanation: explanationResult.output ?? \"Here is the generated image based on your prompt.\"\r\n                };\r\n            } else { // Video generation\r\n                const isVertical = input.aspectRatio === '9:16';\r\n\r\n                const model = isVertical ? 'googleai/veo-2.0-generate-001' : 'googleai/veo-3.0-generate-preview';\r\n                const config: Record<string, any> = {};\r\n                if (isVertical) {\r\n                    config.aspectRatio = '9:16';\r\n                    config.durationSeconds = 8;\r\n                }\r\n\r\n                const result = await generateWithRetry({\r\n                    model,\r\n                    prompt: promptParts,\r\n                    config,\r\n                });\r\n\r\n                let operation = result.operation;\r\n\r\n                if (!operation) {\r\n                    throw new Error('The video generation process did not start correctly. Please try again.');\r\n                }\r\n\r\n                // Poll for completion\r\n                while (!operation.done) {\r\n                    await new Promise(resolve => setTimeout(resolve, 5000)); // wait 5s\r\n                    operation = await ai.checkOperation(operation);\r\n                }\r\n\r\n                if (operation.error) {\r\n                    console.error(\"Video generation operation failed\", operation.error);\r\n                    throw new Error(`Video generation failed: ${operation.error.message}. Please try again.`);\r\n                }\r\n\r\n                const videoPart = operation.output?.message?.content.find(p => !!p.media);\r\n                if (!videoPart || !videoPart.media) {\r\n                    throw new Error('Video generation completed, but the final video file could not be found.');\r\n                }\r\n\r\n                const videoDataUrl = await videoToDataURI(videoPart);\r\n\r\n                return {\r\n                    imageUrl: null,\r\n                    videoUrl: videoDataUrl,\r\n                    aiExplanation: explanationResult.output ?? \"Here is the generated video based on your prompt.\"\r\n                };\r\n            }\r\n        } catch (e: any) {\r\n            console.error(\"Error during creative asset generation:\", e);\r\n            // Ensure a user-friendly error is thrown\r\n            const message = e.message || \"An unknown error occurred during asset generation.\";\r\n            throw new Error(message);\r\n        }\r\n    }\r\n);\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;IA0DsB,wBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/next/src/build/webpack/loaders/next-flight-loader/action-client-wrapper.ts"], "sourcesContent": ["// This file must be bundled in the app's client layer, it shouldn't be directly\n// imported by the server.\n\nexport { callServer } from 'next/dist/client/app-call-server'\nexport { findSourceMapURL } from 'next/dist/client/app-find-source-map-url'\n\n// A noop wrapper to let the Flight client create the server reference.\n// See also: https://github.com/facebook/react/pull/26632\n// Since we're using the Edge build of Flight client for SSR [1], here we need to\n// also use the same Edge build to create the reference. For the client bundle,\n// we use the default and let Webpack to resolve it to the correct version.\n// 1: https://github.com/vercel/next.js/blob/16eb80b0b0be13f04a6407943664b5efd8f3d7d0/packages/next/src/server/app-render/use-flight-response.tsx#L24-L26\nexport const createServerReference = (\n  (!!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')) as typeof import('react-server-dom-webpack/client')\n).createServerReference\n"], "names": ["callServer", "createServerReference", "findSourceMapURL", "process", "env", "NEXT_RUNTIME", "require"], "mappings": "AAAA,gFAAgF;AAChF,0BAA0B;;;;;;;;;;;;;;;;;IAEjBA,UAAU,EAAA;eAAVA,eAAAA,UAAU;;IASNC,qBAAqB,EAAA;eAArBA;;IARJC,gBAAgB,EAAA;eAAhBA,qBAAAA,gBAAgB;;;+BADE;qCACM;AAQ1B,MAAMD,wBACV,CAAA,CAAC,CAACE,QAAQC,GAAG,CAACC,YAAY,GAEvBC,QAAQ,0CAERA,QAAQ,8JAAiC,EAC7CL,qBAAqB", "ignoreList": [0], "debugId": null}}]}