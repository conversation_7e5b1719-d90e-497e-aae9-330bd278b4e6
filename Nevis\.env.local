# Google AI API Key for Gemini (REMOVED - Using new key below)

# OpenAI API Key for GPT-Image 1 (Premium Quality)
# Get your API key from: https://platform.openai.com/api-keys
# OPENAI_API_KEY=sk-your-openai-api-key-here

# =============================================================================
# REAL-TIME TRENDING TOPICS API KEYS (Optional - system works without these)
# =============================================================================

# Google Trends API (via SerpApi)
# Get your API key from: https://serpapi.com/
# GOOGLE_TRENDS_API_KEY=your_serpapi_key_here

# Twitter API v2 (for trending hashtags)
# Get your API key from: https://developer.twitter.com/
# TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here

# News API (for current events) - ACTIVE
NEWS_API_KEY=64b1a341-e683-47d6-b084-933ae03d9973

# Twitter API v2 (for trending hashtags) - ACTIVE
TWITTER_BEARER_TOKEN=AAAAAAAAAAAAAAAAAAAAAElZ3QEAAAAAPnynvgr%2FL2GFD2ckwW723lJtZmk%3Ddo0T2DWe0dkGzM3MRqQ1sitlGSCTNgkY82IBHLE59dWZgQV1N6

# Google Trends via RSS Feeds (no API key needed) - ACTIVE
GOOGLE_TRENDS_RSS_ENABLED=true

# Reddit via RSS Feeds (no API key needed) - ACTIVE
REDDIT_RSS_ENABLED=true

# YouTube Data API (for video trends)
# Get your API key from: https://console.developers.google.com/
# YOUTUBE_API_KEY=your_youtube_api_key_here

# Eventbrite API (for local events) - ACTIVE
EVENTBRITE_API_KEY=********************

# OpenWeather API (for weather context) - ACTIVE
OPENWEATHER_API_KEY=********************************

# =============================================================================
# OPENAI API KEY (for Enhanced Design Generation with DALL-E 3)
# =============================================================================

# OpenAI API Key for DALL-E 3 Enhanced Design Generation
# Get your API key from: https://platform.openai.com/api-keys
# IMPORTANT: Replace with your actual OpenAI API key to enable enhanced design generation
OPENAI_API_KEY=********************************************************************************************************************************************************************

# =============================================================================
# XAI GROK API KEY (for Grok 2.0 Image Generation)
# =============================================================================

# xAI Grok API Key for Grok 2.0 Image Generation
# Get your API key from: https://console.x.ai/team/default/api-keys
# IMPORTANT: Replace with your actual xAI API key to enable Grok image generation
XAI_API_KEY=************************************************************************************

# =============================================================================
# FIREBASE CONFIGURATION
# =============================================================================

# Firebase Project Configuration
NEXT_PUBLIC_FIREBASE_PROJECT_ID=localbuzz-mpkuv
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyAIQQLuNAc0YhNz4o9LF1Zyw_Fy0nJUfwI
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=localbuzz-mpkuv.firebaseapp.com
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=localbuzz-mpkuv.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:3f6b7d195dd4a847c4e1a2

# Firebase Admin Service Account (for server-side operations)
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Google AI API Key (for Gemini models and Revo 2.0)
# Server-side keys
GEMINI_API_KEY=AIzaSyAvcF4FbLrNkMzowvhCdMk8eGCWJumXJkc
GOOGLE_API_KEY=AIzaSyAvcF4FbLrNkMzowvhCdMk8eGCWJumXJkc
GOOGLE_GENAI_API_KEY=AIzaSyAvcF4FbLrNkMzowvhCdMk8eGCWJumXJkc

# Client-side keys (NEXT_PUBLIC_ prefix makes them available in browser)
NEXT_PUBLIC_GEMINI_API_KEY=AIzaSyAvcF4FbLrNkMzowvhCdMk8eGCWJumXJkc
NEXT_PUBLIC_GOOGLE_API_KEY=AIzaSyAvcF4FbLrNkMzowvhCdMk8eGCWJumXJkc
NEXT_PUBLIC_GOOGLE_GENAI_API_KEY=AIzaSyAvcF4FbLrNkMzowvhCdMk8eGCWJumXJkc

# Development Environment
NODE_ENV=development

# =============================================================================
# REVO 2.0 CONFIGURATION (Imagen 4 Integration)
# =============================================================================

# Enable REvoo 2.0 model with Imagen 4 integration
REVO_2_0_ENABLED=true

# Google Cloud Configuration for Imagen 4
# Project ID for Google Cloud Vertex AI (already set above in Firebase config)
GOOGLE_CLOUD_PROJECT_ID=localbuzz-mpkuv

# Google Cloud Authentication (uses Firebase Service Account Key above)
# The FIREBASE_SERVICE_ACCOUNT_KEY above contains the necessary Google Cloud credentials

# AIML API Configuration for FLUX Kontext Max (Revo 2.0)
# Get your API key from: https://aimlapi.com/
# This is required for Revo 2.0 FLUX Kontext Max to function
AIML_API_KEY=fa736f2cd7fe42829d2196c15357ae6e
