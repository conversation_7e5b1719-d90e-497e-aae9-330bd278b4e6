{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/hooks/use-toast.ts"], "sourcesContent": ["\"use client\"\r\n\r\n// Inspired by react-hot-toast library\r\nimport * as React from \"react\"\r\n\r\nimport type {\r\n  ToastActionElement,\r\n  ToastProps,\r\n} from \"@/components/ui/toast\"\r\n\r\nconst TOAST_LIMIT = 1\r\nconst TOAST_REMOVE_DELAY = 1000000\r\n\r\ntype ToasterToast = ToastProps & {\r\n  id: string\r\n  title?: React.ReactNode\r\n  description?: React.ReactNode\r\n  action?: ToastActionElement\r\n}\r\n\r\nconst actionTypes = {\r\n  ADD_TOAST: \"ADD_TOAST\",\r\n  UPDATE_TOAST: \"UPDATE_TOAST\",\r\n  DISMISS_TOAST: \"DISMISS_TOAST\",\r\n  REMOVE_TOAST: \"REMOVE_TOAST\",\r\n} as const\r\n\r\nlet count = 0\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\r\n  return count.toString()\r\n}\r\n\r\ntype ActionType = typeof actionTypes\r\n\r\ntype Action =\r\n  | {\r\n      type: ActionType[\"ADD_TOAST\"]\r\n      toast: ToasterToast\r\n    }\r\n  | {\r\n      type: ActionType[\"UPDATE_TOAST\"]\r\n      toast: Partial<ToasterToast>\r\n    }\r\n  | {\r\n      type: ActionType[\"DISMISS_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n  | {\r\n      type: ActionType[\"REMOVE_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n\r\ninterface State {\r\n  toasts: ToasterToast[]\r\n}\r\n\r\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\r\n\r\nconst addToRemoveQueue = (toastId: string) => {\r\n  if (toastTimeouts.has(toastId)) {\r\n    return\r\n  }\r\n\r\n  const timeout = setTimeout(() => {\r\n    toastTimeouts.delete(toastId)\r\n    dispatch({\r\n      type: \"REMOVE_TOAST\",\r\n      toastId: toastId,\r\n    })\r\n  }, TOAST_REMOVE_DELAY)\r\n\r\n  toastTimeouts.set(toastId, timeout)\r\n}\r\n\r\nexport const reducer = (state: State, action: Action): State => {\r\n  switch (action.type) {\r\n    case \"ADD_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      }\r\n\r\n    case \"UPDATE_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\r\n        ),\r\n      }\r\n\r\n    case \"DISMISS_TOAST\": {\r\n      const { toastId } = action\r\n\r\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\r\n      // but I'll keep it here for simplicity\r\n      if (toastId) {\r\n        addToRemoveQueue(toastId)\r\n      } else {\r\n        state.toasts.forEach((toast) => {\r\n          addToRemoveQueue(toast.id)\r\n        })\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === toastId || toastId === undefined\r\n            ? {\r\n                ...t,\r\n                open: false,\r\n              }\r\n            : t\r\n        ),\r\n      }\r\n    }\r\n    case \"REMOVE_TOAST\":\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        }\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\r\n      }\r\n  }\r\n}\r\n\r\nconst listeners: Array<(state: State) => void> = []\r\n\r\nlet memoryState: State = { toasts: [] }\r\n\r\nfunction dispatch(action: Action) {\r\n  memoryState = reducer(memoryState, action)\r\n  listeners.forEach((listener) => {\r\n    listener(memoryState)\r\n  })\r\n}\r\n\r\ntype Toast = Omit<ToasterToast, \"id\">\r\n\r\nfunction toast({ ...props }: Toast) {\r\n  const id = genId()\r\n\r\n  const update = (props: ToasterToast) =>\r\n    dispatch({\r\n      type: \"UPDATE_TOAST\",\r\n      toast: { ...props, id },\r\n    })\r\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\r\n\r\n  dispatch({\r\n    type: \"ADD_TOAST\",\r\n    toast: {\r\n      ...props,\r\n      id,\r\n      open: true,\r\n      onOpenChange: (open) => {\r\n        if (!open) dismiss()\r\n      },\r\n    },\r\n  })\r\n\r\n  return {\r\n    id: id,\r\n    dismiss,\r\n    update,\r\n  }\r\n}\r\n\r\nfunction useToast() {\r\n  const [state, setState] = React.useState<State>(memoryState)\r\n\r\n  React.useEffect(() => {\r\n    listeners.push(setState)\r\n    return () => {\r\n      const index = listeners.indexOf(setState)\r\n      if (index > -1) {\r\n        listeners.splice(index, 1)\r\n      }\r\n    }\r\n  }, [state])\r\n\r\n  return {\r\n    ...state,\r\n    toast,\r\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\r\n  }\r\n}\r\n\r\nexport { useToast, toast }\r\n"], "names": [], "mappings": ";;;;;AAEA,sCAAsC;AACtC;AAHA;;AAUA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\n/**\r\n * Convert HEX color to HSL format for CSS variables\r\n * @param hex - HEX color string (e.g., \"#3b82f6\")\r\n * @returns HSL string in format \"210 70% 50%\" or null if invalid\r\n */\r\nexport function hexToHsl(hex: string): string | null {\r\n  if (!hex || !hex.startsWith('#')) {\r\n    return null;\r\n  }\r\n\r\n  // Remove # and convert to RGB\r\n  const r = parseInt(hex.slice(1, 3), 16) / 255;\r\n  const g = parseInt(hex.slice(3, 5), 16) / 255;\r\n  const b = parseInt(hex.slice(5, 7), 16) / 255;\r\n\r\n  const max = Math.max(r, g, b);\r\n  const min = Math.min(r, g, b);\r\n  let h = 0;\r\n  let s = 0;\r\n  const l = (max + min) / 2;\r\n\r\n  if (max !== min) {\r\n    const d = max - min;\r\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\r\n\r\n    switch (max) {\r\n      case r:\r\n        h = (g - b) / d + (g < b ? 6 : 0);\r\n        break;\r\n      case g:\r\n        h = (b - r) / d + 2;\r\n        break;\r\n      case b:\r\n        h = (r - g) / d + 4;\r\n        break;\r\n    }\r\n    h /= 6;\r\n  }\r\n\r\n  // Convert to CSS HSL format (no commas, percentages for S and L)\r\n  const hue = Math.round(h * 360);\r\n  const saturation = Math.round(s * 100);\r\n  const lightness = Math.round(l * 100);\r\n\r\n  return `${hue} ${saturation}% ${lightness}%`;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAOO,SAAS,SAAS,GAAW;IAClC,IAAI,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,MAAM;QAChC,OAAO;IACT;IAEA,8BAA8B;IAC9B,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI,MAAM;IAC1C,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI,MAAM;IAC1C,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI,MAAM;IAE1C,MAAM,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG;IAC3B,MAAM,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG;IAC3B,IAAI,IAAI;IACR,IAAI,IAAI;IACR,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI;IAExB,IAAI,QAAQ,KAAK;QACf,MAAM,IAAI,MAAM;QAChB,IAAI,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG;QAElD,OAAQ;YACN,KAAK;gBACH,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;gBAChC;YACF,KAAK;gBACH,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;gBAClB;YACF,KAAK;gBACH,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;gBAClB;QACJ;QACA,KAAK;IACP;IAEA,iEAAiE;IACjE,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI;IAC3B,MAAM,aAAa,KAAK,KAAK,CAAC,IAAI;IAClC,MAAM,YAAY,KAAK,KAAK,CAAC,IAAI;IAEjC,OAAO,GAAG,IAAI,CAAC,EAAE,WAAW,EAAE,EAAE,UAAU,CAAC,CAAC;AAC9C", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ToastProvider = ToastPrimitives.Provider\r\n\r\nconst ToastViewport = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Viewport\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\r\n\r\nconst toastVariants = cva(\r\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"border bg-background text-foreground\",\r\n        destructive:\r\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Toast = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\r\n    VariantProps<typeof toastVariants>\r\n>(({ className, variant, ...props }, ref) => {\r\n  return (\r\n    <ToastPrimitives.Root\r\n      ref={ref}\r\n      className={cn(toastVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nToast.displayName = ToastPrimitives.Root.displayName\r\n\r\nconst ToastAction = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Action>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Action\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nToastAction.displayName = ToastPrimitives.Action.displayName\r\n\r\nconst ToastClose = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Close>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Close\r\n    ref={ref}\r\n    className={cn(\r\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\r\n      className\r\n    )}\r\n    toast-close=\"\"\r\n    {...props}\r\n  >\r\n    <X className=\"h-4 w-4\" />\r\n  </ToastPrimitives.Close>\r\n))\r\nToastClose.displayName = ToastPrimitives.Close.displayName\r\n\r\nconst ToastTitle = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Title>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Title\r\n    ref={ref}\r\n    className={cn(\"text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nToastTitle.displayName = ToastPrimitives.Title.displayName\r\n\r\nconst ToastDescription = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Description>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm opacity-90\", className)}\r\n    {...props}\r\n  />\r\n))\r\nToastDescription.displayName = ToastPrimitives.Description.displayName\r\n\r\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\r\n\r\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\r\n\r\nexport {\r\n  type ToastProps,\r\n  type ToastActionElement,\r\n  ToastProvider,\r\n  ToastViewport,\r\n  Toast,\r\n  ToastTitle,\r\n  ToastDescription,\r\n  ToastClose,\r\n  ToastAction,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,gBAAgB,iKAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,iKAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,8OAAC,iKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AACA,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,SAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,iKAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,iKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,iKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,iKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/toaster.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport {\r\n  Toast,\r\n  ToastClose,\r\n  ToastDescription,\r\n  ToastProvider,\r\n  ToastTitle,\r\n  ToastViewport,\r\n} from \"@/components/ui/toast\"\r\n\r\nexport function Toaster() {\r\n  const { toasts } = useToast()\r\n\r\n  return (\r\n    <ToastProvider>\r\n      {toasts.map(function ({ id, title, description, action, ...props }) {\r\n        return (\r\n          <Toast key={id} {...props}>\r\n            <div className=\"grid gap-1\">\r\n              {title && <ToastTitle>{title}</ToastTitle>}\r\n              {description && (\r\n                <ToastDescription>{description}</ToastDescription>\r\n              )}\r\n            </div>\r\n            {action}\r\n            <ToastClose />\r\n          </Toast>\r\n        )\r\n      })}\r\n      <ToastViewport />\r\n    </ToastProvider>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAE1B,qBACE,8OAAC,iIAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACE,8OAAC,iIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,8OAAC;4BAAI,WAAU;;gCACZ,uBAAS,8OAAC,iIAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,8OAAC,iIAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,8OAAC,iIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/hooks/use-mobile.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,qKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Sheet = SheetPrimitive.Root\r\n\r\nconst SheetTrigger = SheetPrimitive.Trigger\r\n\r\nconst SheetClose = SheetPrimitive.Close\r\n\r\nconst SheetPortal = SheetPrimitive.Portal\r\n\r\nconst SheetOverlay = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\r\n\r\nconst sheetVariants = cva(\r\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n  {\r\n    variants: {\r\n      side: {\r\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\r\n        bottom:\r\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\r\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\r\n        right:\r\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      side: \"right\",\r\n    },\r\n  }\r\n)\r\n\r\ninterface SheetContentProps\r\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\r\n    VariantProps<typeof sheetVariants> {}\r\n\r\nconst SheetContent = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Content>,\r\n  SheetContentProps\r\n>(({ side = \"right\", className, children, ...props }, ref) => (\r\n  <SheetPortal>\r\n    <SheetOverlay />\r\n    <SheetPrimitive.Content\r\n      ref={ref}\r\n      className={cn(sheetVariants({ side }), className)}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </SheetPrimitive.Close>\r\n    </SheetPrimitive.Content>\r\n  </SheetPortal>\r\n))\r\nSheetContent.displayName = SheetPrimitive.Content.displayName\r\n\r\nconst SheetHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetHeader.displayName = \"SheetHeader\"\r\n\r\nconst SheetFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetFooter.displayName = \"SheetFooter\"\r\n\r\nconst SheetTitle = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetTitle.displayName = SheetPrimitive.Title.displayName\r\n\r\nconst SheetDescription = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetDescription.displayName = SheetPrimitive.Description.displayName\r\n\r\nexport {\r\n  Sheet,\r\n  SheetPortal,\r\n  SheetOverlay,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,kKAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,kKAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,kKAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,kKAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,kKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,kKAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 702, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider\r\n\r\nconst Tooltip = TooltipPrimitive.Root\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Content\r\n    ref={ref}\r\n    sideOffset={sideOffset}\r\n    className={cn(\r\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,mKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,mKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { VariantProps, cva } from \"class-variance-authority\"\r\nimport { PanelLeft } from \"lucide-react\"\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\"\r\nimport { Skeleton } from \"@/components/ui/skeleton\"\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\r\nconst SIDEBAR_WIDTH = \"16rem\"\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\r\n\r\ntype SidebarContext = {\r\n  state: \"expanded\" | \"collapsed\"\r\n  open: boolean\r\n  setOpen: (open: boolean) => void\r\n  openMobile: boolean\r\n  setOpenMobile: (open: boolean) => void\r\n  isMobile: boolean\r\n  toggleSidebar: () => void\r\n}\r\n\r\nconst SidebarContext = React.createContext<SidebarContext | null>(null)\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext)\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nconst SidebarProvider = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    defaultOpen?: boolean\r\n    open?: boolean\r\n    onOpenChange?: (open: boolean) => void\r\n  }\r\n>(\r\n  (\r\n    {\r\n      defaultOpen = true,\r\n      open: openProp,\r\n      onOpenChange: setOpenProp,\r\n      className,\r\n      style,\r\n      children,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const isMobile = useIsMobile()\r\n    const [openMobile, setOpenMobile] = React.useState(false)\r\n\r\n    // This is the internal state of the sidebar.\r\n    // We use openProp and setOpenProp for control from outside the component.\r\n    const [_open, _setOpen] = React.useState(defaultOpen)\r\n    const open = openProp ?? _open\r\n    const setOpen = React.useCallback(\r\n      (value: boolean | ((value: boolean) => boolean)) => {\r\n        const openState = typeof value === \"function\" ? value(open) : value\r\n        if (setOpenProp) {\r\n          setOpenProp(openState)\r\n        } else {\r\n          _setOpen(openState)\r\n        }\r\n\r\n        // This sets the cookie to keep the sidebar state.\r\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\r\n      },\r\n      [setOpenProp, open]\r\n    )\r\n\r\n    // Helper to toggle the sidebar.\r\n    const toggleSidebar = React.useCallback(() => {\r\n      return isMobile\r\n        ? setOpenMobile((open) => !open)\r\n        : setOpen((open) => !open)\r\n    }, [isMobile, setOpen, setOpenMobile])\r\n\r\n    // Adds a keyboard shortcut to toggle the sidebar.\r\n    React.useEffect(() => {\r\n      const handleKeyDown = (event: KeyboardEvent) => {\r\n        if (\r\n          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n          (event.metaKey || event.ctrlKey)\r\n        ) {\r\n          event.preventDefault()\r\n          toggleSidebar()\r\n        }\r\n      }\r\n\r\n      window.addEventListener(\"keydown\", handleKeyDown)\r\n      return () => window.removeEventListener(\"keydown\", handleKeyDown)\r\n    }, [toggleSidebar])\r\n\r\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n    // This makes it easier to style the sidebar with Tailwind classes.\r\n    const state = open ? \"expanded\" : \"collapsed\"\r\n\r\n    const contextValue = React.useMemo<SidebarContext>(\r\n      () => ({\r\n        state,\r\n        open,\r\n        setOpen,\r\n        isMobile,\r\n        openMobile,\r\n        setOpenMobile,\r\n        toggleSidebar,\r\n      }),\r\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n    )\r\n\r\n    return (\r\n      <SidebarContext.Provider value={contextValue}>\r\n        <TooltipProvider delayDuration={0}>\r\n          <div\r\n            style={\r\n              {\r\n                \"--sidebar-width\": SIDEBAR_WIDTH,\r\n                \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n                ...style,\r\n              } as React.CSSProperties\r\n            }\r\n            className={cn(\r\n              // Prevent horizontal overflow from nested elements inside the sidebar layout\r\n              \"group/sidebar-wrapper flex min-h-svh w-full overflow-x-hidden has-[[data-variant=inset]]:bg-sidebar\",\r\n              className\r\n            )}\r\n            ref={ref}\r\n            {...props}\r\n          >\r\n            {children}\r\n          </div>\r\n        </TooltipProvider>\r\n      </SidebarContext.Provider>\r\n    )\r\n  }\r\n)\r\nSidebarProvider.displayName = \"SidebarProvider\"\r\n\r\nconst Sidebar = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    side?: \"left\" | \"right\"\r\n    variant?: \"sidebar\" | \"floating\" | \"inset\"\r\n    collapsible?: \"offcanvas\" | \"icon\" | \"none\"\r\n  }\r\n>(\r\n  (\r\n    {\r\n      side = \"left\",\r\n      variant = \"sidebar\",\r\n      collapsible = \"offcanvas\",\r\n      className,\r\n      children,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\r\n\r\n    if (collapsible === \"none\") {\r\n      return (\r\n        <div\r\n          className={cn(\r\n            \"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\",\r\n            className\r\n          )}\r\n          ref={ref}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      )\r\n    }\r\n\r\n    if (isMobile) {\r\n      return (\r\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n          <SheetContent\r\n            data-sidebar=\"sidebar\"\r\n            data-mobile=\"true\"\r\n            className=\"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\r\n            style={\r\n              {\r\n                \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n              } as React.CSSProperties\r\n            }\r\n            side={side}\r\n          >\r\n            <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n          </SheetContent>\r\n        </Sheet>\r\n      )\r\n    }\r\n\r\n    return (\r\n      <div\r\n        ref={ref}\r\n        className=\"group peer hidden md:block text-sidebar-foreground\"\r\n        data-state={state}\r\n        data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n        data-variant={variant}\r\n        data-side={side}\r\n      >\r\n        {/* This is what handles the sidebar gap on desktop */}\r\n        <div\r\n          className={cn(\r\n            \"duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear\",\r\n            \"group-data-[collapsible=offcanvas]:w-0\",\r\n            \"group-data-[side=right]:rotate-180\",\r\n            variant === \"floating\" || variant === \"inset\"\r\n              ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\"\r\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\"\r\n          )}\r\n        />\r\n        <div\r\n          className={cn(\r\n            \"duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex\",\r\n            side === \"left\"\r\n              ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n              : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n            // Adjust the padding for floating and inset variants.\r\n            variant === \"floating\" || variant === \"inset\"\r\n              ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\"\r\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          <div\r\n            data-sidebar=\"sidebar\"\r\n            className=\"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\r\n          >\r\n            {children}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n)\r\nSidebar.displayName = \"Sidebar\"\r\n\r\nconst SidebarTrigger = React.forwardRef<\r\n  React.ElementRef<typeof Button>,\r\n  React.ComponentProps<typeof Button>\r\n>(({ className, onClick, ...props }, ref) => {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <Button\r\n      ref={ref}\r\n      data-sidebar=\"trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"h-7 w-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event)\r\n        toggleSidebar()\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeft />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  )\r\n})\r\nSidebarTrigger.displayName = \"SidebarTrigger\"\r\n\r\nconst SidebarRail = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\">\r\n>(({ className, ...props }, ref) => {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <button\r\n      ref={ref}\r\n      data-sidebar=\"rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex\",\r\n        \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarRail.displayName = \"SidebarRail\"\r\n\r\nconst SidebarInset = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"main\"> & {\r\n    fullWidth?: boolean\r\n  }\r\n>(({ className, fullWidth = false, ...props }, ref) => {\r\n  if (fullWidth) {\r\n    // For full-width mode, break out of the sidebar constraints entirely\r\n    return (\r\n      <main\r\n        ref={ref}\r\n        className={cn(\r\n          // Use fixed positioning but ensure proper flex layout for scrolling\r\n          \"fixed inset-0 z-0 bg-background flex flex-col\",\r\n          // Add left margin to account for sidebar when it's open\r\n          \"md:ml-[--sidebar-width] md:peer-data-[state=collapsed]:ml-[--sidebar-width-icon] md:peer-data-[collapsible=offcanvas]:ml-0\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n\r\n  return (\r\n    <main\r\n      ref={ref}\r\n      className={cn(\r\n        // Standard inset mode with responsive margins\r\n        \"relative flex min-h-svh flex-1 flex-col bg-background\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarInset.displayName = \"SidebarInset\"\r\n\r\nconst SidebarInput = React.forwardRef<\r\n  React.ElementRef<typeof Input>,\r\n  React.ComponentProps<typeof Input>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <Input\r\n      ref={ref}\r\n      data-sidebar=\"input\"\r\n      className={cn(\r\n        \"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarInput.displayName = \"SidebarInput\"\r\n\r\nconst SidebarHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarHeader.displayName = \"SidebarHeader\"\r\n\r\nconst SidebarFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarFooter.displayName = \"SidebarFooter\"\r\n\r\nconst SidebarSeparator = React.forwardRef<\r\n  React.ElementRef<typeof Separator>,\r\n  React.ComponentProps<typeof Separator>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <Separator\r\n      ref={ref}\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"mx-2 w-auto bg-sidebar-border\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarSeparator.displayName = \"SidebarSeparator\"\r\n\r\nconst SidebarContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarContent.displayName = \"SidebarContent\"\r\n\r\nconst SidebarGroup = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarGroup.displayName = \"SidebarGroup\"\r\n\r\nconst SidebarGroupLabel = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & { asChild?: boolean }\r\n>(({ className, asChild = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"div\"\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\"\r\n\r\nconst SidebarGroupAction = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & { asChild?: boolean }\r\n>(({ className, asChild = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 after:md:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarGroupAction.displayName = \"SidebarGroupAction\"\r\n\r\nconst SidebarGroupContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    data-sidebar=\"group-content\"\r\n    className={cn(\"w-full text-sm\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSidebarGroupContent.displayName = \"SidebarGroupContent\"\r\n\r\nconst SidebarMenu = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    data-sidebar=\"menu\"\r\n    className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSidebarMenu.displayName = \"SidebarMenu\"\r\n\r\nconst SidebarMenuItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ className, ...props }, ref) => (\r\n  <li\r\n    ref={ref}\r\n    data-sidebar=\"menu-item\"\r\n    className={cn(\"group/menu-item relative\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSidebarMenuItem.displayName = \"SidebarMenuItem\"\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst SidebarMenuButton = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & {\r\n    asChild?: boolean\r\n    isActive?: boolean\r\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>\r\n  } & VariantProps<typeof sidebarMenuButtonVariants>\r\n>(\r\n  (\r\n    {\r\n      asChild = false,\r\n      isActive = false,\r\n      variant = \"default\",\r\n      size = \"default\",\r\n      tooltip,\r\n      className,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    const { isMobile, state } = useSidebar()\r\n\r\n    const button = (\r\n      <Comp\r\n        ref={ref}\r\n        data-sidebar=\"menu-button\"\r\n        data-size={size}\r\n        data-active={isActive}\r\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n        {...props}\r\n      />\r\n    )\r\n\r\n    if (!tooltip) {\r\n      return button\r\n    }\r\n\r\n    if (typeof tooltip === \"string\") {\r\n      tooltip = {\r\n        children: tooltip,\r\n      }\r\n    }\r\n\r\n    return (\r\n      <Tooltip>\r\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n        <TooltipContent\r\n          side=\"right\"\r\n          align=\"center\"\r\n          hidden={state !== \"collapsed\" || isMobile}\r\n          {...tooltip}\r\n        />\r\n      </Tooltip>\r\n    )\r\n  }\r\n)\r\nSidebarMenuButton.displayName = \"SidebarMenuButton\"\r\n\r\nconst SidebarMenuAction = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & {\r\n    asChild?: boolean\r\n    showOnHover?: boolean\r\n  }\r\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 after:md:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n        \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarMenuAction.displayName = \"SidebarMenuAction\"\r\n\r\nconst SidebarMenuBadge = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    data-sidebar=\"menu-badge\"\r\n    className={cn(\r\n      \"absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none\",\r\n      \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n      \"peer-data-[size=sm]/menu-button:top-1\",\r\n      \"peer-data-[size=default]/menu-button:top-1.5\",\r\n      \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n      \"group-data-[collapsible=icon]:hidden\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\"\r\n\r\nconst SidebarMenuSkeleton = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    showIcon?: boolean\r\n  }\r\n>(({ className, showIcon = false, ...props }, ref) => {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`\r\n  }, [])\r\n\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"rounded-md h-8 flex gap-2 px-2 items-center\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 flex-1 max-w-[--skeleton-width]\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  )\r\n})\r\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\"\r\n\r\nconst SidebarMenuSub = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    data-sidebar=\"menu-sub\"\r\n    className={cn(\r\n      \"mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5\",\r\n      \"group-data-[collapsible=icon]:hidden\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nSidebarMenuSub.displayName = \"SidebarMenuSub\"\r\n\r\nconst SidebarMenuSubItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ ...props }, ref) => <li ref={ref} {...props} />)\r\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\"\r\n\r\nconst SidebarMenuSubButton = React.forwardRef<\r\n  HTMLAnchorElement,\r\n  React.ComponentProps<\"a\"> & {\r\n    asChild?: boolean\r\n    size?: \"sm\" | \"md\"\r\n    isActive?: boolean\r\n  }\r\n>(({ asChild = false, size = \"md\", isActive, className, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\"\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;AAqBA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAyB;AAElE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAQrC,CACE,EACE,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WACH,cAAc,CAAC,OAAS,CAAC,QACzB,QAAQ,CAAC,OAAS,CAAC;IACzB,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6EAA6E;gBAC7E,uGACA;gBAEF,KAAK;gBACJ,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,wBAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAQ7B,CACE,EACE,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAEF,KAAK;YACJ,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;0BAEN,cAAA,8OAAC;oBAAI,WAAU;8BAA+B;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;;0BAGX,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,yFACA;;;;;;0BAGR,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,kGACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEF,QAAQ,WAAW,GAAG;AAEtB,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,KAAK;QACL,gBAAa;QACb,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QACzB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,YAAS;;;;;0BACV,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,8EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKlC,CAAC,EAAE,SAAS,EAAE,YAAY,KAAK,EAAE,GAAG,OAAO,EAAE;IAC7C,IAAI,WAAW;QACb,qEAAqE;QACrE,qBACE,8OAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEAAoE;YACpE,iDACA,wDAAwD;YACxD,8HACA;YAED,GAAG,KAAK;;;;;;IAGf;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8CAA8C;QAC9C,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,qIAAA,CAAA,YAAS;QACR,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sOACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,mBAAmB,WAAW,GAAG;AAEjC,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGb,oBAAoB,WAAW,GAAG;AAElC,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAQvC,CACE,EACE,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OACJ,EACD;IAEA,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEF,kBAAkB,WAAW,GAAG;AAEhC,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAMvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,cAAc,KAAK,EAAE,GAAG,OAAO,EAAE;IAChE,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACA,4LACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG;AAE/B,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKzC,CAAC,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE;IAC5C,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,8OAAC;QAAG,KAAK;QAAM,GAAG,KAAK;;;;;;AAChD,mBAAmB,WAAW,GAAG;AAEjC,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAO1C,CAAC,EAAE,UAAU,KAAK,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAClE,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n))\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n))\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n))\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n))\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qSACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,4KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,4KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 1671, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAvatar.displayName = AvatarPrimitive.Root.displayName\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1842, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/firebase/config.ts"], "sourcesContent": ["// Firebase client configuration\r\nimport { initializeApp, getApps } from 'firebase/app';\r\nimport { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';\r\nimport { getAuth, connectAuthEmulator } from 'firebase/auth';\r\nimport { getStorage, connectStorageEmulator } from 'firebase/storage';\r\n\r\n// Check if Firebase is properly configured\r\nconst isFirebaseConfigured =\r\n  process.env.NEXT_PUBLIC_FIREBASE_API_KEY &&\r\n  process.env.NEXT_PUBLIC_FIREBASE_API_KEY !== 'your_firebase_api_key_here';\r\n\r\nconsole.log('🔧 Firebase Configuration Check:');\r\nconsole.log('- API Key exists:', !!process.env.NEXT_PUBLIC_FIREBASE_API_KEY);\r\nconsole.log('- API Key value:', process.env.NEXT_PUBLIC_FIREBASE_API_KEY?.substring(0, 10) + '...');\r\nconsole.log('- Project ID:', process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID);\r\nconsole.log('- Is configured:', isFirebaseConfigured);\r\n\r\nconst firebaseConfig = {\r\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || 'demo-key',\r\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || 'demo-project.firebaseapp.com',\r\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'demo-project',\r\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'demo-project.appspot.com',\r\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '123456789',\r\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || 'demo-app-id',\r\n};\r\n\r\n// Initialize Firebase with error handling\r\nlet app: any = null;\r\nlet db: any = null;\r\nlet auth: any = null;\r\nlet storage: any = null;\r\n\r\ntry {\r\n  if (isFirebaseConfigured) {\r\n    app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];\r\n    db = getFirestore(app);\r\n    auth = getAuth(app);\r\n    storage = getStorage(app);\r\n    console.log('✅ Firebase initialized successfully');\r\n  } else {\r\n    console.warn('⚠️ Firebase not configured - using demo mode');\r\n    // Create mock objects to prevent errors\r\n    app = { options: firebaseConfig };\r\n    db = null;\r\n    auth = null;\r\n    storage = null;\r\n  }\r\n} catch (error) {\r\n  console.error('❌ Firebase initialization failed:', error);\r\n  app = null;\r\n  db = null;\r\n  auth = null;\r\n  storage = null;\r\n}\r\n\r\nexport { db, auth, storage };\r\n\r\n// DISABLED: Connect to emulators in development\r\n// The emulators are not running, so we'll connect directly to production Firebase\r\n//\r\n// if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {\r\n//   // Only connect to emulators if not already connected\r\n//   try {\r\n//     // Check if we're already connected to avoid multiple connections\r\n//     if (!auth.config.emulator) {\r\n//       connectAuthEmulator(auth, 'http://localhost:9099');\r\n//     }\r\n//   } catch (error) {\r\n//     // Emulator already connected or not available\r\n//   }\r\n\r\n//   try {\r\n//     if (!(db as any)._delegate._databaseId.projectId.includes('localhost')) {\r\n//       connectFirestoreEmulator(db, 'localhost', 8080);\r\n//     }\r\n//   } catch (error) {\r\n//     // Emulator already connected or not available\r\n//   }\r\n\r\n//   try {\r\n//     if (!storage.app.options.storageBucket?.includes('localhost')) {\r\n//       connectStorageEmulator(storage, 'localhost', 9199);\r\n//     }\r\n//   } catch (error) {\r\n//     // Emulator already connected or not available\r\n//   }\r\n// }\r\n\r\nexport { app };\r\nexport default app;\r\n"], "names": [], "mappings": "AAAA,gCAAgC;;;;;;;;AAChC;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AAEA,2CAA2C;AAC3C,MAAM,uBACJ,+EACA,gFAA6C;AAE/C,QAAQ,GAAG,CAAC;AACZ,QAAQ,GAAG,CAAC,qBAAqB,CAAC;AAClC,QAAQ,GAAG,CAAC,oBAAoB,6EAA0C,UAAU,GAAG,MAAM;AAC7F,QAAQ,GAAG,CAAC;AACZ,QAAQ,GAAG,CAAC,oBAAoB;AAEhC,MAAM,iBAAiB;IACrB,QAAQ,+EAA4C;IACpD,YAAY,uEAAgD;IAC5D,WAAW,uDAA+C;IAC1D,eAAe,2EAAmD;IAClE,mBAAmB,oDAAwD;IAC3E,OAAO,iFAA2C;AACpD;AAEA,0CAA0C;AAC1C,IAAI,MAAW;AACf,IAAI,KAAU;AACd,IAAI,OAAY;AAChB,IAAI,UAAe;AAEnB,IAAI;IACF,wCAA0B;QACxB,MAAM,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,IAAI,MAAM,KAAK,IAAI,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,GAAG,CAAC,EAAE;QAC3E,KAAK,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAClB,OAAO,CAAA,GAAA,yOAAA,CAAA,UAAO,AAAD,EAAE;QACf,UAAU,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD,EAAE;QACrB,QAAQ,GAAG,CAAC;IACd,OAAO;;IAOP;AACF,EAAE,OAAO,OAAO;IACd,QAAQ,KAAK,CAAC,qCAAqC;IACnD,MAAM;IACN,KAAK;IACL,OAAO;IACP,UAAU;AACZ;;;uCAoCe", "debugId": null}}, {"offset": {"line": 1908, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/firebase/schema.ts"], "sourcesContent": ["// Firestore database schema definitions\r\nimport { z } from 'zod';\r\nimport { Timestamp } from 'firebase/firestore';\r\n\r\n// Base schema for all documents\r\nexport const BaseDocumentSchema = z.object({\r\n  id: z.string(),\r\n  userId: z.string(),\r\n  createdAt: z.union([z.date(), z.custom<Timestamp>()]),\r\n  updatedAt: z.union([z.date(), z.custom<Timestamp>()]),\r\n});\r\n\r\n// User document schema\r\nexport const UserDocumentSchema = BaseDocumentSchema.extend({\r\n  email: z.string().email(),\r\n  displayName: z.string().optional(),\r\n  photoURL: z.string().url().optional(),\r\n  preferences: z.object({\r\n    theme: z.enum(['light', 'dark', 'system']).default('system'),\r\n    notifications: z.boolean().default(true),\r\n    autoSave: z.boolean().default(true),\r\n  }).optional(),\r\n  subscription: z.object({\r\n    plan: z.enum(['free', 'pro', 'enterprise']).default('free'),\r\n    status: z.enum(['active', 'inactive', 'cancelled']).default('active'),\r\n    expiresAt: z.union([z.date(), z.custom<Timestamp>()]).optional(),\r\n  }).optional(),\r\n});\r\n\r\n// Brand Profile document schema\r\nexport const BrandProfileDocumentSchema = BaseDocumentSchema.extend({\r\n  name: z.string(),\r\n  businessType: z.string().optional(),\r\n  description: z.string().optional(),\r\n  location: z.string().optional(),\r\n  website: z.string().optional(),\r\n  logoDataUrl: z.string().optional(), // Added logo support\r\n  socialMedia: z.object({\r\n    instagram: z.string().optional(),\r\n    facebook: z.string().optional(),\r\n    twitter: z.string().optional(),\r\n    linkedin: z.string().optional(),\r\n    tiktok: z.string().optional(),\r\n  }).optional(),\r\n  brandColors: z.array(z.string()).optional(),\r\n  brandFonts: z.array(z.string()).optional(),\r\n  visualStyle: z.string().optional(),\r\n  targetAudience: z.string().optional(),\r\n  brandVoice: z.string().optional(),\r\n  services: z.array(z.object({\r\n    name: z.string(),\r\n    description: z.string(),\r\n    category: z.string().optional(),\r\n  })).optional(),\r\n  designExamples: z.array(z.object({\r\n    url: z.string(),\r\n    description: z.string().optional(),\r\n    type: z.enum(['logo', 'banner', 'post', 'story', 'other']),\r\n  })).optional(),\r\n  isComplete: z.boolean().default(false),\r\n  version: z.string().default('1.0'),\r\n});\r\n\r\n// Generated Post document schema\r\nexport const GeneratedPostDocumentSchema = BaseDocumentSchema.extend({\r\n  brandProfileId: z.string(),\r\n  platform: z.enum(['instagram', 'facebook', 'twitter', 'linkedin', 'tiktok']),\r\n  postType: z.enum(['post', 'story', 'reel', 'advertisement']),\r\n  content: z.object({\r\n    text: z.string().min(1, \"Content text cannot be empty\"),\r\n    hashtags: z.array(z.string()).optional(),\r\n    mentions: z.array(z.string()).optional(),\r\n    imageUrl: z.string().optional(),\r\n    videoUrl: z.string().optional(),\r\n  }),\r\n  metadata: z.object({\r\n    businessType: z.string().optional(),\r\n    visualStyle: z.string().optional(),\r\n    targetAudience: z.string().optional(),\r\n    generationPrompt: z.string().optional(),\r\n    aiModel: z.string().optional(),\r\n  }),\r\n  analytics: z.object({\r\n    qualityScore: z.number().min(0).max(100).optional(),\r\n    engagementPrediction: z.number().min(0).max(100).optional(),\r\n    brandAlignmentScore: z.number().min(0).max(100).optional(),\r\n    views: z.number().default(0),\r\n    likes: z.number().default(0),\r\n    shares: z.number().default(0),\r\n    comments: z.number().default(0),\r\n  }).optional(),\r\n  status: z.enum(['draft', 'scheduled', 'published', 'archived']).default('draft'),\r\n  scheduledAt: z.union([z.date(), z.custom<Timestamp>()]).optional(),\r\n  publishedAt: z.union([z.date(), z.custom<Timestamp>()]).optional(),\r\n});\r\n\r\n// Artifact document schema\r\nexport const ArtifactDocumentSchema = BaseDocumentSchema.extend({\r\n  name: z.string(),\r\n  type: z.enum(['image', 'video', 'document', 'text']),\r\n  category: z.enum(['exact-use', 'reference']),\r\n  usageType: z.enum(['exact', 'reference']),\r\n  uploadType: z.enum(['file', 'text', 'url']),\r\n  folderId: z.string().optional(),\r\n  isActive: z.boolean().default(true),\r\n  instructions: z.string().optional(),\r\n  textOverlay: z.object({\r\n    headline: z.string().optional(),\r\n    message: z.string().optional(),\r\n    cta: z.string().optional(),\r\n    contact: z.string().optional(),\r\n  }).optional(),\r\n  filePath: z.string().optional(),\r\n  thumbnailPath: z.string().optional(),\r\n  fileUrl: z.string().optional(),\r\n  thumbnailUrl: z.string().optional(),\r\n  metadata: z.object({\r\n    fileSize: z.number().optional(),\r\n    mimeType: z.string().optional(),\r\n    dimensions: z.object({\r\n      width: z.number(),\r\n      height: z.number(),\r\n    }).optional(),\r\n    duration: z.number().optional(),\r\n  }).optional(),\r\n  tags: z.array(z.string()).default([]),\r\n  usage: z.object({\r\n    usageCount: z.number().default(0),\r\n    usedInContexts: z.array(z.string()).default([]),\r\n    lastUsedAt: z.union([z.date(), z.custom<Timestamp>()]).optional(),\r\n  }),\r\n  discountInfo: z.object({\r\n    hasDiscount: z.boolean().default(false),\r\n    discountPercentage: z.number().optional(),\r\n    discountText: z.string().optional(),\r\n    validUntil: z.union([z.date(), z.custom<Timestamp>()]).optional(),\r\n  }).optional(),\r\n});\r\n\r\n// Design Analytics document schema\r\nexport const DesignAnalyticsDocumentSchema = BaseDocumentSchema.extend({\r\n  designId: z.string(),\r\n  businessType: z.string(),\r\n  platform: z.string(),\r\n  visualStyle: z.string(),\r\n  generatedAt: z.union([z.date(), z.custom<Timestamp>()]),\r\n  metrics: z.object({\r\n    qualityScore: z.number().min(1).max(10),\r\n    engagementPrediction: z.number().min(1).max(10),\r\n    brandAlignmentScore: z.number().min(1).max(10),\r\n    technicalQuality: z.number().min(1).max(10),\r\n    trendRelevance: z.number().min(1).max(10),\r\n  }),\r\n  designElements: z.object({\r\n    colorPalette: z.array(z.string()),\r\n    typography: z.string(),\r\n    composition: z.string(),\r\n    trends: z.array(z.string()),\r\n    businessDNA: z.string(),\r\n  }),\r\n  tags: z.array(z.string()).default([]),\r\n  performance: z.object({\r\n    actualEngagement: z.number().optional(),\r\n    actualReach: z.number().optional(),\r\n    conversionRate: z.number().optional(),\r\n    updatedAt: z.union([z.date(), z.custom<Timestamp>()]).optional(),\r\n  }).optional(),\r\n});\r\n\r\n// Content Calendar document schema\r\nexport const ContentCalendarDocumentSchema = BaseDocumentSchema.extend({\r\n  brandProfileId: z.string(),\r\n  date: z.union([z.date(), z.custom<Timestamp>()]),\r\n  services: z.array(z.object({\r\n    serviceId: z.string(),\r\n    serviceName: z.string(),\r\n    description: z.string().optional(),\r\n    priority: z.enum(['low', 'medium', 'high']).default('medium'),\r\n  })),\r\n  generatedPosts: z.array(z.string()).default([]), // Array of post IDs\r\n  notes: z.string().optional(),\r\n  status: z.enum(['planned', 'in-progress', 'completed', 'cancelled']).default('planned'),\r\n});\r\n\r\n// Export types\r\nexport type UserDocument = z.infer<typeof UserDocumentSchema>;\r\nexport type BrandProfileDocument = z.infer<typeof BrandProfileDocumentSchema>;\r\nexport type GeneratedPostDocument = z.infer<typeof GeneratedPostDocumentSchema>;\r\nexport type ArtifactDocument = z.infer<typeof ArtifactDocumentSchema>;\r\nexport type DesignAnalyticsDocument = z.infer<typeof DesignAnalyticsDocumentSchema>;\r\nexport type ContentCalendarDocument = z.infer<typeof ContentCalendarDocumentSchema>;\r\n\r\n// Collection names\r\nexport const COLLECTIONS = {\r\n  USERS: 'users',\r\n  BRAND_PROFILES: 'brandProfiles',\r\n  GENERATED_POSTS: 'generatedPosts',\r\n  ARTIFACTS: 'artifacts',\r\n  DESIGN_ANALYTICS: 'designAnalytics',\r\n  CONTENT_CALENDAR: 'contentCalendar',\r\n} as const;\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;;;;;;;AACxC;;AAIO,MAAM,qBAAqB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,IAAI,oIAAA,CAAA,IAAC,CAAC,MAAM;IACZ,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM;IAChB,WAAW,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,oIAAA,CAAA,IAAC,CAAC,IAAI;QAAI,oIAAA,CAAA,IAAC,CAAC,MAAM;KAAc;IACpD,WAAW,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,oIAAA,CAAA,IAAC,CAAC,IAAI;QAAI,oIAAA,CAAA,IAAC,CAAC,MAAM;KAAc;AACtD;AAGO,MAAM,qBAAqB,mBAAmB,MAAM,CAAC;IAC1D,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK;IACvB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;IACnC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,OAAO,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAS;YAAQ;SAAS,EAAE,OAAO,CAAC;QACnD,eAAe,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QACnC,UAAU,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAChC,GAAG,QAAQ;IACX,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,MAAM,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAO;SAAa,EAAE,OAAO,CAAC;QACpD,QAAQ,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAU;YAAY;SAAY,EAAE,OAAO,CAAC;QAC5D,WAAW,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;YAAC,oIAAA,CAAA,IAAC,CAAC,IAAI;YAAI,oIAAA,CAAA,IAAC,CAAC,MAAM;SAAc,EAAE,QAAQ;IAChE,GAAG,QAAQ;AACb;AAGO,MAAM,6BAA6B,mBAAmB,MAAM,CAAC;IAClE,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM;IACd,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC9B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,GAAG,QAAQ;IACX,aAAa,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IACzC,YAAY,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IACxC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnC,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM;QACd,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;QACrB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,IAAI,QAAQ;IACZ,gBAAgB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC/B,KAAK,oIAAA,CAAA,IAAC,CAAC,MAAM;QACb,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAChC,MAAM,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;YAAQ;YAAS;SAAQ;IAC3D,IAAI,QAAQ;IACZ,YAAY,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAChC,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;AAC9B;AAGO,MAAM,8BAA8B,mBAAmB,MAAM,CAAC;IACnE,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM;IACxB,UAAU,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAa;QAAY;QAAW;QAAY;KAAS;IAC3E,UAAU,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAS;QAAQ;KAAgB;IAC3D,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACxB,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;QACtC,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;QACtC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B;IACA,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACjB,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACjC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAChC,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B;IACA,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAClB,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,QAAQ;QACjD,sBAAsB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,QAAQ;QACzD,qBAAqB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,QAAQ;QACxD,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;QAC1B,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;QAC1B,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;QAC3B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC/B,GAAG,QAAQ;IACX,QAAQ,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAa;QAAa;KAAW,EAAE,OAAO,CAAC;IACxE,aAAa,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,oIAAA,CAAA,IAAC,CAAC,IAAI;QAAI,oIAAA,CAAA,IAAC,CAAC,MAAM;KAAc,EAAE,QAAQ;IAChE,aAAa,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,oIAAA,CAAA,IAAC,CAAC,IAAI;QAAI,oIAAA,CAAA,IAAC,CAAC,MAAM;KAAc,EAAE,QAAQ;AAClE;AAGO,MAAM,yBAAyB,mBAAmB,MAAM,CAAC;IAC9D,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM;IACd,MAAM,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAS;QAAY;KAAO;IACnD,UAAU,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAa;KAAY;IAC3C,WAAW,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;KAAY;IACxC,YAAY,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAQ;KAAM;IAC1C,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,UAAU,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,KAAK,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxB,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,GAAG,QAAQ;IACX,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,eAAe,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACjB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACnB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM;YACf,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,GAAG,QAAQ;QACX,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,GAAG,QAAQ;IACX,MAAM,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACpC,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACd,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;QAC/B,gBAAgB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QAC9C,YAAY,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;YAAC,oIAAA,CAAA,IAAC,CAAC,IAAI;YAAI,oIAAA,CAAA,IAAC,CAAC,MAAM;SAAc,EAAE,QAAQ;IACjE;IACA,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,aAAa,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QACjC,oBAAoB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACvC,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACjC,YAAY,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;YAAC,oIAAA,CAAA,IAAC,CAAC,IAAI;YAAI,oIAAA,CAAA,IAAC,CAAC,MAAM;SAAc,EAAE,QAAQ;IACjE,GAAG,QAAQ;AACb;AAGO,MAAM,gCAAgC,mBAAmB,MAAM,CAAC;IACrE,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;IAClB,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM;IACtB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;IAClB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;IACrB,aAAa,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,oIAAA,CAAA,IAAC,CAAC,IAAI;QAAI,oIAAA,CAAA,IAAC,CAAC,MAAM;KAAc;IACtD,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACpC,sBAAsB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAC5C,qBAAqB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAC3C,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACxC,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACxC;IACA,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACvB,cAAc,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM;QAC9B,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM;QACpB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;QACrB,QAAQ,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM;QACxB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;IACvB;IACA,MAAM,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACpC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAChC,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,WAAW,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;YAAC,oIAAA,CAAA,IAAC,CAAC,IAAI;YAAI,oIAAA,CAAA,IAAC,CAAC,MAAM;SAAc,EAAE,QAAQ;IAChE,GAAG,QAAQ;AACb;AAGO,MAAM,gCAAgC,mBAAmB,MAAM,CAAC;IACrE,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM;IACxB,MAAM,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,oIAAA,CAAA,IAAC,CAAC,IAAI;QAAI,oIAAA,CAAA,IAAC,CAAC,MAAM;KAAc;IAC/C,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM;QACnB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;QACrB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAChC,UAAU,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAO;YAAU;SAAO,EAAE,OAAO,CAAC;IACtD;IACA,gBAAgB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IAC9C,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,QAAQ,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAe;QAAa;KAAY,EAAE,OAAO,CAAC;AAC/E;AAWO,MAAM,cAAc;IACzB,OAAO;IACP,gBAAgB;IAChB,iBAAiB;IACjB,WAAW;IACX,kBAAkB;IAClB,kBAAkB;AACpB", "debugId": null}}, {"offset": {"line": 2189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/firebase/database.ts"], "sourcesContent": ["// Firebase database service layer\r\nimport {\r\n  collection,\r\n  doc,\r\n  getDoc,\r\n  getDocs,\r\n  addDoc,\r\n  setDoc,\r\n  updateDoc,\r\n  deleteDoc,\r\n  query,\r\n  where,\r\n  orderBy,\r\n  limit,\r\n  startAfter,\r\n  onSnapshot,\r\n  serverTimestamp,\r\n  Timestamp,\r\n  DocumentSnapshot,\r\n  QuerySnapshot,\r\n  Unsubscribe,\r\n  WriteBatch,\r\n  writeBatch,\r\n} from 'firebase/firestore';\r\nimport { db } from './config';\r\nimport { COLLECTIONS } from './schema';\r\nimport type {\r\n  UserDocument,\r\n  BrandProfileDocument,\r\n  GeneratedPostDocument,\r\n  ArtifactDocument,\r\n  DesignAnalyticsDocument,\r\n  ContentCalendarDocument,\r\n} from './schema';\r\n\r\n// Generic database operations\r\nexport class DatabaseService<T extends { id: string; userId: string }> {\r\n  constructor(private collectionName: string) {}\r\n\r\n  // Create a new document\r\n  async create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\r\n    const docData = {\r\n      ...data,\r\n      createdAt: serverTimestamp(),\r\n      updatedAt: serverTimestamp(),\r\n    };\r\n\r\n    const docRef = await addDoc(collection(db, this.collectionName), docData);\r\n    return docRef.id;\r\n  }\r\n\r\n  // Create or set a document with a specific ID (useful for users so the doc ID == uid)\r\n  async createWithId(id: string, data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {\r\n    const docRef = doc(db, this.collectionName, id);\r\n    const docData = {\r\n      ...data,\r\n      createdAt: serverTimestamp(),\r\n      updatedAt: serverTimestamp(),\r\n    };\r\n\r\n    await setDoc(docRef, docData);\r\n  }\r\n\r\n  // Get a document by ID\r\n  async getById(id: string): Promise<T | null> {\r\n    const docRef = doc(db, this.collectionName, id);\r\n    const docSnap = await getDoc(docRef);\r\n    \r\n    if (docSnap.exists()) {\r\n      return {\r\n        id: docSnap.id,\r\n        ...docSnap.data(),\r\n      } as T;\r\n    }\r\n    \r\n    return null;\r\n  }\r\n\r\n  // Get documents by user ID\r\n  async getByUserId(\r\n    userId: string,\r\n    options?: {\r\n      limit?: number;\r\n      orderBy?: string;\r\n      orderDirection?: 'asc' | 'desc';\r\n      startAfter?: DocumentSnapshot;\r\n    }\r\n  ): Promise<T[]> {\r\n    let q = query(\r\n      collection(db, this.collectionName),\r\n      where('userId', '==', userId)\r\n    );\r\n\r\n    if (options?.orderBy) {\r\n      q = query(q, orderBy(options.orderBy, options.orderDirection || 'desc'));\r\n    }\r\n\r\n    if (options?.limit) {\r\n      q = query(q, limit(options.limit));\r\n    }\r\n\r\n    if (options?.startAfter) {\r\n      q = query(q, startAfter(options.startAfter));\r\n    }\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    return querySnapshot.docs.map(doc => ({\r\n      id: doc.id,\r\n      ...doc.data(),\r\n    })) as T[];\r\n  }\r\n\r\n  // Update a document\r\n  async update(id: string, data: Partial<Omit<T, 'id' | 'createdAt'>>): Promise<void> {\r\n    const docRef = doc(db, this.collectionName, id);\r\n    await updateDoc(docRef, {\r\n      ...data,\r\n      updatedAt: serverTimestamp(),\r\n    });\r\n  }\r\n\r\n  // Delete a document\r\n  async delete(id: string): Promise<void> {\r\n    const docRef = doc(db, this.collectionName, id);\r\n    await deleteDoc(docRef);\r\n  }\r\n\r\n  // Real-time listener for user documents\r\n  onUserDocumentsChange(\r\n    userId: string,\r\n    callback: (documents: T[]) => void,\r\n    options?: {\r\n      limit?: number;\r\n      orderBy?: string;\r\n      orderDirection?: 'asc' | 'desc';\r\n    }\r\n  ): Unsubscribe {\r\n    let q = query(\r\n      collection(db, this.collectionName),\r\n      where('userId', '==', userId)\r\n    );\r\n\r\n    if (options?.orderBy) {\r\n      q = query(q, orderBy(options.orderBy, options.orderDirection || 'desc'));\r\n    }\r\n\r\n    if (options?.limit) {\r\n      q = query(q, limit(options.limit));\r\n    }\r\n\r\n    return onSnapshot(q, (querySnapshot) => {\r\n      const documents = querySnapshot.docs.map(doc => ({\r\n        id: doc.id,\r\n        ...doc.data(),\r\n      })) as T[];\r\n      callback(documents);\r\n    });\r\n  }\r\n\r\n  // Real-time listener for a single document\r\n  onDocumentChange(id: string, callback: (document: T | null) => void): Unsubscribe {\r\n    const docRef = doc(db, this.collectionName, id);\r\n    return onSnapshot(docRef, (docSnap) => {\r\n      if (docSnap.exists()) {\r\n        callback({\r\n          id: docSnap.id,\r\n          ...docSnap.data(),\r\n        } as T);\r\n      } else {\r\n        callback(null);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Batch operations\r\n  createBatch(): WriteBatch {\r\n    return writeBatch(db);\r\n  }\r\n\r\n  async executeBatch(batch: WriteBatch): Promise<void> {\r\n    await batch.commit();\r\n  }\r\n}\r\n\r\n// Specific service instances\r\nexport const userService = new DatabaseService<UserDocument>(COLLECTIONS.USERS);\r\nexport const brandProfileService = new DatabaseService<BrandProfileDocument>(COLLECTIONS.BRAND_PROFILES);\r\nexport const generatedPostService = new DatabaseService<GeneratedPostDocument>(COLLECTIONS.GENERATED_POSTS);\r\nexport const artifactService = new DatabaseService<ArtifactDocument>(COLLECTIONS.ARTIFACTS);\r\nexport const designAnalyticsService = new DatabaseService<DesignAnalyticsDocument>(COLLECTIONS.DESIGN_ANALYTICS);\r\nexport const contentCalendarService = new DatabaseService<ContentCalendarDocument>(COLLECTIONS.CONTENT_CALENDAR);\r\n\r\n// Utility functions\r\nexport const convertTimestamp = (timestamp: Timestamp | Date): Date => {\r\n  if (timestamp instanceof Timestamp) {\r\n    return timestamp.toDate();\r\n  }\r\n  return timestamp;\r\n};\r\n\r\nexport const convertToFirestoreData = (data: any): any => {\r\n  if (data instanceof Date) {\r\n    return Timestamp.fromDate(data);\r\n  }\r\n  \r\n  if (Array.isArray(data)) {\r\n    return data.map(convertToFirestoreData);\r\n  }\r\n  \r\n  if (data && typeof data === 'object') {\r\n    const converted: any = {};\r\n    for (const [key, value] of Object.entries(data)) {\r\n      converted[key] = convertToFirestoreData(value);\r\n    }\r\n    return converted;\r\n  }\r\n  \r\n  return data;\r\n};\r\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;;;;;;;;AAClC;AAAA;AAuBA;AACA;;;;AAWO,MAAM;;IACX,YAAY,AAAQ,cAAsB,CAAE;aAAxB,iBAAA;IAAyB;IAE7C,wBAAwB;IACxB,MAAM,OAAO,IAA+C,EAAmB;QAC7E,MAAM,UAAU;YACd,GAAG,IAAI;YACP,WAAW,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;YACzB,WAAW,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;QAC3B;QAEA,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,IAAI,CAAC,cAAc,GAAG;QACjE,OAAO,OAAO,EAAE;IAClB;IAEA,sFAAsF;IACtF,MAAM,aAAa,EAAU,EAAE,IAA+C,EAAiB;QAC7F,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,IAAI,CAAC,cAAc,EAAE;QAC5C,MAAM,UAAU;YACd,GAAG,IAAI;YACP,WAAW,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;YACzB,WAAW,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;QAC3B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IACvB;IAEA,uBAAuB;IACvB,MAAM,QAAQ,EAAU,EAAqB;QAC3C,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,IAAI,CAAC,cAAc,EAAE;QAC5C,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO;gBACL,IAAI,QAAQ,EAAE;gBACd,GAAG,QAAQ,IAAI,EAAE;YACnB;QACF;QAEA,OAAO;IACT;IAEA,2BAA2B;IAC3B,MAAM,YACJ,MAAc,EACd,OAKC,EACa;QACd,IAAI,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACV,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,IAAI,CAAC,cAAc,GAClC,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM;QAGxB,IAAI,SAAS,SAAS;YACpB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO,EAAE,QAAQ,cAAc,IAAI;QAClE;QAEA,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,IAAI,SAAS,YAAY;YACvB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,UAAU;QAC5C;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH;IAEA,oBAAoB;IACpB,MAAM,OAAO,EAAU,EAAE,IAA0C,EAAiB;QAClF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,IAAI,CAAC,cAAc,EAAE;QAC5C,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;QAC3B;IACF;IAEA,oBAAoB;IACpB,MAAM,OAAO,EAAU,EAAiB;QACtC,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,IAAI,CAAC,cAAc,EAAE;QAC5C,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE;IAClB;IAEA,wCAAwC;IACxC,sBACE,MAAc,EACd,QAAkC,EAClC,OAIC,EACY;QACb,IAAI,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACV,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,IAAI,CAAC,cAAc,GAClC,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM;QAGxB,IAAI,SAAS,SAAS;YACpB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO,EAAE,QAAQ,cAAc,IAAI;QAClE;QAEA,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;YACpB,MAAM,YAAY,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC/C,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,SAAS;QACX;IACF;IAEA,2CAA2C;IAC3C,iBAAiB,EAAU,EAAE,QAAsC,EAAe;QAChF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,IAAI,CAAC,cAAc,EAAE;QAC5C,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,CAAC;YACzB,IAAI,QAAQ,MAAM,IAAI;gBACpB,SAAS;oBACP,IAAI,QAAQ,EAAE;oBACd,GAAG,QAAQ,IAAI,EAAE;gBACnB;YACF,OAAO;gBACL,SAAS;YACX;QACF;IACF;IAEA,mBAAmB;IACnB,cAA0B;QACxB,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE;IACtB;IAEA,MAAM,aAAa,KAAiB,EAAiB;QACnD,MAAM,MAAM,MAAM;IACpB;AACF;AAGO,MAAM,cAAc,IAAI,gBAA8B,gIAAA,CAAA,cAAW,CAAC,KAAK;AACvE,MAAM,sBAAsB,IAAI,gBAAsC,gIAAA,CAAA,cAAW,CAAC,cAAc;AAChG,MAAM,uBAAuB,IAAI,gBAAuC,gIAAA,CAAA,cAAW,CAAC,eAAe;AACnG,MAAM,kBAAkB,IAAI,gBAAkC,gIAAA,CAAA,cAAW,CAAC,SAAS;AACnF,MAAM,yBAAyB,IAAI,gBAAyC,gIAAA,CAAA,cAAW,CAAC,gBAAgB;AACxG,MAAM,yBAAyB,IAAI,gBAAyC,gIAAA,CAAA,cAAW,CAAC,gBAAgB;AAGxG,MAAM,mBAAmB,CAAC;IAC/B,IAAI,qBAAqB,iKAAA,CAAA,YAAS,EAAE;QAClC,OAAO,UAAU,MAAM;IACzB;IACA,OAAO;AACT;AAEO,MAAM,yBAAyB,CAAC;IACrC,IAAI,gBAAgB,MAAM;QACxB,OAAO,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;IAC5B;IAEA,IAAI,MAAM,OAAO,CAAC,OAAO;QACvB,OAAO,KAAK,GAAG,CAAC;IAClB;IAEA,IAAI,QAAQ,OAAO,SAAS,UAAU;QACpC,MAAM,YAAiB,CAAC;QACxB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,MAAO;YAC/C,SAAS,CAAC,IAAI,GAAG,uBAAuB;QAC1C;QACA,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/hooks/use-firebase-auth.ts"], "sourcesContent": ["// Firebase authentication hook\r\nimport { useState, useEffect } from 'react';\r\nimport {\r\n  User,\r\n  onAuthStateChanged,\r\n  signInAnonymously,\r\n  signInWithEmailAndPassword,\r\n  createUserWithEmailAndPassword,\r\n  signOut as firebaseSignOut,\r\n  updateProfile\r\n} from 'firebase/auth';\r\nimport { auth } from '@/lib/firebase/config';\r\nimport { userService } from '@/lib/firebase/database';\r\n\r\nexport interface AuthUser {\r\n  uid: string;\r\n  email: string | null;\r\n  displayName: string | null;\r\n  photoURL: string | null;\r\n  isAnonymous: boolean;\r\n}\r\n\r\nexport interface AuthState {\r\n  user: AuthUser | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\nexport function useFirebaseAuth() {\r\n  const [authState, setAuthState] = useState<AuthState>({\r\n    user: null,\r\n    loading: true,\r\n    error: null,\r\n  });\r\n\r\n  useEffect(() => {\r\n    // If Firebase auth is not available, do not create demo users — require explicit authentication\r\n    if (!auth) {\r\n      console.warn('Firebase auth not available; authentication is required. No demo user will be created.');\r\n      setAuthState({ user: null, loading: false, error: null });\r\n      return;\r\n    }\r\n\r\n    let mounted = true;\r\n\r\n    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {\r\n      if (!mounted) return;\r\n\r\n      if (firebaseUser) {\r\n        const userObj: AuthUser = {\r\n          uid: firebaseUser.uid,\r\n          email: firebaseUser.email,\r\n          displayName: firebaseUser.displayName,\r\n          photoURL: firebaseUser.photoURL,\r\n          isAnonymous: firebaseUser.isAnonymous,\r\n        };\r\n\r\n        // Ensure user document exists (best-effort)\r\n        try {\r\n          const existingUser = await userService.getById(firebaseUser.uid);\r\n          if (!existingUser) {\r\n            // Create the user document using the auth uid as the document id to avoid duplicate auto-id docs\r\n            await userService.createWithId(firebaseUser.uid, {\r\n              userId: firebaseUser.uid,\r\n              email: firebaseUser.email || '',\r\n              displayName: firebaseUser.displayName || '',\r\n              photoURL: firebaseUser.photoURL || '',\r\n              preferences: { theme: 'system', notifications: true, autoSave: true },\r\n              subscription: { plan: 'free', status: 'active' },\r\n            });\r\n          }\r\n        } catch (err) {\r\n          console.error('Failed to create/update user document:', err);\r\n        }\r\n\r\n        setAuthState({ user: userObj, loading: false, error: null });\r\n      } else {\r\n        // No user signed in — require explicit sign-in\r\n        if (!mounted) return;\r\n        setAuthState({ user: null, loading: false, error: null });\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      mounted = false;\r\n      unsubscribe();\r\n    };\r\n  }, []);\r\n\r\n  // Sign in anonymously (for demo/trial users) - wrapper\r\n  const signInAnonymous = async (): Promise<void> => {\r\n      if (!auth) {\r\n        const err = new Error('Firebase auth not initialized');\r\n        setAuthState((prev) => ({ ...prev, loading: false, error: err.message }));\r\n        throw err;\r\n      }\r\n\r\n      try {\r\n        setAuthState((prev) => ({ ...prev, loading: true, error: null }));\r\n        await signInAnonymously(auth);\r\n      } catch (error) {\r\n        setAuthState((prev) => ({ ...prev, loading: false, error: error instanceof Error ? error.message : 'Failed to sign in' }));\r\n        throw error;\r\n      }\r\n  };\r\n\r\n  // Sign in with email and password\r\n  const signIn = async (email: string, password: string): Promise<void> => {\r\n    try {\r\n      setAuthState(prev => ({ ...prev, loading: true, error: null }));\r\n      await signInWithEmailAndPassword(auth, email, password);\r\n    } catch (error) {\r\n      setAuthState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to sign in',\r\n      }));\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Sign up with email and password\r\n  const signUp = async (email: string, password: string, displayName?: string): Promise<void> => {\r\n    try {\r\n      setAuthState(prev => ({ ...prev, loading: true, error: null }));\r\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\r\n\r\n      if (displayName && userCredential.user) {\r\n        await updateProfile(userCredential.user, { displayName });\r\n      }\r\n    } catch (error) {\r\n      setAuthState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to sign up',\r\n      }));\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Sign out\r\n  const signOut = async (): Promise<void> => {\r\n    try {\r\n      await firebaseSignOut(auth);\r\n    } catch (error) {\r\n      console.error('Failed to sign out:', error);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Update user profile\r\n  const updateUserProfile = async (updates: {\r\n    displayName?: string;\r\n    photoURL?: string;\r\n  }): Promise<void> => {\r\n    if (!auth.currentUser) {\r\n      throw new Error('No user signed in');\r\n    }\r\n\r\n    try {\r\n      await updateProfile(auth.currentUser, updates);\r\n\r\n      // Update Firestore user document\r\n      await userService.update(auth.currentUser.uid, {\r\n        displayName: updates.displayName || auth.currentUser.displayName || '',\r\n        photoURL: updates.photoURL || auth.currentUser.photoURL || '',\r\n      });\r\n    } catch (error) {\r\n      console.error('Failed to update profile:', error);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  return {\r\n    ...authState,\r\n    signInAnonymous,\r\n    signIn,\r\n    signUp,\r\n    signOut,\r\n    updateUserProfile,\r\n  };\r\n}\r\n\r\n// Hook for getting current user ID\r\nexport function useUserId(): string | null {\r\n  const { user } = useFirebaseAuth();\r\n  return user?.uid || null;\r\n}\r\n\r\n// Hook for checking if user is authenticated\r\nexport function useIsAuthenticated(): boolean {\r\n  const { user, loading } = useFirebaseAuth();\r\n  return !loading && !!user;\r\n}\r\n\r\n// Hook for requiring authentication\r\nexport function useRequireAuth(): AuthUser {\r\n  const { user, loading } = useFirebaseAuth();\r\n\r\n  if (loading) {\r\n    throw new Promise(() => { }); // Suspend component until auth is loaded\r\n  }\r\n\r\n  if (!user) {\r\n    throw new Error('Authentication required');\r\n  }\r\n\r\n  return user;\r\n}\r\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;AAC/B;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;;;AAgBO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gGAAgG;QAChG,IAAI,CAAC,gIAAA,CAAA,OAAI,EAAE;YACT,QAAQ,IAAI,CAAC;YACb,aAAa;gBAAE,MAAM;gBAAM,SAAS;gBAAO,OAAO;YAAK;YACvD;QACF;QAEA,IAAI,UAAU;QAEd,MAAM,cAAc,CAAA,GAAA,oPAAA,CAAA,qBAAkB,AAAD,EAAE,gIAAA,CAAA,OAAI,EAAE,OAAO;YAClD,IAAI,CAAC,SAAS;YAEd,IAAI,cAAc;gBAChB,MAAM,UAAoB;oBACxB,KAAK,aAAa,GAAG;oBACrB,OAAO,aAAa,KAAK;oBACzB,aAAa,aAAa,WAAW;oBACrC,UAAU,aAAa,QAAQ;oBAC/B,aAAa,aAAa,WAAW;gBACvC;gBAEA,4CAA4C;gBAC5C,IAAI;oBACF,MAAM,eAAe,MAAM,kIAAA,CAAA,cAAW,CAAC,OAAO,CAAC,aAAa,GAAG;oBAC/D,IAAI,CAAC,cAAc;wBACjB,iGAAiG;wBACjG,MAAM,kIAAA,CAAA,cAAW,CAAC,YAAY,CAAC,aAAa,GAAG,EAAE;4BAC/C,QAAQ,aAAa,GAAG;4BACxB,OAAO,aAAa,KAAK,IAAI;4BAC7B,aAAa,aAAa,WAAW,IAAI;4BACzC,UAAU,aAAa,QAAQ,IAAI;4BACnC,aAAa;gCAAE,OAAO;gCAAU,eAAe;gCAAM,UAAU;4BAAK;4BACpE,cAAc;gCAAE,MAAM;gCAAQ,QAAQ;4BAAS;wBACjD;oBACF;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,0CAA0C;gBAC1D;gBAEA,aAAa;oBAAE,MAAM;oBAAS,SAAS;oBAAO,OAAO;gBAAK;YAC5D,OAAO;gBACL,+CAA+C;gBAC/C,IAAI,CAAC,SAAS;gBACd,aAAa;oBAAE,MAAM;oBAAM,SAAS;oBAAO,OAAO;gBAAK;YACzD;QACF;QAEA,OAAO;YACL,UAAU;YACV;QACF;IACF,GAAG,EAAE;IAEL,uDAAuD;IACvD,MAAM,kBAAkB;QACpB,IAAI,CAAC,gIAAA,CAAA,OAAI,EAAE;YACT,MAAM,MAAM,IAAI,MAAM;YACtB,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAO,OAAO,IAAI,OAAO;gBAAC,CAAC;YACvE,MAAM;QACR;QAEA,IAAI;YACF,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YAC/D,MAAM,CAAA,GAAA,oPAAA,CAAA,oBAAiB,AAAD,EAAE,gIAAA,CAAA,OAAI;QAC9B,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAO,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAAoB,CAAC;YACxH,MAAM;QACR;IACJ;IAEA,kCAAkC;IAClC,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,aAAa,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YAC7D,MAAM,CAAA,GAAA,6PAAA,CAAA,6BAA0B,AAAD,EAAE,gIAAA,CAAA,OAAI,EAAE,OAAO;QAChD,EAAE,OAAO,OAAO;YACd,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;YACD,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,aAAa,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YAC7D,MAAM,iBAAiB,MAAM,CAAA,GAAA,iQAAA,CAAA,iCAA8B,AAAD,EAAE,gIAAA,CAAA,OAAI,EAAE,OAAO;YAEzE,IAAI,eAAe,eAAe,IAAI,EAAE;gBACtC,MAAM,CAAA,GAAA,gPAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,IAAI,EAAE;oBAAE;gBAAY;YACzD;QACF,EAAE,OAAO,OAAO;YACd,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;YACD,MAAM;QACR;IACF;IAEA,WAAW;IACX,MAAM,UAAU;QACd,IAAI;YACF,MAAM,CAAA,GAAA,yOAAA,CAAA,UAAe,AAAD,EAAE,gIAAA,CAAA,OAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,OAAO;QAI/B,IAAI,CAAC,gIAAA,CAAA,OAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,gPAAA,CAAA,gBAAa,AAAD,EAAE,gIAAA,CAAA,OAAI,CAAC,WAAW,EAAE;YAEtC,iCAAiC;YACjC,MAAM,kIAAA,CAAA,cAAW,CAAC,MAAM,CAAC,gIAAA,CAAA,OAAI,CAAC,WAAW,CAAC,GAAG,EAAE;gBAC7C,aAAa,QAAQ,WAAW,IAAI,gIAAA,CAAA,OAAI,CAAC,WAAW,CAAC,WAAW,IAAI;gBACpE,UAAU,QAAQ,QAAQ,IAAI,gIAAA,CAAA,OAAI,CAAC,WAAW,CAAC,QAAQ,IAAI;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,OAAO;QACL,GAAG,SAAS;QACZ;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,OAAO,MAAM,OAAO;AACtB;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAC1B,OAAO,CAAC,WAAW,CAAC,CAAC;AACvB;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAE1B,IAAI,SAAS;QACX,MAAM,IAAI,QAAQ,KAAQ,IAAI,yCAAyC;IACzE;IAEA,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2569, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/firebase/services/brand-profile-firebase-first.ts"], "sourcesContent": ["// Firebase-first brand profile service\r\n// This service uses Firebase as the single source of truth with localStorage as cache only\r\n\r\nimport {\r\n  collection,\r\n  doc,\r\n  setDoc,\r\n  getDoc,\r\n  getDocs,\r\n  deleteDoc,\r\n  query,\r\n  where,\r\n  orderBy,\r\n  onSnapshot,\r\n  Timestamp,\r\n  limit\r\n} from 'firebase/firestore';\r\nimport { db } from '../config';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\n\r\nconst COLLECTION_NAME = 'brandProfiles';\r\nconst CACHE_KEY_PREFIX = 'brandProfileCache';\r\nconst CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes\r\n\r\ninterface CachedData {\r\n  data: CompleteBrandProfile;\r\n  timestamp: number;\r\n}\r\n\r\n// Cache management functions\r\nfunction getCachedProfile(userId: string): CompleteBrandProfile | null {\r\n  try {\r\n    const cached = localStorage.getItem(`${CACHE_KEY_PREFIX}_${userId}`);\r\n    if (!cached) return null;\r\n\r\n    const { data, timestamp }: CachedData = JSON.parse(cached);\r\n    if (Date.now() - timestamp > CACHE_EXPIRY) {\r\n      localStorage.removeItem(`${CACHE_KEY_PREFIX}_${userId}`);\r\n      return null;\r\n    }\r\n\r\n    console.log('📦 Using cached profile for user:', userId);\r\n    return data;\r\n  } catch (error) {\r\n    console.warn('Failed to get cached profile:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\nfunction setCachedProfile(userId: string, profile: CompleteBrandProfile): void {\r\n  try {\r\n    const cacheData: CachedData = {\r\n      data: profile,\r\n      timestamp: Date.now()\r\n    };\r\n    localStorage.setItem(`${CACHE_KEY_PREFIX}_${userId}`, JSON.stringify(cacheData));\r\n    console.log('📦 Cached profile for user:', userId);\r\n  } catch (error) {\r\n    console.warn('Failed to cache profile:', error);\r\n  }\r\n}\r\n\r\nfunction clearCachedProfile(userId: string): void {\r\n  try {\r\n    localStorage.removeItem(`${CACHE_KEY_PREFIX}_${userId}`);\r\n    console.log('🗑️ Cleared cached profile for user:', userId);\r\n  } catch (error) {\r\n    console.warn('Failed to clear cached profile:', error);\r\n  }\r\n}\r\n\r\n// Helper function to clean undefined values and empty objects\r\nfunction cleanObject(obj: any): any {\r\n  if (obj === null || obj === undefined) return '';\r\n  if (typeof obj === 'string') return obj.trim();\r\n  if (typeof obj !== 'object') return obj;\r\n\r\n  const cleaned: any = {};\r\n  for (const [key, value] of Object.entries(obj)) {\r\n    if (value !== undefined && value !== null && value !== '') {\r\n      cleaned[key] = typeof value === 'object' ? cleanObject(value) : value;\r\n    }\r\n  }\r\n  return Object.keys(cleaned).length > 0 ? cleaned : '';\r\n}\r\n\r\n// Helper function to clean URLs\r\nfunction cleanUrl(url: string | undefined): string {\r\n  if (!url) return '';\r\n  const trimmed = url.trim();\r\n  if (!trimmed) return '';\r\n\r\n  // Add https:// if no protocol is specified\r\n  if (!trimmed.startsWith('http://') && !trimmed.startsWith('https://')) {\r\n    return `https://${trimmed}`;\r\n  }\r\n  return trimmed;\r\n}\r\n\r\n// Save brand profile to Firebase (primary storage)\r\nexport async function saveBrandProfileFirebaseFirst(profile: CompleteBrandProfile, userId: string): Promise<string> {\r\n  try {\r\n    console.log('🔄 Saving brand profile to Firebase (Firebase-first):', profile.businessName);\r\n\r\n    // Create the document data\r\n    const data = {\r\n      userId,\r\n      name: profile.businessName || 'Untitled Business',\r\n      businessType: profile.businessType || 'General',\r\n      description: profile.businessDescription || profile.description || '',\r\n      location: cleanObject(profile.location),\r\n      website: cleanUrl(profile.websiteUrl || (profile as any).website),\r\n      logoDataUrl: profile.logoDataUrl || '', // Logo support - this is key!\r\n      // Brand colors - essential for brand consistency\r\n      primaryColor: profile.primaryColor || '#3B82F6',\r\n      accentColor: profile.accentColor || '#10B981',\r\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\r\n      socialMedia: cleanObject({\r\n        instagram: profile.socialMedia?.instagram || '',\r\n        facebook: profile.socialMedia?.facebook || '',\r\n        twitter: profile.socialMedia?.twitter || '',\r\n        linkedin: profile.socialMedia?.linkedin || '',\r\n        tiktok: profile.socialMedia?.tiktok || '',\r\n      }),\r\n      brandColors: Array.isArray(profile.brandColors) ? profile.brandColors.filter(c => c) : [],\r\n      brandFonts: Array.isArray(profile.brandFonts) ? profile.brandFonts.filter(f => f) : [],\r\n      visualStyle: profile.visualStyle || '',\r\n      targetAudience: profile.targetAudience || '',\r\n      brandVoice: profile.brandVoice || '',\r\n\r\n      // Services\r\n      services: Array.isArray(profile.services) ? profile.services.map(service => ({\r\n        name: service.name || '',\r\n        description: service.description || '',\r\n        price: service.price || '',\r\n        category: service.category || ''\r\n      })) : [],\r\n\r\n      // Contact information\r\n      contactPhone: profile.contactPhone || '',\r\n      contactEmail: profile.contactEmail || '',\r\n      contactAddress: profile.contactAddress || '',\r\n\r\n      // Brand identity\r\n      writingTone: profile.writingTone || '',\r\n      contentThemes: profile.contentThemes || '',\r\n      keyFeatures: profile.keyFeatures || '',\r\n      competitiveAdvantages: profile.competitiveAdvantages || '',\r\n\r\n      // Design examples\r\n      designExamples: Array.isArray(profile.designExamples) ? profile.designExamples : [],\r\n\r\n      // Metadata\r\n      isComplete: true,\r\n      version: profile.version || '1.0',\r\n      createdAt: profile.createdAt ? Timestamp.fromDate(new Date(profile.createdAt)) : Timestamp.now(),\r\n      updatedAt: Timestamp.now(),\r\n    };\r\n\r\n    // Use existing ID or generate new one\r\n    const docId = profile.id || doc(collection(db, COLLECTION_NAME)).id;\r\n    const docRef = doc(db, COLLECTION_NAME, docId);\r\n\r\n    // Save to Firebase first (primary storage)\r\n    await setDoc(docRef, data, { merge: true });\r\n    console.log('✅ Brand profile saved to Firebase successfully');\r\n\r\n    // Update the profile with the ID and timestamps\r\n    const savedProfile: CompleteBrandProfile = {\r\n      ...profile,\r\n      id: docId,\r\n      createdAt: data.createdAt.toDate().toISOString(),\r\n      updatedAt: data.updatedAt.toDate().toISOString(),\r\n    };\r\n\r\n    // Cache the saved profile for performance\r\n    setCachedProfile(userId, savedProfile);\r\n\r\n    return docId;\r\n  } catch (error) {\r\n    console.error('❌ Failed to save brand profile to Firebase:', error);\r\n    throw new Error(`Failed to save brand profile: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n// Load brand profile from Firebase (primary storage) with cache fallback\r\nexport async function loadBrandProfileFirebaseFirst(userId: string): Promise<CompleteBrandProfile | null> {\r\n  try {\r\n    console.log('🔄 Loading brand profile from Firebase (Firebase-first) for user:', userId);\r\n\r\n    // Try to get from cache first for performance\r\n    const cached = getCachedProfile(userId);\r\n    if (cached) {\r\n      // Still try to update from Firebase in background, but return cached data immediately\r\n      loadFromFirebaseInBackground(userId);\r\n      return cached;\r\n    }\r\n\r\n    // Load from Firebase (primary storage)\r\n    return await loadFromFirebase(userId);\r\n  } catch (error) {\r\n    console.error('❌ Failed to load from Firebase, trying cache:', error);\r\n\r\n    // Fallback to cache if Firebase fails\r\n    const cached = getCachedProfile(userId);\r\n    if (cached) {\r\n      console.log('📦 Using cached profile as fallback');\r\n      return cached;\r\n    }\r\n\r\n    console.error('❌ No cached profile available');\r\n    return null;\r\n  }\r\n}\r\n\r\n// Load from Firebase and update cache\r\nasync function loadFromFirebase(userId: string): Promise<CompleteBrandProfile | null> {\r\n  const q = query(\r\n    collection(db, COLLECTION_NAME),\r\n    where('userId', '==', userId),\r\n    orderBy('updatedAt', 'desc'),\r\n    limit(1)\r\n  );\r\n\r\n  const querySnapshot = await getDocs(q);\r\n  if (querySnapshot.empty) {\r\n    console.log('📭 No brand profile found in Firebase for user:', userId);\r\n    return null;\r\n  }\r\n\r\n  const doc = querySnapshot.docs[0];\r\n  const data = doc.data();\r\n\r\n  const profile = convertFirebaseToProfile(doc.id, data);\r\n\r\n  // Cache the loaded profile\r\n  setCachedProfile(userId, profile);\r\n\r\n  console.log('✅ Brand profile loaded from Firebase:', profile.businessName);\r\n  return profile;\r\n}\r\n\r\n// Background loading to update cache\r\nasync function loadFromFirebaseInBackground(userId: string): Promise<void> {\r\n  try {\r\n    await loadFromFirebase(userId);\r\n  } catch (error) {\r\n    console.warn('Background Firebase load failed:', error);\r\n  }\r\n}\r\n\r\n// Convert Firebase document to CompleteBrandProfile\r\nfunction convertFirebaseToProfile(id: string, data: any): CompleteBrandProfile {\r\n  return {\r\n    id,\r\n    businessName: data.name || '',\r\n    businessType: data.businessType || '',\r\n    businessDescription: data.description || '',\r\n    description: data.description || '',\r\n    location: data.location || '',\r\n    websiteUrl: data.website || '',\r\n    logoDataUrl: data.logoDataUrl || '', // Important: preserve logo data\r\n\r\n    // Social media\r\n    socialMedia: data.socialMedia || {},\r\n\r\n    // Brand colors - individual color properties for brand consistency\r\n    primaryColor: data.primaryColor || '#3B82F6',\r\n    accentColor: data.accentColor || '#10B981',\r\n    backgroundColor: data.backgroundColor || '#F8FAFC',\r\n\r\n    // Brand identity\r\n    brandColors: data.brandColors || [],\r\n    brandFonts: data.brandFonts || [],\r\n    visualStyle: data.visualStyle || '',\r\n    targetAudience: data.targetAudience || '',\r\n    brandVoice: data.brandVoice || '',\r\n    writingTone: data.writingTone || '',\r\n    contentThemes: data.contentThemes || '',\r\n    keyFeatures: data.keyFeatures || '',\r\n    competitiveAdvantages: data.competitiveAdvantages || '',\r\n\r\n    // Services\r\n    services: data.services || [],\r\n\r\n    // Contact information\r\n    contactPhone: data.contactPhone || '',\r\n    contactEmail: data.contactEmail || '',\r\n    contactAddress: data.contactAddress || '',\r\n\r\n    // Design examples\r\n    designExamples: data.designExamples || [],\r\n\r\n    // Metadata\r\n    isComplete: data.isComplete || false,\r\n    version: data.version || '1.0',\r\n    createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),\r\n    updatedAt: data.updatedAt?.toDate?.()?.toISOString() || new Date().toISOString(),\r\n  };\r\n}\r\n\r\n// Get all brand profiles for a user\r\nexport async function getUserBrandProfilesFirebaseFirst(userId: string): Promise<CompleteBrandProfile[]> {\r\n  try {\r\n    console.log('🔄 Loading all brand profiles from Firebase for user:', userId);\r\n\r\n    const q = query(\r\n      collection(db, COLLECTION_NAME),\r\n      where('userId', '==', userId),\r\n      orderBy('updatedAt', 'desc')\r\n    );\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    const profiles = querySnapshot.docs.map(doc =>\r\n      convertFirebaseToProfile(doc.id, doc.data())\r\n    );\r\n\r\n    console.log(`✅ Loaded ${profiles.length} brand profiles from Firebase`);\r\n    return profiles;\r\n  } catch (error) {\r\n    console.error('❌ Failed to load brand profiles from Firebase:', error);\r\n    return [];\r\n  }\r\n}\r\n\r\n// Delete brand profile from Firebase and clear cache\r\nexport async function deleteBrandProfileFirebaseFirst(profileId: string, userId: string): Promise<void> {\r\n  try {\r\n    console.log('🗑️ Deleting brand profile from Firebase:', profileId);\r\n\r\n    // Delete from Firebase\r\n    await deleteDoc(doc(db, COLLECTION_NAME, profileId));\r\n\r\n    // Clear cache\r\n    clearCachedProfile(userId);\r\n\r\n    console.log('✅ Brand profile deleted successfully');\r\n  } catch (error) {\r\n    console.error('❌ Failed to delete brand profile:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Clear all cached data (useful for logout)\r\nexport function clearAllCachedProfiles(): void {\r\n  try {\r\n    const keys = Object.keys(localStorage);\r\n    keys.forEach(key => {\r\n      if (key.startsWith(CACHE_KEY_PREFIX)) {\r\n        localStorage.removeItem(key);\r\n      }\r\n    });\r\n    console.log('🗑️ Cleared all cached brand profiles');\r\n  } catch (error) {\r\n    console.warn('Failed to clear cached profiles:', error);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,2FAA2F;;;;;;;;AAE3F;AAAA;AAcA;;;AAGA,MAAM,kBAAkB;AACxB,MAAM,mBAAmB;AACzB,MAAM,eAAe,IAAI,KAAK,MAAM,YAAY;AAOhD,6BAA6B;AAC7B,SAAS,iBAAiB,MAAc;IACtC,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC,GAAG,iBAAiB,CAAC,EAAE,QAAQ;QACnE,IAAI,CAAC,QAAQ,OAAO;QAEpB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAe,KAAK,KAAK,CAAC;QACnD,IAAI,KAAK,GAAG,KAAK,YAAY,cAAc;YACzC,aAAa,UAAU,CAAC,GAAG,iBAAiB,CAAC,EAAE,QAAQ;YACvD,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,qCAAqC;QACjD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,iCAAiC;QAC9C,OAAO;IACT;AACF;AAEA,SAAS,iBAAiB,MAAc,EAAE,OAA6B;IACrE,IAAI;QACF,MAAM,YAAwB;YAC5B,MAAM;YACN,WAAW,KAAK,GAAG;QACrB;QACA,aAAa,OAAO,CAAC,GAAG,iBAAiB,CAAC,EAAE,QAAQ,EAAE,KAAK,SAAS,CAAC;QACrE,QAAQ,GAAG,CAAC,+BAA+B;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;IAC3C;AACF;AAEA,SAAS,mBAAmB,MAAc;IACxC,IAAI;QACF,aAAa,UAAU,CAAC,GAAG,iBAAiB,CAAC,EAAE,QAAQ;QACvD,QAAQ,GAAG,CAAC,wCAAwC;IACtD,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,mCAAmC;IAClD;AACF;AAEA,8DAA8D;AAC9D,SAAS,YAAY,GAAQ;IAC3B,IAAI,QAAQ,QAAQ,QAAQ,WAAW,OAAO;IAC9C,IAAI,OAAO,QAAQ,UAAU,OAAO,IAAI,IAAI;IAC5C,IAAI,OAAO,QAAQ,UAAU,OAAO;IAEpC,MAAM,UAAe,CAAC;IACtB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;QAC9C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,OAAO,CAAC,IAAI,GAAG,OAAO,UAAU,WAAW,YAAY,SAAS;QAClE;IACF;IACA,OAAO,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,IAAI,UAAU;AACrD;AAEA,gCAAgC;AAChC,SAAS,SAAS,GAAuB;IACvC,IAAI,CAAC,KAAK,OAAO;IACjB,MAAM,UAAU,IAAI,IAAI;IACxB,IAAI,CAAC,SAAS,OAAO;IAErB,2CAA2C;IAC3C,IAAI,CAAC,QAAQ,UAAU,CAAC,cAAc,CAAC,QAAQ,UAAU,CAAC,aAAa;QACrE,OAAO,CAAC,QAAQ,EAAE,SAAS;IAC7B;IACA,OAAO;AACT;AAGO,eAAe,8BAA8B,OAA6B,EAAE,MAAc;IAC/F,IAAI;QACF,QAAQ,GAAG,CAAC,yDAAyD,QAAQ,YAAY;QAEzF,2BAA2B;QAC3B,MAAM,OAAO;YACX;YACA,MAAM,QAAQ,YAAY,IAAI;YAC9B,cAAc,QAAQ,YAAY,IAAI;YACtC,aAAa,QAAQ,mBAAmB,IAAI,QAAQ,WAAW,IAAI;YACnE,UAAU,YAAY,QAAQ,QAAQ;YACtC,SAAS,SAAS,QAAQ,UAAU,IAAI,AAAC,QAAgB,OAAO;YAChE,aAAa,QAAQ,WAAW,IAAI;YACpC,iDAAiD;YACjD,cAAc,QAAQ,YAAY,IAAI;YACtC,aAAa,QAAQ,WAAW,IAAI;YACpC,iBAAiB,QAAQ,eAAe,IAAI;YAC5C,aAAa,YAAY;gBACvB,WAAW,QAAQ,WAAW,EAAE,aAAa;gBAC7C,UAAU,QAAQ,WAAW,EAAE,YAAY;gBAC3C,SAAS,QAAQ,WAAW,EAAE,WAAW;gBACzC,UAAU,QAAQ,WAAW,EAAE,YAAY;gBAC3C,QAAQ,QAAQ,WAAW,EAAE,UAAU;YACzC;YACA,aAAa,MAAM,OAAO,CAAC,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,KAAK,EAAE;YACzF,YAAY,MAAM,OAAO,CAAC,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,CAAC,CAAA,IAAK,KAAK,EAAE;YACtF,aAAa,QAAQ,WAAW,IAAI;YACpC,gBAAgB,QAAQ,cAAc,IAAI;YAC1C,YAAY,QAAQ,UAAU,IAAI;YAElC,WAAW;YACX,UAAU,MAAM,OAAO,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;oBAC3E,MAAM,QAAQ,IAAI,IAAI;oBACtB,aAAa,QAAQ,WAAW,IAAI;oBACpC,OAAO,QAAQ,KAAK,IAAI;oBACxB,UAAU,QAAQ,QAAQ,IAAI;gBAChC,CAAC,KAAK,EAAE;YAER,sBAAsB;YACtB,cAAc,QAAQ,YAAY,IAAI;YACtC,cAAc,QAAQ,YAAY,IAAI;YACtC,gBAAgB,QAAQ,cAAc,IAAI;YAE1C,iBAAiB;YACjB,aAAa,QAAQ,WAAW,IAAI;YACpC,eAAe,QAAQ,aAAa,IAAI;YACxC,aAAa,QAAQ,WAAW,IAAI;YACpC,uBAAuB,QAAQ,qBAAqB,IAAI;YAExD,kBAAkB;YAClB,gBAAgB,MAAM,OAAO,CAAC,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,EAAE;YAEnF,WAAW;YACX,YAAY;YACZ,SAAS,QAAQ,OAAO,IAAI;YAC5B,WAAW,QAAQ,SAAS,GAAG,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ,SAAS,KAAK,iKAAA,CAAA,YAAS,CAAC,GAAG;YAC9F,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;QAEA,sCAAsC;QACtC,MAAM,QAAQ,QAAQ,EAAE,IAAI,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,kBAAkB,EAAE;QACnE,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,iBAAiB;QAExC,2CAA2C;QAC3C,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,MAAM;YAAE,OAAO;QAAK;QACzC,QAAQ,GAAG,CAAC;QAEZ,gDAAgD;QAChD,MAAM,eAAqC;YACzC,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,KAAK,SAAS,CAAC,MAAM,GAAG,WAAW;YAC9C,WAAW,KAAK,SAAS,CAAC,MAAM,GAAG,WAAW;QAChD;QAEA,0CAA0C;QAC1C,iBAAiB,QAAQ;QAEzB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC7G;AACF;AAGO,eAAe,8BAA8B,MAAc;IAChE,IAAI;QACF,QAAQ,GAAG,CAAC,qEAAqE;QAEjF,8CAA8C;QAC9C,MAAM,SAAS,iBAAiB;QAChC,IAAI,QAAQ;YACV,sFAAsF;YACtF,6BAA6B;YAC7B,OAAO;QACT;QAEA,uCAAuC;QACvC,OAAO,MAAM,iBAAiB;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAE/D,sCAAsC;QACtC,MAAM,SAAS,iBAAiB;QAChC,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,KAAK,CAAC;QACd,OAAO;IACT;AACF;AAEA,sCAAsC;AACtC,eAAe,iBAAiB,MAAc;IAC5C,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,kBACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,SACtB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,SACrB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE;IAGR,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;IACpC,IAAI,cAAc,KAAK,EAAE;QACvB,QAAQ,GAAG,CAAC,mDAAmD;QAC/D,OAAO;IACT;IAEA,MAAM,MAAM,cAAc,IAAI,CAAC,EAAE;IACjC,MAAM,OAAO,IAAI,IAAI;IAErB,MAAM,UAAU,yBAAyB,IAAI,EAAE,EAAE;IAEjD,2BAA2B;IAC3B,iBAAiB,QAAQ;IAEzB,QAAQ,GAAG,CAAC,yCAAyC,QAAQ,YAAY;IACzE,OAAO;AACT;AAEA,qCAAqC;AACrC,eAAe,6BAA6B,MAAc;IACxD,IAAI;QACF,MAAM,iBAAiB;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,oCAAoC;IACnD;AACF;AAEA,oDAAoD;AACpD,SAAS,yBAAyB,EAAU,EAAE,IAAS;IACrD,OAAO;QACL;QACA,cAAc,KAAK,IAAI,IAAI;QAC3B,cAAc,KAAK,YAAY,IAAI;QACnC,qBAAqB,KAAK,WAAW,IAAI;QACzC,aAAa,KAAK,WAAW,IAAI;QACjC,UAAU,KAAK,QAAQ,IAAI;QAC3B,YAAY,KAAK,OAAO,IAAI;QAC5B,aAAa,KAAK,WAAW,IAAI;QAEjC,eAAe;QACf,aAAa,KAAK,WAAW,IAAI,CAAC;QAElC,mEAAmE;QACnE,cAAc,KAAK,YAAY,IAAI;QACnC,aAAa,KAAK,WAAW,IAAI;QACjC,iBAAiB,KAAK,eAAe,IAAI;QAEzC,iBAAiB;QACjB,aAAa,KAAK,WAAW,IAAI,EAAE;QACnC,YAAY,KAAK,UAAU,IAAI,EAAE;QACjC,aAAa,KAAK,WAAW,IAAI;QACjC,gBAAgB,KAAK,cAAc,IAAI;QACvC,YAAY,KAAK,UAAU,IAAI;QAC/B,aAAa,KAAK,WAAW,IAAI;QACjC,eAAe,KAAK,aAAa,IAAI;QACrC,aAAa,KAAK,WAAW,IAAI;QACjC,uBAAuB,KAAK,qBAAqB,IAAI;QAErD,WAAW;QACX,UAAU,KAAK,QAAQ,IAAI,EAAE;QAE7B,sBAAsB;QACtB,cAAc,KAAK,YAAY,IAAI;QACnC,cAAc,KAAK,YAAY,IAAI;QACnC,gBAAgB,KAAK,cAAc,IAAI;QAEvC,kBAAkB;QAClB,gBAAgB,KAAK,cAAc,IAAI,EAAE;QAEzC,WAAW;QACX,YAAY,KAAK,UAAU,IAAI;QAC/B,SAAS,KAAK,OAAO,IAAI;QACzB,WAAW,KAAK,SAAS,EAAE,YAAY,iBAAiB,IAAI,OAAO,WAAW;QAC9E,WAAW,KAAK,SAAS,EAAE,YAAY,iBAAiB,IAAI,OAAO,WAAW;IAChF;AACF;AAGO,eAAe,kCAAkC,MAAc;IACpE,IAAI;QACF,QAAQ,GAAG,CAAC,yDAAyD;QAErE,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,kBACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,SACtB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAGvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MACtC,yBAAyB,IAAI,EAAE,EAAE,IAAI,IAAI;QAG3C,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,MAAM,CAAC,6BAA6B,CAAC;QACtE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,OAAO,EAAE;IACX;AACF;AAGO,eAAe,gCAAgC,SAAiB,EAAE,MAAc;IACrF,IAAI;QACF,QAAQ,GAAG,CAAC,6CAA6C;QAEzD,uBAAuB;QACvB,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,iBAAiB;QAEzC,cAAc;QACd,mBAAmB;QAEnB,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAGO,SAAS;IACd,IAAI;QACF,MAAM,OAAO,OAAO,IAAI,CAAC;QACzB,KAAK,OAAO,CAAC,CAAA;YACX,IAAI,IAAI,UAAU,CAAC,mBAAmB;gBACpC,aAAa,UAAU,CAAC;YAC1B;QACF;QACA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,oCAAoC;IACnD;AACF", "debugId": null}}, {"offset": {"line": 2857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/hooks/use-brand-profiles-firebase-first.ts"], "sourcesContent": ["// Firebase-first hook for managing brand profiles\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useUserId } from './use-firebase-auth';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\nimport {\r\n  saveBrandProfileFirebaseFirst,\r\n  loadBrandProfileFirebaseFirst,\r\n  getUserBrandProfilesFirebaseFirst,\r\n  deleteBrandProfileFirebaseFirst,\r\n  clearAllCachedProfiles\r\n} from '@/lib/firebase/services/brand-profile-firebase-first';\r\n\r\nexport interface BrandProfilesState {\r\n  profiles: CompleteBrandProfile[];\r\n  currentProfile: CompleteBrandProfile | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  saving: boolean;\r\n}\r\n\r\nexport function useBrandProfilesFirebaseFirst() {\r\n  const userId = useUserId();\r\n  const [state, setState] = useState<BrandProfilesState>({\r\n    profiles: [],\r\n    currentProfile: null,\r\n    loading: true,\r\n    error: null,\r\n    saving: false,\r\n  });\r\n\r\n  // Load profiles from Firebase (with cache)\r\n  const loadProfiles = useCallback(async () => {\r\n    if (!userId) {\r\n      setState(prev => ({ ...prev, loading: false, profiles: [], currentProfile: null }));\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n      console.log('🔄 Loading profiles with Firebase-first approach');\r\n\r\n      // Load all profiles\r\n      const profiles = await getUserBrandProfilesFirebaseFirst(userId);\r\n      \r\n      // Get the current/latest profile\r\n      const currentProfile = profiles.length > 0 ? profiles[0] : null;\r\n\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles,\r\n        currentProfile,\r\n        loading: false,\r\n      }));\r\n\r\n      console.log(`✅ Loaded ${profiles.length} profiles, current:`, currentProfile?.businessName);\r\n    } catch (error) {\r\n      console.error('❌ Failed to load profiles:', error);\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to load profiles',\r\n      }));\r\n    }\r\n  }, [userId]);\r\n\r\n  // Save profile to Firebase (primary storage)\r\n  const saveProfile = useCallback(async (profile: CompleteBrandProfile): Promise<string> => {\r\n    if (!userId) {\r\n      throw new Error('User not authenticated');\r\n    }\r\n\r\n    setState(prev => ({ ...prev, saving: true, error: null }));\r\n\r\n    try {\r\n      console.log('💾 Saving profile with Firebase-first approach:', profile.businessName);\r\n      \r\n      // Save to Firebase first\r\n      const profileId = await saveBrandProfileFirebaseFirst(profile, userId);\r\n      \r\n      // Update local state with the saved profile\r\n      const savedProfile = { ...profile, id: profileId };\r\n      \r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles: [savedProfile, ...prev.profiles.filter(p => p.id !== profileId)],\r\n        currentProfile: savedProfile,\r\n        saving: false,\r\n      }));\r\n\r\n      console.log('✅ Profile saved successfully:', profileId);\r\n      return profileId;\r\n    } catch (error) {\r\n      console.error('❌ Failed to save profile:', error);\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to save profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId]);\r\n\r\n  // Update existing profile\r\n  const updateProfile = useCallback(async (profileId: string, updates: Partial<CompleteBrandProfile>): Promise<void> => {\r\n    if (!userId) {\r\n      throw new Error('User not authenticated');\r\n    }\r\n\r\n    const existingProfile = state.profiles.find(p => p.id === profileId);\r\n    if (!existingProfile) {\r\n      throw new Error('Profile not found');\r\n    }\r\n\r\n    const updatedProfile = { ...existingProfile, ...updates };\r\n    await saveProfile(updatedProfile);\r\n  }, [userId, state.profiles, saveProfile]);\r\n\r\n  // Delete profile\r\n  const deleteProfile = useCallback(async (profileId: string): Promise<void> => {\r\n    if (!userId) {\r\n      throw new Error('User not authenticated');\r\n    }\r\n\r\n    setState(prev => ({ ...prev, saving: true, error: null }));\r\n\r\n    try {\r\n      console.log('🗑️ Deleting profile:', profileId);\r\n      \r\n      // Delete from Firebase\r\n      await deleteBrandProfileFirebaseFirst(profileId, userId);\r\n      \r\n      // Update local state\r\n      setState(prev => {\r\n        const updatedProfiles = prev.profiles.filter(p => p.id !== profileId);\r\n        const newCurrentProfile = prev.currentProfile?.id === profileId \r\n          ? (updatedProfiles.length > 0 ? updatedProfiles[0] : null)\r\n          : prev.currentProfile;\r\n        \r\n        return {\r\n          ...prev,\r\n          profiles: updatedProfiles,\r\n          currentProfile: newCurrentProfile,\r\n          saving: false,\r\n        };\r\n      });\r\n\r\n      console.log('✅ Profile deleted successfully');\r\n    } catch (error) {\r\n      console.error('❌ Failed to delete profile:', error);\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to delete profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId]);\r\n\r\n  // Set current profile\r\n  const setCurrentProfile = useCallback((profile: CompleteBrandProfile | null) => {\r\n    console.log('🎯 Setting current profile:', profile?.businessName || 'null');\r\n    setState(prev => ({ ...prev, currentProfile: profile }));\r\n  }, []);\r\n\r\n  // Get profile by ID\r\n  const getProfileById = useCallback((profileId: string): CompleteBrandProfile | null => {\r\n    return state.profiles.find(p => p.id === profileId) || null;\r\n  }, [state.profiles]);\r\n\r\n  // Clear all data (useful for logout)\r\n  const clearAllData = useCallback(() => {\r\n    console.log('🗑️ Clearing all brand profile data');\r\n    clearAllCachedProfiles();\r\n    setState({\r\n      profiles: [],\r\n      currentProfile: null,\r\n      loading: false,\r\n      error: null,\r\n      saving: false,\r\n    });\r\n  }, []);\r\n\r\n  // Load profiles when userId changes\r\n  useEffect(() => {\r\n    loadProfiles();\r\n  }, [loadProfiles]);\r\n\r\n  // Clear data when user logs out\r\n  useEffect(() => {\r\n    if (!userId) {\r\n      clearAllData();\r\n    }\r\n  }, [userId, clearAllData]);\r\n\r\n  return {\r\n    ...state,\r\n    saveProfile,\r\n    updateProfile,\r\n    deleteProfile,\r\n    setCurrentProfile,\r\n    getProfileById,\r\n    reload: loadProfiles,\r\n    clearAllData,\r\n  };\r\n}\r\n\r\n// Hook for checking if user has a complete brand profile\r\nexport function useHasCompleteBrandProfileFirebaseFirst(): boolean {\r\n  const { currentProfile, loading } = useBrandProfilesFirebaseFirst();\r\n\r\n  if (loading || !currentProfile) return false;\r\n\r\n  // Check if profile has required fields\r\n  const requiredFields = [\r\n    'businessName',\r\n    'businessType',\r\n    'location',\r\n    'description',\r\n    'services',\r\n  ];\r\n\r\n  return requiredFields.every(field => {\r\n    const value = currentProfile[field as keyof CompleteBrandProfile];\r\n    return value && (\r\n      typeof value === 'string' ? value.trim().length > 0 :\r\n        Array.isArray(value) ? value.length > 0 :\r\n          true\r\n    );\r\n  });\r\n}\r\n\r\n// Hook for getting the current brand profile with Firebase-first approach\r\nexport function useCurrentBrandProfileFirebaseFirst(): CompleteBrandProfile | null {\r\n  const { currentProfile } = useBrandProfilesFirebaseFirst();\r\n  return currentProfile;\r\n}\r\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;AAClD;AACA;AAEA;;;;AAgBO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QACrD,UAAU,EAAE;QACZ,gBAAgB;QAChB,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,2CAA2C;IAC3C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,QAAQ;YACX,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAO,UAAU,EAAE;oBAAE,gBAAgB;gBAAK,CAAC;YACjF;QACF;QAEA,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YACzD,QAAQ,GAAG,CAAC;YAEZ,oBAAoB;YACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2KAAA,CAAA,oCAAiC,AAAD,EAAE;YAEzD,iCAAiC;YACjC,MAAM,iBAAiB,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG;YAE3D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP;oBACA;oBACA,SAAS;gBACX,CAAC;YAED,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,MAAM,CAAC,mBAAmB,CAAC,EAAE,gBAAgB;QAChF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;QACH;IACF,GAAG;QAAC;KAAO;IAEX,6CAA6C;IAC7C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ;gBAAM,OAAO;YAAK,CAAC;QAExD,IAAI;YACF,QAAQ,GAAG,CAAC,mDAAmD,QAAQ,YAAY;YAEnF,yBAAyB;YACzB,MAAM,YAAY,MAAM,CAAA,GAAA,2KAAA,CAAA,gCAA6B,AAAD,EAAE,SAAS;YAE/D,4CAA4C;YAC5C,MAAM,eAAe;gBAAE,GAAG,OAAO;gBAAE,IAAI;YAAU;YAEjD,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,UAAU;wBAAC;2BAAiB,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;qBAAW;oBAC1E,gBAAgB;oBAChB,QAAQ;gBACV,CAAC;YAED,QAAQ,GAAG,CAAC,iCAAiC;YAC7C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,QAAQ;oBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;YACD,MAAM;QACR;IACF,GAAG;QAAC;KAAO;IAEX,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB;QAC1D,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,kBAAkB,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1D,IAAI,CAAC,iBAAiB;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,iBAAiB;YAAE,GAAG,eAAe;YAAE,GAAG,OAAO;QAAC;QACxD,MAAM,YAAY;IACpB,GAAG;QAAC;QAAQ,MAAM,QAAQ;QAAE;KAAY;IAExC,iBAAiB;IACjB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ;gBAAM,OAAO;YAAK,CAAC;QAExD,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB;YAErC,uBAAuB;YACvB,MAAM,CAAA,GAAA,2KAAA,CAAA,kCAA+B,AAAD,EAAE,WAAW;YAEjD,qBAAqB;YACrB,SAAS,CAAA;gBACP,MAAM,kBAAkB,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC3D,MAAM,oBAAoB,KAAK,cAAc,EAAE,OAAO,YACjD,gBAAgB,MAAM,GAAG,IAAI,eAAe,CAAC,EAAE,GAAG,OACnD,KAAK,cAAc;gBAEvB,OAAO;oBACL,GAAG,IAAI;oBACP,UAAU;oBACV,gBAAgB;oBAChB,QAAQ;gBACV;YACF;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,QAAQ;oBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;YACD,MAAM;QACR;IACF,GAAG;QAAC;KAAO;IAEX,sBAAsB;IACtB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,QAAQ,GAAG,CAAC,+BAA+B,SAAS,gBAAgB;QACpE,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,gBAAgB;YAAQ,CAAC;IACxD,GAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc;IACzD,GAAG;QAAC,MAAM,QAAQ;KAAC;IAEnB,qCAAqC;IACrC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,QAAQ,GAAG,CAAC;QACZ,CAAA,GAAA,2KAAA,CAAA,yBAAsB,AAAD;QACrB,SAAS;YACP,UAAU,EAAE;YACZ,gBAAgB;YAChB,SAAS;YACT,OAAO;YACP,QAAQ;QACV;IACF,GAAG,EAAE;IAEL,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAa;IAEjB,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;YACX;QACF;IACF,GAAG;QAAC;QAAQ;KAAa;IAEzB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA,QAAQ;QACR;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG;IAEpC,IAAI,WAAW,CAAC,gBAAgB,OAAO;IAEvC,uCAAuC;IACvC,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,eAAe,KAAK,CAAC,CAAA;QAC1B,MAAM,QAAQ,cAAc,CAAC,MAAoC;QACjE,OAAO,SAAS,CACd,OAAO,UAAU,WAAW,MAAM,IAAI,GAAG,MAAM,GAAG,IAChD,MAAM,OAAO,CAAC,SAAS,MAAM,MAAM,GAAG,IACpC,IACN;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,cAAc,EAAE,GAAG;IAC3B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3095, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/services/brand-scoped-storage.ts"], "sourcesContent": ["// Brand-scoped storage service\r\n// This service ensures all feature data is properly scoped to the current brand\r\n\r\nexport interface BrandScopedStorageConfig {\r\n  brandId: string;\r\n  feature: string; // e.g., 'artifacts', 'social-media', 'content-calendar'\r\n}\r\n\r\n// Predefined storage features for consistency\r\nexport const STORAGE_FEATURES = {\r\n  ARTIFACTS: 'artifacts',\r\n  ARTIFACT_FOLDERS: 'artifact-folders',\r\n  SELECTED_ARTIFACTS: 'selectedArtifacts',\r\n  SOCIAL_MEDIA: 'social-media',\r\n  CONTENT_CALENDAR: 'content-calendar',\r\n  QUICK_CONTENT: 'quick-content',\r\n  CREATIVE_STUDIO: 'creative-studio',\r\n  DESIGN_SETTINGS: 'design-settings',\r\n  BRAND_SETTINGS: 'brand-settings',\r\n} as const;\r\n\r\nexport type StorageFeature = typeof STORAGE_FEATURES[keyof typeof STORAGE_FEATURES];\r\n\r\nexport class BrandScopedStorage {\r\n  private brandId: string;\r\n  private feature: string;\r\n\r\n  constructor(config: BrandScopedStorageConfig) {\r\n    this.brandId = config.brandId;\r\n    this.feature = config.feature;\r\n  }\r\n\r\n  /**\r\n   * Get the brand-scoped storage key\r\n   */\r\n  private getStorageKey(): string {\r\n    return `${this.feature}_${this.brandId}`;\r\n  }\r\n\r\n  /**\r\n   * Get the global storage key (for migration purposes)\r\n   */\r\n  private getGlobalKey(): string {\r\n    return this.feature;\r\n  }\r\n\r\n  /**\r\n   * Store data for the current brand with quota management\r\n   */\r\n  setItem(data: any): void {\r\n    try {\r\n      // SMART APPROACH: Only strip large image data, keep URLs\r\n      const optimizedData = this.optimizeImageData(data);\r\n      const key = this.getStorageKey();\r\n      const serialized = JSON.stringify(optimizedData);\r\n\r\n      // Check storage stats before attempting to store\r\n      const stats = this.getStorageStats();\r\n      const dataSize = new Blob([serialized]).size;\r\n      const maxSize = 500 * 1024; // 500KB limit per item (much more aggressive)\r\n\r\n      console.log(`📊 Storage stats before save: Total: ${stats.totalUsage}, Available: ${stats.available}, New data: ${this.formatBytes(dataSize)}`);\r\n\r\n      // Always perform cleanup before saving to ensure maximum space\r\n      this.aggressiveCleanup();\r\n\r\n      if (dataSize > maxSize) {\r\n        console.warn(`⚠️ Data too large for ${this.feature} (${this.formatBytes(dataSize)}), trying fallback approaches...`);\r\n\r\n        // Try fallback 1: Use aggressive stripping (old method)\r\n        const strippedData = this.stripImageData(data);\r\n        const strippedSerialized = JSON.stringify(strippedData);\r\n        const strippedSize = new Blob([strippedSerialized]).size;\r\n\r\n        if (strippedSize <= maxSize) {\r\n          localStorage.setItem(key, strippedSerialized);\r\n          console.log(`💾 Saved stripped ${this.feature} data for brand ${this.brandId} (${this.formatBytes(strippedSize)})`);\r\n          return;\r\n        }\r\n\r\n        // Try fallback 2: Use minimal data\r\n        const minimalData = this.extractMinimalData(strippedData);\r\n        const minimalSerialized = JSON.stringify(minimalData);\r\n        const minimalSize = new Blob([minimalSerialized]).size;\r\n\r\n        if (minimalSize > maxSize) {\r\n          console.error(`💥 Cannot store even minimal data. Clearing storage.`);\r\n          this.removeItem(); // Clear existing data\r\n          // Store emergency placeholder\r\n          const emergency = {\r\n            id: Date.now().toString(),\r\n            date: new Date().toISOString(),\r\n            content: 'Content generated but not stored due to size limits',\r\n            platform: 'instagram'\r\n          };\r\n          localStorage.setItem(key, JSON.stringify([emergency]));\r\n        } else {\r\n          localStorage.setItem(key, minimalSerialized);\r\n        }\r\n        console.log(`💾 Saved minimal ${this.feature} data for brand ${this.brandId} (${this.formatBytes(minimalSize || 0)})`);\r\n        return;\r\n      }\r\n\r\n      localStorage.setItem(key, serialized);\r\n      console.log(`💾 Saved ${this.feature} data for brand ${this.brandId} (${this.formatBytes(dataSize)})`);\r\n    } catch (error) {\r\n      if (error.name === 'QuotaExceededError') {\r\n        console.error(`🚨 Storage quota exceeded for ${this.feature} data for brand ${this.brandId}`);\r\n        this.handleQuotaExceeded(data);\r\n      } else {\r\n        console.error(`Failed to save ${this.feature} data for brand ${this.brandId}:`, error);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get data for the current brand\r\n   */\r\n  getItem<T = any>(): T | null {\r\n    try {\r\n      const key = this.getStorageKey();\r\n      const stored = localStorage.getItem(key);\r\n\r\n      if (stored) {\r\n        console.log(`📂 Loaded ${this.feature} data for brand ${this.brandId}`);\r\n        return JSON.parse(stored);\r\n      }\r\n\r\n      // Try to migrate from global storage if brand-scoped data doesn't exist\r\n      const globalData = this.migrateFromGlobalStorage<T>();\r\n      if (globalData) {\r\n        console.log(`🔄 Migrated ${this.feature} data from global to brand-scoped storage`);\r\n        return globalData;\r\n      }\r\n\r\n      console.log(`📂 No ${this.feature} data found for brand ${this.brandId}`);\r\n      return null;\r\n    } catch (error) {\r\n      console.error(`Failed to load ${this.feature} data for brand ${this.brandId}:`, error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove data for the current brand\r\n   */\r\n  removeItem(): void {\r\n    try {\r\n      const key = this.getStorageKey();\r\n      localStorage.removeItem(key);\r\n      console.log(`🗑️ Removed ${this.feature} data for brand ${this.brandId}`);\r\n    } catch (error) {\r\n      console.error(`Failed to remove ${this.feature} data for brand ${this.brandId}:`, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if data exists for the current brand\r\n   */\r\n  hasItem(): boolean {\r\n    const key = this.getStorageKey();\r\n    return localStorage.getItem(key) !== null;\r\n  }\r\n\r\n  /**\r\n   * Migrate data from global storage to brand-scoped storage\r\n   * Only migrates if no other brand has already been migrated to prevent duplication\r\n   */\r\n  private migrateFromGlobalStorage<T = any>(): T | null {\r\n    try {\r\n      const globalKey = this.getGlobalKey();\r\n      const globalData = localStorage.getItem(globalKey);\r\n\r\n      if (globalData) {\r\n        // Check if migration has already happened for this feature\r\n        const migrationKey = `${this.feature}_migration_completed`;\r\n        const migrationCompleted = localStorage.getItem(migrationKey);\r\n\r\n        if (migrationCompleted) {\r\n          console.log(`🚫 Migration already completed for ${this.feature}, skipping for brand ${this.brandId}`);\r\n          return null;\r\n        }\r\n\r\n        const parsed = JSON.parse(globalData);\r\n\r\n        // Save to brand-scoped storage\r\n        this.setItem(parsed);\r\n\r\n        // Mark migration as completed to prevent future duplications\r\n        localStorage.setItem(migrationKey, 'true');\r\n\r\n        // Remove global data after migration to prevent future conflicts\r\n        localStorage.removeItem(globalKey);\r\n\r\n        console.log(`✅ Successfully migrated ${this.feature} data from global to brand ${this.brandId} and cleared global data`);\r\n        return parsed;\r\n      }\r\n\r\n      return null;\r\n    } catch (error) {\r\n      console.error(`Failed to migrate ${this.feature} data from global storage:`, error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get all brand IDs that have data for this feature\r\n   */\r\n  static getAllBrandIds(feature: string): string[] {\r\n    const brandIds: string[] = [];\r\n    const prefix = `${feature}_`;\r\n\r\n    try {\r\n      for (let i = 0; i < localStorage.length; i++) {\r\n        const key = localStorage.key(i);\r\n        if (key && key.startsWith(prefix)) {\r\n          const brandId = key.substring(prefix.length);\r\n          if (brandId) {\r\n            brandIds.push(brandId);\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(`Failed to get brand IDs for ${feature}:`, error);\r\n    }\r\n\r\n    return brandIds;\r\n  }\r\n\r\n  /**\r\n   * Clear all data for a specific brand across all features\r\n   */\r\n  static clearBrandData(brandId: string): void {\r\n    const keysToRemove: string[] = [];\r\n\r\n    try {\r\n      for (let i = 0; i < localStorage.length; i++) {\r\n        const key = localStorage.key(i);\r\n        if (key && key.endsWith(`_${brandId}`)) {\r\n          keysToRemove.push(key);\r\n        }\r\n      }\r\n\r\n      keysToRemove.forEach(key => {\r\n        localStorage.removeItem(key);\r\n        console.log(`🗑️ Removed ${key}`);\r\n      });\r\n\r\n      console.log(`🗑️ Cleared all data for brand ${brandId}`);\r\n    } catch (error) {\r\n      console.error(`Failed to clear data for brand ${brandId}:`, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get storage usage statistics for the current brand\r\n   */\r\n  getStorageStats(): { key: string; size: number; sizeFormatted: string; totalUsage: string; available: string } {\r\n    const key = this.getStorageKey();\r\n    const data = localStorage.getItem(key);\r\n    const size = data ? new Blob([data]).size : 0;\r\n\r\n    // Calculate total localStorage usage\r\n    let totalSize = 0;\r\n    for (let i = 0; i < localStorage.length; i++) {\r\n      const key = localStorage.key(i);\r\n      if (key) {\r\n        const value = localStorage.getItem(key);\r\n        if (value) {\r\n          totalSize += new Blob([value]).size;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Estimate available space (localStorage limit is usually 5-10MB)\r\n    const estimatedLimit = 8 * 1024 * 1024; // 8MB estimate\r\n    const available = Math.max(0, estimatedLimit - totalSize);\r\n\r\n    return {\r\n      key,\r\n      size,\r\n      sizeFormatted: this.formatBytes(size),\r\n      totalUsage: this.formatBytes(totalSize),\r\n      available: this.formatBytes(available)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Reset migration flag for this feature (admin/debug use only)\r\n   */\r\n  static resetMigrationFlag(feature: string): void {\r\n    const migrationKey = `${feature}_migration_completed`;\r\n    localStorage.removeItem(migrationKey);\r\n    console.log(`🔄 Reset migration flag for ${feature}`);\r\n  }\r\n\r\n  /**\r\n   * Handle quota exceeded error by cleaning up old data\r\n   */\r\n  private handleQuotaExceeded(data: any): void {\r\n    console.log(`🧹 EMERGENCY: Handling quota exceeded for ${this.feature} brand ${this.brandId}`);\r\n\r\n    try {\r\n      // Step 1: Nuclear cleanup - clear ALL localStorage data\r\n      console.log(`💥 Performing nuclear cleanup of ALL localStorage data...`);\r\n      const allKeys = Object.keys(localStorage);\r\n      let totalCleared = 0;\r\n\r\n      allKeys.forEach(key => {\r\n        const item = localStorage.getItem(key);\r\n        if (item) {\r\n          totalCleared += new Blob([item]).size;\r\n          localStorage.removeItem(key);\r\n        }\r\n      });\r\n\r\n      console.log(`🧹 Cleared ALL localStorage data: ${this.formatBytes(totalCleared)}`);\r\n\r\n      // Step 2: Try to save only the most essential data\r\n      const emergency = {\r\n        id: Date.now().toString(),\r\n        date: new Date().toISOString(),\r\n        content: Array.isArray(data) && data.length > 0 ?\r\n          (data[0].content ? data[0].content.substring(0, 50) + '...' : 'Generated content') :\r\n          (data.content ? data.content.substring(0, 50) + '...' : 'Generated content'),\r\n        platform: 'instagram',\r\n        note: 'Storage quota exceeded - full data not saved'\r\n      };\r\n\r\n      const key = this.getStorageKey();\r\n      localStorage.setItem(key, JSON.stringify([emergency]));\r\n      console.log(`🆘 Saved emergency data only for ${this.feature}`);\r\n\r\n    } catch (criticalError) {\r\n      console.error(`💀 CRITICAL: Cannot save any data even after nuclear cleanup:`, criticalError);\r\n      // At this point, we just give up on localStorage entirely\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Aggressive cleanup of localStorage to free up space\r\n   */\r\n  private aggressiveCleanup(): void {\r\n    console.log('🔥 Performing aggressive localStorage cleanup...');\r\n\r\n    // Get all localStorage keys\r\n    const allKeys = Object.keys(localStorage);\r\n    let totalFreed = 0;\r\n\r\n    // Remove old migration flags and temporary data\r\n    allKeys.forEach(key => {\r\n      if (key.includes('_migrated') || key.includes('_temp') || key.includes('_cache')) {\r\n        const item = localStorage.getItem(key);\r\n        if (item) {\r\n          totalFreed += new Blob([item]).size;\r\n          localStorage.removeItem(key);\r\n        }\r\n      }\r\n    });\r\n\r\n    // Clean up old brand data (keep only current brand and most recent)\r\n    const brandKeys = allKeys.filter(key => key.includes('_') && key !== this.getStorageKey());\r\n    brandKeys.forEach(key => {\r\n      const item = localStorage.getItem(key);\r\n      if (item) {\r\n        // Check if the data contains old compression placeholders\r\n        if (item.includes('[COMPRESSED_IMAGE]') || item.includes('[TRUNCATED]')) {\r\n          console.log(`🧹 Removing old compressed data with placeholders: ${key}`);\r\n          totalFreed += new Blob([item]).size;\r\n          localStorage.removeItem(key);\r\n        } else {\r\n          totalFreed += new Blob([item]).size;\r\n          localStorage.removeItem(key);\r\n        }\r\n      }\r\n    });\r\n\r\n    console.log(`🧹 Freed up ${this.formatBytes(totalFreed)} of storage space`);\r\n  }\r\n\r\n  /**\r\n   * Smart image data optimization - keep URLs but remove large data\r\n   */\r\n  private optimizeImageData(data: any): any {\r\n    if (!data) return data;\r\n\r\n    if (Array.isArray(data)) {\r\n      // Keep more items but optimize each one\r\n      return data.slice(0, 5).map(item => this.optimizeImageFromItem(item));\r\n    }\r\n\r\n    return this.optimizeImageFromItem(data);\r\n  }\r\n\r\n  /**\r\n   * Optimize image data in a single item - preserve all valid URLs, handle base64 smartly\r\n   */\r\n  private optimizeImageFromItem(item: any): any {\r\n    if (!item || typeof item !== 'object') return item;\r\n\r\n    const optimized = { ...item };\r\n\r\n    // Keep image URLs but handle base64 data smartly\r\n    Object.keys(optimized).forEach(key => {\r\n      const value = optimized[key];\r\n\r\n      if (typeof value === 'string') {\r\n        // Keep ALL valid URLs (HTTP, HTTPS, blob, data URLs under size limit)\r\n        if (value.startsWith('http://') || value.startsWith('https://')) {\r\n          // Keep HTTP/HTTPS URLs as-is, especially Firebase Storage URLs\r\n          if (value.includes('firebasestorage.googleapis.com')) {\r\n            console.log(`🔥 Preserving Firebase Storage URL for ${key}: ${value.substring(0, 50)}...`);\r\n          } else {\r\n            console.log(`📸 Preserving HTTP/HTTPS URL for ${key}: ${value.substring(0, 50)}...`);\r\n          }\r\n          return;\r\n        }\r\n\r\n        if (value.startsWith('blob:')) {\r\n          // Keep blob URLs but warn they might be temporary\r\n          console.log(`⚠️ Preserving blob URL for ${key} (may be temporary): ${value.substring(0, 50)}...`);\r\n          return;\r\n        }\r\n\r\n        if (value.startsWith('data:image/')) {\r\n          // For data URLs, keep smaller ones and convert large ones to placeholder\r\n          if (value.length <= 50000) { // Increased limit to 50KB for small images\r\n            console.log(`📸 Preserving small data URL for ${key} (${this.formatBytes(value.length)})`);\r\n            return;\r\n          } else {\r\n            // For large data URLs, create a more informative placeholder\r\n            console.log(`🗜️ Converting large data URL to placeholder for ${key} (${this.formatBytes(value.length)})`);\r\n            const mimeType = value.split(';')[0].split(':')[1] || 'image/png';\r\n            optimized[key] = `[Image data preserved but too large for storage - ${mimeType} - ${this.formatBytes(value.length)}]`;\r\n          }\r\n        }\r\n      } else if (Array.isArray(value)) {\r\n        optimized[key] = value.map(v => this.optimizeImageFromItem(v));\r\n      } else if (typeof value === 'object' && value !== null) {\r\n        optimized[key] = this.optimizeImageFromItem(value);\r\n      }\r\n    });\r\n\r\n    return optimized;\r\n  }\r\n\r\n  /**\r\n   * Strip all image data to prevent storage quota issues (fallback method)\r\n   */\r\n  private stripImageData(data: any): any {\r\n    if (!data) return data;\r\n\r\n    if (Array.isArray(data)) {\r\n      return data.slice(0, 3).map(item => this.stripImageFromItem(item)); // Keep only 3 most recent\r\n    }\r\n\r\n    return this.stripImageFromItem(data);\r\n  }\r\n\r\n  /**\r\n   * Strip image data from individual items\r\n   */\r\n  private stripImageFromItem(item: any): any {\r\n    if (!item || typeof item !== 'object') return item;\r\n\r\n    const stripped = {\r\n      id: item.id,\r\n      date: item.date,\r\n      content: (item.content && typeof item.content === 'string') ? (item.content.length > 300 ? item.content.substring(0, 300) + '...' : item.content) : '',\r\n      hashtags: item.hashtags,\r\n      platform: item.platform || 'instagram',\r\n      status: item.status,\r\n      catchyWords: item.catchyWords,\r\n      subheadline: item.subheadline,\r\n      callToAction: item.callToAction,\r\n      // Completely omit variants with images to save space\r\n      variants: item.variants ? item.variants.map((variant: any) => ({\r\n        platform: variant.platform,\r\n        // No imageUrl at all - will be regenerated if needed\r\n      })) : []\r\n    };\r\n\r\n    return stripped;\r\n  }\r\n\r\n  /**\r\n   * Extract minimal data (only the most recent items)\r\n   */\r\n  private extractMinimalData(data: any): any {\r\n    if (!data) return null;\r\n\r\n    if (Array.isArray(data)) {\r\n      // Keep only the 1 most recent item\r\n      return data.slice(0, 1).map(item => ({\r\n        id: item.id,\r\n        content: (item.content && typeof item.content === 'string') ? item.content.substring(0, 100) + '...' : '',\r\n        date: item.date,\r\n        platform: item.platform || 'instagram'\r\n      }));\r\n    }\r\n\r\n    // For single items, return basic info only\r\n    return {\r\n      id: data.id,\r\n      content: (data.content && typeof data.content === 'string') ? data.content.substring(0, 100) + '...' : '',\r\n      date: data.date || new Date().toISOString(),\r\n      platform: data.platform || 'instagram'\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Compress data by removing or reducing large elements\r\n   */\r\n  private compressDataForStorage(data: any): any {\r\n    if (!data) return data;\r\n\r\n    // Handle arrays (like posts)\r\n    if (Array.isArray(data)) {\r\n      // Limit to 5 most recent items and compress each\r\n      return data.slice(0, 5).map(item => this.compressItem(item));\r\n    }\r\n\r\n    // Handle single items\r\n    return this.compressItem(data);\r\n  }\r\n\r\n  /**\r\n   * Compress individual data items aggressively\r\n   */\r\n  private compressItem(item: any): any {\r\n    if (!item || typeof item !== 'object') return item;\r\n\r\n    const compressed = { ...item };\r\n\r\n    // Remove or compress large base64 images\r\n    if (compressed.variants && Array.isArray(compressed.variants)) {\r\n      compressed.variants = compressed.variants.map((variant: any) => {\r\n        const newVariant: any = {\r\n          platform: variant.platform\r\n        };\r\n\r\n        // Only include imageUrl if it's not a base64 image\r\n        if (variant.imageUrl && !variant.imageUrl.startsWith('data:')) {\r\n          newVariant.imageUrl = variant.imageUrl;\r\n        }\r\n        // For base64 images, simply omit the imageUrl property entirely\r\n\r\n        return newVariant;\r\n      });\r\n    }\r\n\r\n    // Compress content fields\r\n    if (compressed.content && typeof compressed.content === 'string') {\r\n      if (compressed.content.length > 500) {\r\n        compressed.content = compressed.content.substring(0, 500) + '...';\r\n      }\r\n    }\r\n\r\n    // Remove large metadata fields\r\n    delete compressed.metadata;\r\n    delete compressed.marketIntelligence;\r\n    delete compressed.localContext;\r\n    delete compressed.contentVariants;\r\n    delete compressed.hashtagAnalysis;\r\n\r\n    // Keep only essential fields\r\n    const essential = {\r\n      id: compressed.id,\r\n      date: compressed.date,\r\n      content: compressed.content,\r\n      hashtags: compressed.hashtags,\r\n      platform: compressed.platform || 'instagram',\r\n      status: compressed.status,\r\n      catchyWords: compressed.catchyWords,\r\n      subheadline: compressed.subheadline,\r\n      callToAction: compressed.callToAction,\r\n      variants: compressed.variants\r\n    };\r\n\r\n    return essential;\r\n  }\r\n\r\n  /**\r\n   * Extract only essential data when storage is critically low\r\n   */\r\n  private extractEssentialData(data: any): any {\r\n    if (!data) return data;\r\n\r\n    if (Array.isArray(data)) {\r\n      // Keep only the most recent 5 items\r\n      return data.slice(-5).map(item => ({\r\n        id: item.id,\r\n        date: item.date,\r\n        content: (item.content && typeof item.content === 'string') ? item.content.substring(0, 200) + '...' : '',\r\n        hashtags: item.hashtags,\r\n        status: item.status\r\n      }));\r\n    }\r\n\r\n    // For single items, keep only essential fields\r\n    return {\r\n      id: data.id,\r\n      date: data.date,\r\n      content: (data.content && typeof data.content === 'string') ? data.content.substring(0, 200) + '...' : '',\r\n      hashtags: data.hashtags,\r\n      status: data.status\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clean up old data to free space\r\n   */\r\n  private cleanupOldData(): void {\r\n    try {\r\n      // Get current data\r\n      const currentData = this.getItem();\r\n      if (!currentData || !Array.isArray(currentData)) return;\r\n\r\n      // Keep only the most recent 10 items\r\n      const recentData = currentData.slice(-10);\r\n\r\n      if (recentData.length < currentData.length) {\r\n        const key = this.getStorageKey();\r\n        localStorage.setItem(key, JSON.stringify(recentData));\r\n        console.log(`🧹 Cleaned up old data, kept ${recentData.length} of ${currentData.length} items`);\r\n      }\r\n    } catch (error) {\r\n      console.warn('Failed to cleanup old data:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Format bytes to human readable format\r\n   */\r\n  private formatBytes(bytes: number): string {\r\n    if (bytes === 0) return '0 Bytes';\r\n\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  }\r\n}\r\n\r\n/**\r\n * Factory function to create brand-scoped storage for different features\r\n */\r\nexport function createBrandScopedStorage(brandId: string, feature: string): BrandScopedStorage {\r\n  return new BrandScopedStorage({ brandId, feature });\r\n}\r\n\r\n/**\r\n * Hook-like function to get brand-scoped storage that updates when brand changes\r\n */\r\nexport function useBrandScopedStorage(brandId: string | null, feature: string): BrandScopedStorage | null {\r\n  if (!brandId) {\r\n    console.warn(`Cannot create brand-scoped storage for ${feature}: brandId is null`);\r\n    return null;\r\n  }\r\n\r\n  return createBrandScopedStorage(brandId, feature);\r\n}\r\n\r\n/**\r\n * Utility to migrate all global storage to brand-scoped storage\r\n */\r\nexport function migrateAllGlobalStorage(brandId: string, features: string[]): void {\r\n  console.log(`🔄 Starting migration of global storage to brand-scoped for brand ${brandId}`);\r\n\r\n  features.forEach(feature => {\r\n    const storage = createBrandScopedStorage(brandId, feature);\r\n\r\n    // This will automatically migrate if brand-scoped data doesn't exist\r\n    storage.getItem();\r\n  });\r\n\r\n  console.log(`✅ Migration completed for brand ${brandId}`);\r\n}\r\n\r\n/**\r\n * Get localStorage usage statistics\r\n */\r\nexport function getStorageUsage(): {\r\n  used: number;\r\n  total: number;\r\n  percentage: number;\r\n  usedFormatted: string;\r\n  totalFormatted: string;\r\n} {\r\n  let used = 0;\r\n\r\n  try {\r\n    // Calculate used space\r\n    for (let key in localStorage) {\r\n      if (localStorage.hasOwnProperty(key)) {\r\n        used += localStorage[key].length + key.length;\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.warn('Failed to calculate storage usage:', error);\r\n  }\r\n\r\n  // Estimate total available space (varies by browser, ~5-10MB)\r\n  const total = 10 * 1024 * 1024; // 10MB estimate\r\n  const percentage = (used / total) * 100;\r\n\r\n  return {\r\n    used,\r\n    total,\r\n    percentage,\r\n    usedFormatted: formatBytes(used),\r\n    totalFormatted: formatBytes(total)\r\n  };\r\n}\r\n\r\n/**\r\n * Clean up all old data across all brands and features\r\n */\r\nexport function cleanupAllStorage(): void {\r\n  console.log('🧹 Starting comprehensive storage cleanup...');\r\n\r\n  const keysToCheck: string[] = [];\r\n\r\n  // Collect all keys\r\n  for (let i = 0; i < localStorage.length; i++) {\r\n    const key = localStorage.key(i);\r\n    if (key) keysToCheck.push(key);\r\n  }\r\n\r\n  // Clean up brand-scoped data\r\n  keysToCheck.forEach(key => {\r\n    if (key.includes('_') && !key.includes('migration_completed')) {\r\n      try {\r\n        const data = JSON.parse(localStorage.getItem(key) || '[]');\r\n        if (Array.isArray(data) && data.length > 10) {\r\n          // Keep only recent items\r\n          const recentData = data.slice(-10);\r\n          localStorage.setItem(key, JSON.stringify(recentData));\r\n          console.log(`🧹 Cleaned up ${key}: ${data.length} → ${recentData.length} items`);\r\n        }\r\n      } catch (error) {\r\n        console.warn(`Failed to cleanup ${key}:`, error);\r\n      }\r\n    }\r\n  });\r\n\r\n  console.log('✅ Storage cleanup completed');\r\n}\r\n\r\n/**\r\n * Format bytes helper function\r\n */\r\nfunction formatBytes(bytes: number): string {\r\n  if (bytes === 0) return '0 Bytes';\r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n}\r\n\r\n\r\n"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B,gFAAgF;;;;;;;;;;AAQzE,MAAM,mBAAmB;IAC9B,WAAW;IACX,kBAAkB;IAClB,oBAAoB;IACpB,cAAc;IACd,kBAAkB;IAClB,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,gBAAgB;AAClB;AAIO,MAAM;IACH,QAAgB;IAChB,QAAgB;IAExB,YAAY,MAAgC,CAAE;QAC5C,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO;IAC/B;IAEA;;GAEC,GACD,AAAQ,gBAAwB;QAC9B,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE;IAC1C;IAEA;;GAEC,GACD,AAAQ,eAAuB;QAC7B,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA;;GAEC,GACD,QAAQ,IAAS,EAAQ;QACvB,IAAI;YACF,yDAAyD;YACzD,MAAM,gBAAgB,IAAI,CAAC,iBAAiB,CAAC;YAC7C,MAAM,MAAM,IAAI,CAAC,aAAa;YAC9B,MAAM,aAAa,KAAK,SAAS,CAAC;YAElC,iDAAiD;YACjD,MAAM,QAAQ,IAAI,CAAC,eAAe;YAClC,MAAM,WAAW,IAAI,KAAK;gBAAC;aAAW,EAAE,IAAI;YAC5C,MAAM,UAAU,MAAM,MAAM,8CAA8C;YAE1E,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,UAAU,CAAC,aAAa,EAAE,MAAM,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW;YAE9I,+DAA+D;YAC/D,IAAI,CAAC,iBAAiB;YAEtB,IAAI,WAAW,SAAS;gBACtB,QAAQ,IAAI,CAAC,CAAC,sBAAsB,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU,gCAAgC,CAAC;gBAEnH,wDAAwD;gBACxD,MAAM,eAAe,IAAI,CAAC,cAAc,CAAC;gBACzC,MAAM,qBAAqB,KAAK,SAAS,CAAC;gBAC1C,MAAM,eAAe,IAAI,KAAK;oBAAC;iBAAmB,EAAE,IAAI;gBAExD,IAAI,gBAAgB,SAAS;oBAC3B,aAAa,OAAO,CAAC,KAAK;oBAC1B,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;oBAClH;gBACF;gBAEA,mCAAmC;gBACnC,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC;gBAC5C,MAAM,oBAAoB,KAAK,SAAS,CAAC;gBACzC,MAAM,cAAc,IAAI,KAAK;oBAAC;iBAAkB,EAAE,IAAI;gBAEtD,IAAI,cAAc,SAAS;oBACzB,QAAQ,KAAK,CAAC,CAAC,oDAAoD,CAAC;oBACpE,IAAI,CAAC,UAAU,IAAI,sBAAsB;oBACzC,8BAA8B;oBAC9B,MAAM,YAAY;wBAChB,IAAI,KAAK,GAAG,GAAG,QAAQ;wBACvB,MAAM,IAAI,OAAO,WAAW;wBAC5B,SAAS;wBACT,UAAU;oBACZ;oBACA,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;wBAAC;qBAAU;gBACtD,OAAO;oBACL,aAAa,OAAO,CAAC,KAAK;gBAC5B;gBACA,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,eAAe,GAAG,CAAC,CAAC;gBACrH;YACF;YAEA,aAAa,OAAO,CAAC,KAAK;YAC1B,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACvG,EAAE,OAAO,OAAO;YACd,IAAI,MAAM,IAAI,KAAK,sBAAsB;gBACvC,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAAE;gBAC5F,IAAI,CAAC,mBAAmB,CAAC;YAC3B,OAAO;gBACL,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YAClF;QACF;IACF;IAEA;;GAEC,GACD,UAA6B;QAC3B,IAAI;YACF,MAAM,MAAM,IAAI,CAAC,aAAa;YAC9B,MAAM,SAAS,aAAa,OAAO,CAAC;YAEpC,IAAI,QAAQ;gBACV,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAAE;gBACtE,OAAO,KAAK,KAAK,CAAC;YACpB;YAEA,wEAAwE;YACxE,MAAM,aAAa,IAAI,CAAC,wBAAwB;YAChD,IAAI,YAAY;gBACd,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,yCAAyC,CAAC;gBAClF,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC,OAAO,EAAE;YACxE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YAChF,OAAO;QACT;IACF;IAEA;;GAEC,GACD,aAAmB;QACjB,IAAI;YACF,MAAM,MAAM,IAAI,CAAC,aAAa;YAC9B,aAAa,UAAU,CAAC;YACxB,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAAE;QAC1E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;QACpF;IACF;IAEA;;GAEC,GACD,UAAmB;QACjB,MAAM,MAAM,IAAI,CAAC,aAAa;QAC9B,OAAO,aAAa,OAAO,CAAC,SAAS;IACvC;IAEA;;;GAGC,GACD,AAAQ,2BAA8C;QACpD,IAAI;YACF,MAAM,YAAY,IAAI,CAAC,YAAY;YACnC,MAAM,aAAa,aAAa,OAAO,CAAC;YAExC,IAAI,YAAY;gBACd,2DAA2D;gBAC3D,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;gBAC1D,MAAM,qBAAqB,aAAa,OAAO,CAAC;gBAEhD,IAAI,oBAAoB;oBACtB,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,EAAE;oBACpG,OAAO;gBACT;gBAEA,MAAM,SAAS,KAAK,KAAK,CAAC;gBAE1B,+BAA+B;gBAC/B,IAAI,CAAC,OAAO,CAAC;gBAEb,6DAA6D;gBAC7D,aAAa,OAAO,CAAC,cAAc;gBAEnC,iEAAiE;gBACjE,aAAa,UAAU,CAAC;gBAExB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAE,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC;gBACvH,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,EAAE;YAC7E,OAAO;QACT;IACF;IAEA;;GAEC,GACD,OAAO,eAAe,OAAe,EAAY;QAC/C,MAAM,WAAqB,EAAE;QAC7B,MAAM,SAAS,GAAG,QAAQ,CAAC,CAAC;QAE5B,IAAI;YACF,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;gBAC7B,IAAI,OAAO,IAAI,UAAU,CAAC,SAAS;oBACjC,MAAM,UAAU,IAAI,SAAS,CAAC,OAAO,MAAM;oBAC3C,IAAI,SAAS;wBACX,SAAS,IAAI,CAAC;oBAChB;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC,EAAE;QAC3D;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,eAAe,OAAe,EAAQ;QAC3C,MAAM,eAAyB,EAAE;QAEjC,IAAI;YACF,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;gBAC7B,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG;oBACtC,aAAa,IAAI,CAAC;gBACpB;YACF;YAEA,aAAa,OAAO,CAAC,CAAA;gBACnB,aAAa,UAAU,CAAC;gBACxB,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK;YAClC;YAEA,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,SAAS;QACzD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,QAAQ,CAAC,CAAC,EAAE;QAC9D;IACF;IAEA;;GAEC,GACD,kBAA+G;QAC7G,MAAM,MAAM,IAAI,CAAC,aAAa;QAC9B,MAAM,OAAO,aAAa,OAAO,CAAC;QAClC,MAAM,OAAO,OAAO,IAAI,KAAK;YAAC;SAAK,EAAE,IAAI,GAAG;QAE5C,qCAAqC;QACrC,IAAI,YAAY;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;YAC7B,IAAI,KAAK;gBACP,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,OAAO;oBACT,aAAa,IAAI,KAAK;wBAAC;qBAAM,EAAE,IAAI;gBACrC;YACF;QACF;QAEA,kEAAkE;QAClE,MAAM,iBAAiB,IAAI,OAAO,MAAM,eAAe;QACvD,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,iBAAiB;QAE/C,OAAO;YACL;YACA;YACA,eAAe,IAAI,CAAC,WAAW,CAAC;YAChC,YAAY,IAAI,CAAC,WAAW,CAAC;YAC7B,WAAW,IAAI,CAAC,WAAW,CAAC;QAC9B;IACF;IAEA;;GAEC,GACD,OAAO,mBAAmB,OAAe,EAAQ;QAC/C,MAAM,eAAe,GAAG,QAAQ,oBAAoB,CAAC;QACrD,aAAa,UAAU,CAAC;QACxB,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,SAAS;IACtD;IAEA;;GAEC,GACD,AAAQ,oBAAoB,IAAS,EAAQ;QAC3C,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE7F,IAAI;YACF,wDAAwD;YACxD,QAAQ,GAAG,CAAC,CAAC,yDAAyD,CAAC;YACvE,MAAM,UAAU,OAAO,IAAI,CAAC;YAC5B,IAAI,eAAe;YAEnB,QAAQ,OAAO,CAAC,CAAA;gBACd,MAAM,OAAO,aAAa,OAAO,CAAC;gBAClC,IAAI,MAAM;oBACR,gBAAgB,IAAI,KAAK;wBAAC;qBAAK,EAAE,IAAI;oBACrC,aAAa,UAAU,CAAC;gBAC1B;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,IAAI,CAAC,WAAW,CAAC,eAAe;YAEjF,mDAAmD;YACnD,MAAM,YAAY;gBAChB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM,IAAI,OAAO,WAAW;gBAC5B,SAAS,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,IAC3C,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,QAAQ,sBAC7D,KAAK,OAAO,GAAG,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,QAAQ;gBAC1D,UAAU;gBACV,MAAM;YACR;YAEA,MAAM,MAAM,IAAI,CAAC,aAAa;YAC9B,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;gBAAC;aAAU;YACpD,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,IAAI,CAAC,OAAO,EAAE;QAEhE,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,CAAC,6DAA6D,CAAC,EAAE;QAC/E,0DAA0D;QAC5D;IACF;IAEA;;GAEC,GACD,AAAQ,oBAA0B;QAChC,QAAQ,GAAG,CAAC;QAEZ,4BAA4B;QAC5B,MAAM,UAAU,OAAO,IAAI,CAAC;QAC5B,IAAI,aAAa;QAEjB,gDAAgD;QAChD,QAAQ,OAAO,CAAC,CAAA;YACd,IAAI,IAAI,QAAQ,CAAC,gBAAgB,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,WAAW;gBAChF,MAAM,OAAO,aAAa,OAAO,CAAC;gBAClC,IAAI,MAAM;oBACR,cAAc,IAAI,KAAK;wBAAC;qBAAK,EAAE,IAAI;oBACnC,aAAa,UAAU,CAAC;gBAC1B;YACF;QACF;QAEA,oEAAoE;QACpE,MAAM,YAAY,QAAQ,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,CAAC,QAAQ,QAAQ,IAAI,CAAC,aAAa;QACvF,UAAU,OAAO,CAAC,CAAA;YAChB,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,IAAI,MAAM;gBACR,0DAA0D;gBAC1D,IAAI,KAAK,QAAQ,CAAC,yBAAyB,KAAK,QAAQ,CAAC,gBAAgB;oBACvE,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,KAAK;oBACvE,cAAc,IAAI,KAAK;wBAAC;qBAAK,EAAE,IAAI;oBACnC,aAAa,UAAU,CAAC;gBAC1B,OAAO;oBACL,cAAc,IAAI,KAAK;wBAAC;qBAAK,EAAE,IAAI;oBACnC,aAAa,UAAU,CAAC;gBAC1B;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,iBAAiB,CAAC;IAC5E;IAEA;;GAEC,GACD,AAAQ,kBAAkB,IAAS,EAAO;QACxC,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,wCAAwC;YACxC,OAAO,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,qBAAqB,CAAC;QACjE;QAEA,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAS,EAAO;QAC5C,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;QAE9C,MAAM,YAAY;YAAE,GAAG,IAAI;QAAC;QAE5B,iDAAiD;QACjD,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,CAAA;YAC7B,MAAM,QAAQ,SAAS,CAAC,IAAI;YAE5B,IAAI,OAAO,UAAU,UAAU;gBAC7B,sEAAsE;gBACtE,IAAI,MAAM,UAAU,CAAC,cAAc,MAAM,UAAU,CAAC,aAAa;oBAC/D,+DAA+D;oBAC/D,IAAI,MAAM,QAAQ,CAAC,mCAAmC;wBACpD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,IAAI,EAAE,EAAE,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;oBAC3F,OAAO;wBACL,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,IAAI,EAAE,EAAE,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;oBACrF;oBACA;gBACF;gBAEA,IAAI,MAAM,UAAU,CAAC,UAAU;oBAC7B,kDAAkD;oBAClD,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,IAAI,qBAAqB,EAAE,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;oBAChG;gBACF;gBAEA,IAAI,MAAM,UAAU,CAAC,gBAAgB;oBACnC,yEAAyE;oBACzE,IAAI,MAAM,MAAM,IAAI,OAAO;wBACzB,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,MAAM,EAAE,CAAC,CAAC;wBACzF;oBACF,OAAO;wBACL,6DAA6D;wBAC7D,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,MAAM,EAAE,CAAC,CAAC;wBACzG,MAAM,WAAW,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;wBACtD,SAAS,CAAC,IAAI,GAAG,CAAC,kDAAkD,EAAE,SAAS,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,MAAM,EAAE,CAAC,CAAC;oBACvH;gBACF;YACF,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;gBAC/B,SAAS,CAAC,IAAI,GAAG,MAAM,GAAG,CAAC,CAAA,IAAK,IAAI,CAAC,qBAAqB,CAAC;YAC7D,OAAO,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;gBACtD,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC9C;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,eAAe,IAAS,EAAO;QACrC,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,OAAO,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,kBAAkB,CAAC,QAAQ,0BAA0B;QAChG;QAEA,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC;IAEA;;GAEC,GACD,AAAQ,mBAAmB,IAAS,EAAO;QACzC,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;QAE9C,MAAM,WAAW;YACf,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,SAAS,AAAC,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,KAAK,WAAa,KAAK,OAAO,CAAC,MAAM,GAAG,MAAM,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,QAAQ,KAAK,OAAO,GAAI;YACpJ,UAAU,KAAK,QAAQ;YACvB,UAAU,KAAK,QAAQ,IAAI;YAC3B,QAAQ,KAAK,MAAM;YACnB,aAAa,KAAK,WAAW;YAC7B,aAAa,KAAK,WAAW;YAC7B,cAAc,KAAK,YAAY;YAC/B,qDAAqD;YACrD,UAAU,KAAK,QAAQ,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,UAAiB,CAAC;oBAC7D,UAAU,QAAQ,QAAQ;gBAE5B,CAAC,KAAK,EAAE;QACV;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,mBAAmB,IAAS,EAAO;QACzC,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,mCAAmC;YACnC,OAAO,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACnC,IAAI,KAAK,EAAE;oBACX,SAAS,AAAC,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,KAAK,WAAY,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,QAAQ;oBACvG,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ,IAAI;gBAC7B,CAAC;QACH;QAEA,2CAA2C;QAC3C,OAAO;YACL,IAAI,KAAK,EAAE;YACX,SAAS,AAAC,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,KAAK,WAAY,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,QAAQ;YACvG,MAAM,KAAK,IAAI,IAAI,IAAI,OAAO,WAAW;YACzC,UAAU,KAAK,QAAQ,IAAI;QAC7B;IACF;IAEA;;GAEC,GACD,AAAQ,uBAAuB,IAAS,EAAO;QAC7C,IAAI,CAAC,MAAM,OAAO;QAElB,6BAA6B;QAC7B,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,iDAAiD;YACjD,OAAO,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,YAAY,CAAC;QACxD;QAEA,sBAAsB;QACtB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B;IAEA;;GAEC,GACD,AAAQ,aAAa,IAAS,EAAO;QACnC,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;QAE9C,MAAM,aAAa;YAAE,GAAG,IAAI;QAAC;QAE7B,yCAAyC;QACzC,IAAI,WAAW,QAAQ,IAAI,MAAM,OAAO,CAAC,WAAW,QAAQ,GAAG;YAC7D,WAAW,QAAQ,GAAG,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAC7C,MAAM,aAAkB;oBACtB,UAAU,QAAQ,QAAQ;gBAC5B;gBAEA,mDAAmD;gBACnD,IAAI,QAAQ,QAAQ,IAAI,CAAC,QAAQ,QAAQ,CAAC,UAAU,CAAC,UAAU;oBAC7D,WAAW,QAAQ,GAAG,QAAQ,QAAQ;gBACxC;gBACA,gEAAgE;gBAEhE,OAAO;YACT;QACF;QAEA,0BAA0B;QAC1B,IAAI,WAAW,OAAO,IAAI,OAAO,WAAW,OAAO,KAAK,UAAU;YAChE,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG,KAAK;gBACnC,WAAW,OAAO,GAAG,WAAW,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO;YAC9D;QACF;QAEA,+BAA+B;QAC/B,OAAO,WAAW,QAAQ;QAC1B,OAAO,WAAW,kBAAkB;QACpC,OAAO,WAAW,YAAY;QAC9B,OAAO,WAAW,eAAe;QACjC,OAAO,WAAW,eAAe;QAEjC,6BAA6B;QAC7B,MAAM,YAAY;YAChB,IAAI,WAAW,EAAE;YACjB,MAAM,WAAW,IAAI;YACrB,SAAS,WAAW,OAAO;YAC3B,UAAU,WAAW,QAAQ;YAC7B,UAAU,WAAW,QAAQ,IAAI;YACjC,QAAQ,WAAW,MAAM;YACzB,aAAa,WAAW,WAAW;YACnC,aAAa,WAAW,WAAW;YACnC,cAAc,WAAW,YAAY;YACrC,UAAU,WAAW,QAAQ;QAC/B;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,qBAAqB,IAAS,EAAO;QAC3C,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,oCAAoC;YACpC,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACjC,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,SAAS,AAAC,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,KAAK,WAAY,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,QAAQ;oBACvG,UAAU,KAAK,QAAQ;oBACvB,QAAQ,KAAK,MAAM;gBACrB,CAAC;QACH;QAEA,+CAA+C;QAC/C,OAAO;YACL,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,SAAS,AAAC,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,KAAK,WAAY,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,QAAQ;YACvG,UAAU,KAAK,QAAQ;YACvB,QAAQ,KAAK,MAAM;QACrB;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAuB;QAC7B,IAAI;YACF,mBAAmB;YACnB,MAAM,cAAc,IAAI,CAAC,OAAO;YAChC,IAAI,CAAC,eAAe,CAAC,MAAM,OAAO,CAAC,cAAc;YAEjD,qCAAqC;YACrC,MAAM,aAAa,YAAY,KAAK,CAAC,CAAC;YAEtC,IAAI,WAAW,MAAM,GAAG,YAAY,MAAM,EAAE;gBAC1C,MAAM,MAAM,IAAI,CAAC,aAAa;gBAC9B,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;gBACzC,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,WAAW,MAAM,CAAC,IAAI,EAAE,YAAY,MAAM,CAAC,MAAM,CAAC;YAChG;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,+BAA+B;QAC9C;IACF;IAEA;;GAEC,GACD,AAAQ,YAAY,KAAa,EAAU;QACzC,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;AACF;AAKO,SAAS,yBAAyB,OAAe,EAAE,OAAe;IACvE,OAAO,IAAI,mBAAmB;QAAE;QAAS;IAAQ;AACnD;AAKO,SAAS,sBAAsB,OAAsB,EAAE,OAAe;IAC3E,IAAI,CAAC,SAAS;QACZ,QAAQ,IAAI,CAAC,CAAC,uCAAuC,EAAE,QAAQ,iBAAiB,CAAC;QACjF,OAAO;IACT;IAEA,OAAO,yBAAyB,SAAS;AAC3C;AAKO,SAAS,wBAAwB,OAAe,EAAE,QAAkB;IACzE,QAAQ,GAAG,CAAC,CAAC,kEAAkE,EAAE,SAAS;IAE1F,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,UAAU,yBAAyB,SAAS;QAElD,qEAAqE;QACrE,QAAQ,OAAO;IACjB;IAEA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,SAAS;AAC1D;AAKO,SAAS;IAOd,IAAI,OAAO;IAEX,IAAI;QACF,uBAAuB;QACvB,IAAK,IAAI,OAAO,aAAc;YAC5B,IAAI,aAAa,cAAc,CAAC,MAAM;gBACpC,QAAQ,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM;YAC/C;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,sCAAsC;IACrD;IAEA,8DAA8D;IAC9D,MAAM,QAAQ,KAAK,OAAO,MAAM,gBAAgB;IAChD,MAAM,aAAa,AAAC,OAAO,QAAS;IAEpC,OAAO;QACL;QACA;QACA;QACA,eAAe,YAAY;QAC3B,gBAAgB,YAAY;IAC9B;AACF;AAKO,SAAS;IACd,QAAQ,GAAG,CAAC;IAEZ,MAAM,cAAwB,EAAE;IAEhC,mBAAmB;IACnB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,IAAI,KAAK,YAAY,IAAI,CAAC;IAC5B;IAEA,6BAA6B;IAC7B,YAAY,OAAO,CAAC,CAAA;QAClB,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,wBAAwB;YAC7D,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,QAAQ;gBACrD,IAAI,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,IAAI;oBAC3C,yBAAyB;oBACzB,MAAM,aAAa,KAAK,KAAK,CAAC,CAAC;oBAC/B,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;oBACzC,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,WAAW,MAAM,CAAC,MAAM,CAAC;gBACjF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,EAAE;YAC5C;QACF;IACF;IAEA,QAAQ,GAAG,CAAC;AACd;AAEA;;CAEC,GACD,SAAS,YAAY,KAAa;IAChC,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE", "debugId": null}}, {"offset": {"line": 3742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/services/brand-scoped-artifacts-service.ts"], "sourcesContent": ["// Brand-scoped artifacts service\r\n// This service ensures artifacts are properly scoped to the current brand\r\n\r\nimport { BrandScopedStorage, STORAGE_FEATURES } from './brand-scoped-storage';\r\nimport type {\r\n  Artifact,\r\n  ArtifactFolder,\r\n  ArtifactUploadConfig,\r\n  ArtifactCategory,\r\n  ArtifactUploadType,\r\n  ArtifactUsageType,\r\n  ArtifactTextOverlay\r\n} from '@/lib/types/artifacts';\r\n\r\n// Default upload configuration\r\nconst DEFAULT_UPLOAD_CONFIG: ArtifactUploadConfig = {\r\n  maxFileSize: 20 * 1024 * 1024, // 20MB\r\n  allowedTypes: [\r\n    'image/jpeg',\r\n    'image/png',\r\n    'image/webp',\r\n    'image/gif',\r\n    'image/svg+xml',\r\n    'application/pdf',\r\n    'text/plain'\r\n  ],\r\n  generateThumbnails: true,\r\n  extractMetadata: true,\r\n  performImageAnalysis: true,\r\n  storage: {\r\n    provider: 'local',\r\n    basePath: '/uploads/artifacts',\r\n    publicUrl: '/api/artifacts'\r\n  }\r\n};\r\n\r\nexport class BrandScopedArtifactsService {\r\n  private artifacts: Map<string, Artifact> = new Map();\r\n  private folders: Map<string, ArtifactFolder> = new Map();\r\n  private config: ArtifactUploadConfig = DEFAULT_UPLOAD_CONFIG;\r\n  private fileCache: Map<string, File> = new Map();\r\n  private brandId: string | null = null;\r\n  private artifactsStorage: BrandScopedStorage | null = null;\r\n  private foldersStorage: BrandScopedStorage | null = null;\r\n\r\n  constructor(config?: Partial<ArtifactUploadConfig>) {\r\n    if (config) {\r\n      this.config = { ...DEFAULT_UPLOAD_CONFIG, ...config };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Set the current brand and reload artifacts for that brand\r\n   */\r\n  setBrand(brandId: string | null): void {\r\n    if (this.brandId === brandId) {\r\n      return; // No change needed\r\n    }\r\n\r\n    console.log(`🔄 Switching artifacts service to brand: ${brandId || 'none'}`);\r\n\r\n    // Clear current data\r\n    this.artifacts.clear();\r\n    this.folders.clear();\r\n    this.fileCache.clear();\r\n\r\n    // Update brand ID and storage\r\n    this.brandId = brandId;\r\n\r\n    if (brandId) {\r\n      this.artifactsStorage = new BrandScopedStorage({ brandId, feature: STORAGE_FEATURES.ARTIFACTS });\r\n      this.foldersStorage = new BrandScopedStorage({ brandId, feature: STORAGE_FEATURES.ARTIFACT_FOLDERS });\r\n\r\n      // Load artifacts for the new brand\r\n      this.loadArtifactsFromStorage();\r\n      this.initializeDefaultFolders();\r\n    } else {\r\n      this.artifactsStorage = null;\r\n      this.foldersStorage = null;\r\n      console.log(`🚫 No brand selected, artifacts service disabled`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get current brand ID\r\n   */\r\n  getCurrentBrandId(): string | null {\r\n    return this.brandId;\r\n  }\r\n\r\n  /**\r\n   * Check if service is ready (has a brand selected)\r\n   */\r\n  isReady(): boolean {\r\n    return this.brandId !== null && this.artifactsStorage !== null;\r\n  }\r\n\r\n  /**\r\n   * Load artifacts from brand-scoped storage\r\n   */\r\n  private loadArtifactsFromStorage(): void {\r\n    if (!this.artifactsStorage) {\r\n      console.warn('Cannot load artifacts: no brand selected');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const artifacts = this.artifactsStorage.getItem<Artifact[]>();\r\n\r\n      if (artifacts && Array.isArray(artifacts)) {\r\n        console.log(`✅ Found ${artifacts.length} artifacts for brand ${this.brandId}`);\r\n\r\n        let validArtifacts = 0;\r\n        artifacts.forEach((artifact: any) => {\r\n          // Validate artifact structure before processing\r\n          if (!artifact || typeof artifact !== 'object') {\r\n            console.warn('Skipping invalid artifact data:', artifact);\r\n            return;\r\n          }\r\n\r\n          // Ensure required properties exist\r\n          if (!artifact.id || !artifact.name) {\r\n            console.warn('Skipping artifact with missing required properties:', artifact);\r\n            return;\r\n          }\r\n\r\n          // Validate and fix timestamps\r\n          if (!artifact.timestamps || typeof artifact.timestamps !== 'object') {\r\n            console.warn('Artifact missing timestamps, creating default:', artifact.id);\r\n            artifact.timestamps = {\r\n              created: new Date(),\r\n              modified: new Date(),\r\n              uploaded: new Date()\r\n            };\r\n          } else {\r\n            try {\r\n              artifact.timestamps.created = artifact.timestamps.created ? new Date(artifact.timestamps.created) : new Date();\r\n              artifact.timestamps.modified = artifact.timestamps.modified ? new Date(artifact.timestamps.modified) : new Date();\r\n              artifact.timestamps.uploaded = artifact.timestamps.uploaded ? new Date(artifact.timestamps.uploaded) : new Date();\r\n            } catch (dateError) {\r\n              console.warn('Invalid timestamps in artifact, using current date:', artifact.id);\r\n              artifact.timestamps = {\r\n                created: new Date(),\r\n                modified: new Date(),\r\n                uploaded: new Date()\r\n              };\r\n            }\r\n          }\r\n\r\n          // Validate and fix usage data\r\n          if (!artifact.usage || typeof artifact.usage !== 'object') {\r\n            artifact.usage = {\r\n              count: 0,\r\n              lastUsed: null\r\n            };\r\n          } else {\r\n            if (artifact.usage.lastUsed) {\r\n              try {\r\n                artifact.usage.lastUsed = new Date(artifact.usage.lastUsed);\r\n              } catch (dateError) {\r\n                artifact.usage.lastUsed = null;\r\n              }\r\n            }\r\n            if (typeof artifact.usage.count !== 'number') {\r\n              artifact.usage.count = 0;\r\n            }\r\n          }\r\n\r\n          // Ensure other required properties\r\n          if (typeof artifact.isActive !== 'boolean') {\r\n            artifact.isActive = false;\r\n          }\r\n\r\n          this.artifacts.set(artifact.id, artifact as Artifact);\r\n          console.log(`📋 Loaded artifact: ${artifact.name}, isActive: ${artifact.isActive}, usageType: ${artifact.usageType || 'unknown'}`);\r\n          validArtifacts++;\r\n        });\r\n\r\n        console.log(`🎯 Successfully loaded ${validArtifacts} valid artifacts for brand ${this.brandId}`);\r\n      } else {\r\n        console.log(`📂 No artifacts found for brand ${this.brandId}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(`Failed to load artifacts for brand ${this.brandId}:`, error);\r\n      // Clear corrupted artifact data\r\n      if (this.artifactsStorage) {\r\n        console.log('🧹 Clearing corrupted artifact data');\r\n        this.artifactsStorage.removeItem();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Save artifacts to brand-scoped storage\r\n   */\r\n  private async saveArtifactsToStorage(): Promise<void> {\r\n    if (!this.artifactsStorage) {\r\n      console.warn('Cannot save artifacts: no brand selected');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const artifacts = Array.from(this.artifacts.values());\r\n      console.log(`💾 Saving ${artifacts.length} artifacts for brand ${this.brandId}`);\r\n\r\n      this.artifactsStorage.setItem(artifacts);\r\n      console.log(`✅ Artifacts saved successfully for brand ${this.brandId}`);\r\n    } catch (error) {\r\n      console.error(`Failed to save artifacts for brand ${this.brandId}:`, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Load folders from brand-scoped storage\r\n   */\r\n  private loadFoldersFromStorage(): void {\r\n    if (!this.foldersStorage) {\r\n      console.warn('Cannot load folders: no brand selected');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const folders = this.foldersStorage.getItem<ArtifactFolder[]>();\r\n\r\n      if (folders && Array.isArray(folders)) {\r\n        let validFolders = 0;\r\n        folders.forEach((folder: any) => {\r\n          // Validate folder structure before processing\r\n          if (!folder || typeof folder !== 'object') {\r\n            console.warn('Skipping invalid folder data:', folder);\r\n            return;\r\n          }\r\n\r\n          // Ensure required properties exist\r\n          if (!folder.id || !folder.name) {\r\n            console.warn('Skipping folder with missing required properties:', folder);\r\n            return;\r\n          }\r\n\r\n          // Ensure metadata exists and has required properties\r\n          if (!folder.metadata || typeof folder.metadata !== 'object') {\r\n            console.warn('Folder missing metadata, creating default:', folder.id);\r\n            folder.metadata = {\r\n              created: new Date(),\r\n              modified: new Date()\r\n            };\r\n          } else {\r\n            // Validate and convert date properties\r\n            try {\r\n              folder.metadata.created = folder.metadata.created ? new Date(folder.metadata.created) : new Date();\r\n              folder.metadata.modified = folder.metadata.modified ? new Date(folder.metadata.modified) : new Date();\r\n            } catch (dateError) {\r\n              console.warn('Invalid date in folder metadata, using current date:', folder.id);\r\n              folder.metadata.created = new Date();\r\n              folder.metadata.modified = new Date();\r\n            }\r\n          }\r\n\r\n          // Ensure other required properties\r\n          if (!folder.artifactIds || !Array.isArray(folder.artifactIds)) {\r\n            folder.artifactIds = [];\r\n          }\r\n\r\n          if (typeof folder.isDefault !== 'boolean') {\r\n            folder.isDefault = false;\r\n          }\r\n\r\n          if (!folder.type) {\r\n            folder.type = 'custom';\r\n          }\r\n\r\n          this.folders.set(folder.id, folder as ArtifactFolder);\r\n          validFolders++;\r\n        });\r\n        console.log(`✅ Loaded ${validFolders} valid folders for brand ${this.brandId}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(`Failed to load folders for brand ${this.brandId}:`, error);\r\n      // Clear corrupted folder data\r\n      if (this.foldersStorage) {\r\n        console.log('🧹 Clearing corrupted folder data');\r\n        this.foldersStorage.removeItem();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Save folders to brand-scoped storage\r\n   */\r\n  private async saveFoldersToStorage(): Promise<void> {\r\n    if (!this.foldersStorage) {\r\n      console.warn('Cannot save folders: no brand selected');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const folders = Array.from(this.folders.values());\r\n      this.foldersStorage.setItem(folders);\r\n      console.log(`✅ Folders saved successfully for brand ${this.brandId}`);\r\n    } catch (error) {\r\n      console.error(`Failed to save folders for brand ${this.brandId}:`, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize default folders for the brand\r\n   */\r\n  private initializeDefaultFolders(): void {\r\n    this.loadFoldersFromStorage();\r\n\r\n    // Create default folders if they don't exist\r\n    const defaultFolders = [\r\n      { id: 'previous-posts', name: 'Previous Posts', description: 'Your past social media posts' },\r\n      { id: 'products', name: 'Products', description: 'Product images and information' },\r\n      { id: 'discounts', name: 'Discounts', description: 'Promotional materials and offers' },\r\n      { id: 'references', name: 'References', description: 'Reference materials and inspiration' }\r\n    ];\r\n\r\n    let foldersAdded = false;\r\n    defaultFolders.forEach(({ id, name, description }) => {\r\n      if (!this.folders.has(id)) {\r\n        const folder: ArtifactFolder = {\r\n          id,\r\n          name,\r\n          description,\r\n          artifactIds: [],\r\n          metadata: {\r\n            created: new Date(),\r\n            modified: new Date()\r\n          }\r\n        };\r\n        this.folders.set(id, folder);\r\n        foldersAdded = true;\r\n      }\r\n    });\r\n\r\n    if (foldersAdded) {\r\n      this.saveFoldersToStorage();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get all artifacts for the current brand\r\n   */\r\n  getAllArtifacts(): Artifact[] {\r\n    if (!this.isReady()) {\r\n      console.warn('Artifacts service not ready: no brand selected');\r\n      return [];\r\n    }\r\n    return Array.from(this.artifacts.values());\r\n  }\r\n\r\n  /**\r\n   * Get active artifacts for the current brand\r\n   */\r\n  getActiveArtifacts(): Artifact[] {\r\n    if (!this.isReady()) {\r\n      console.warn('Artifacts service not ready: no brand selected');\r\n      return [];\r\n    }\r\n\r\n    console.log(`🔍 getActiveArtifacts called for brand ${this.brandId}:`);\r\n    const allArtifacts = this.getAllArtifacts();\r\n    const activeArtifacts = allArtifacts.filter(artifact => artifact.isActive);\r\n\r\n    console.log(`📊 Total artifacts for brand ${this.brandId}: ${allArtifacts.length}`);\r\n    console.log(`✅ Active artifacts for brand ${this.brandId}: ${activeArtifacts.length}`);\r\n\r\n    return activeArtifacts;\r\n  }\r\n\r\n  /**\r\n   * Set artifact active status\r\n   */\r\n  setArtifactActive(artifactId: string, isActive: boolean): void {\r\n    if (!this.isReady()) {\r\n      console.warn('Artifacts service not ready: no brand selected');\r\n      return;\r\n    }\r\n\r\n    console.log(`🔧 setArtifactActive called for brand ${this.brandId}: ${artifactId} -> ${isActive}`);\r\n\r\n    const artifact = this.artifacts.get(artifactId);\r\n    if (artifact) {\r\n      console.log(`✅ Artifact found: ${artifact.name}, current isActive: ${artifact.isActive}`);\r\n      artifact.isActive = isActive;\r\n      artifact.timestamps.modified = new Date();\r\n      this.artifacts.set(artifactId, artifact);\r\n\r\n      // Save to storage\r\n      this.saveArtifactsToStorage();\r\n      console.log(`💾 Artifact updated and saved for brand ${this.brandId}: ${artifact.name} isActive: ${isActive}`);\r\n    } else {\r\n      console.warn(`❌ Artifact not found for brand ${this.brandId}: ${artifactId}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get artifact by ID\r\n   */\r\n  getArtifact(artifactId: string): Artifact | undefined {\r\n    if (!this.isReady()) {\r\n      console.warn('Artifacts service not ready: no brand selected');\r\n      return undefined;\r\n    }\r\n    return this.artifacts.get(artifactId);\r\n  }\r\n\r\n  /**\r\n   * Get all folders for the current brand\r\n   */\r\n  getAllFolders(): ArtifactFolder[] {\r\n    if (!this.isReady()) {\r\n      console.warn('Artifacts service not ready: no brand selected');\r\n      return [];\r\n    }\r\n    return Array.from(this.folders.values());\r\n  }\r\n\r\n  /**\r\n   * Generate unique ID\r\n   */\r\n  private generateId(): string {\r\n    return `artifact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  /**\r\n   * Upload and process new artifacts with enhanced configuration\r\n   */\r\n  async uploadArtifacts(\r\n    files: File[],\r\n    category?: ArtifactCategory,\r\n    options?: {\r\n      uploadType?: ArtifactUploadType;\r\n      usageType?: ArtifactUsageType;\r\n      folderId?: string;\r\n      textOverlay?: ArtifactTextOverlay;\r\n      isActive?: boolean;\r\n      customName?: string;\r\n      instructions?: string;\r\n    }\r\n  ): Promise<Artifact[]> {\r\n    if (!this.isReady()) {\r\n      throw new Error('Artifacts service not ready: no brand selected');\r\n    }\r\n\r\n    const uploadedArtifacts: Artifact[] = [];\r\n\r\n    for (const file of files) {\r\n      try {\r\n        // Validate file\r\n        this.validateFile(file);\r\n\r\n        // Generate unique ID\r\n        const id = this.generateId();\r\n\r\n        // Process file and extract metadata\r\n        const metadata = await this.extractMetadata(file);\r\n\r\n        // Generate file path (simulate file storage)\r\n        const filePath = `/uploads/artifacts/${this.brandId}/${id}_${file.name}`;\r\n\r\n        // Generate thumbnail path for images\r\n        const thumbnailPath = metadata.mimeType.startsWith('image/')\r\n          ? `/uploads/artifacts/${this.brandId}/thumbnails/${id}_thumb.jpg`\r\n          : undefined;\r\n\r\n        // Create artifact object\r\n        const artifact: Artifact = {\r\n          id,\r\n          name: options?.customName || file.name.replace(/\\.[^/.]+$/, ''),\r\n          type: this.getArtifactType(file),\r\n          category: category || 'general',\r\n          uploadType: options?.uploadType || 'image',\r\n          usageType: options?.usageType || 'reference',\r\n          folderId: options?.folderId || 'default',\r\n          fileUrl: filePath,\r\n          thumbnailUrl: thumbnailPath,\r\n          filePath,\r\n          thumbnailPath,\r\n          metadata,\r\n          instructions: options?.instructions || '',\r\n          textOverlay: options?.textOverlay || {},\r\n          isActive: options?.isActive ?? false,\r\n          timestamps: {\r\n            created: new Date(),\r\n            modified: new Date(),\r\n            uploaded: new Date()\r\n          },\r\n          usage: {\r\n            count: 0,\r\n            lastUsed: null\r\n          },\r\n          tags: [],\r\n          directives: []\r\n        };\r\n\r\n        // Store file in cache (simulate file storage)\r\n        this.fileCache.set(id, file);\r\n\r\n        // Add to artifacts map\r\n        this.artifacts.set(id, artifact);\r\n        uploadedArtifacts.push(artifact);\r\n\r\n        console.log(`✅ Uploaded artifact for brand ${this.brandId}: ${artifact.name}`);\r\n      } catch (error) {\r\n        console.error(`Failed to upload file ${file.name}:`, error);\r\n        throw error;\r\n      }\r\n    }\r\n\r\n    // Save to storage\r\n    await this.saveArtifactsToStorage();\r\n\r\n    console.log(`🎯 Successfully uploaded ${uploadedArtifacts.length} artifacts for brand ${this.brandId}`);\r\n    return uploadedArtifacts;\r\n  }\r\n\r\n  /**\r\n   * Validate file before upload\r\n   */\r\n  private validateFile(file: File): void {\r\n    if (file.size > this.config.maxFileSize) {\r\n      throw new Error(`File ${file.name} is too large. Maximum size is ${this.config.maxFileSize / (1024 * 1024)}MB`);\r\n    }\r\n\r\n    if (!this.config.allowedTypes.includes(file.type)) {\r\n      throw new Error(`File type ${file.type} is not allowed`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Extract metadata from file\r\n   */\r\n  private async extractMetadata(file: File): Promise<any> {\r\n    const metadata: any = {\r\n      name: file.name,\r\n      size: file.size,\r\n      mimeType: file.type,\r\n      lastModified: new Date(file.lastModified)\r\n    };\r\n\r\n    // Add image-specific metadata\r\n    if (file.type.startsWith('image/')) {\r\n      try {\r\n        const dimensions = await this.getImageDimensions(file);\r\n        metadata.dimensions = dimensions;\r\n      } catch (error) {\r\n        console.warn('Failed to get image dimensions:', error);\r\n      }\r\n    }\r\n\r\n    return metadata;\r\n  }\r\n\r\n  /**\r\n   * Get image dimensions\r\n   */\r\n  private getImageDimensions(file: File): Promise<{ width: number; height: number }> {\r\n    return new Promise((resolve, reject) => {\r\n      const img = new Image();\r\n      const url = URL.createObjectURL(file);\r\n\r\n      img.onload = () => {\r\n        URL.revokeObjectURL(url);\r\n        resolve({ width: img.naturalWidth, height: img.naturalHeight });\r\n      };\r\n\r\n      img.onerror = () => {\r\n        URL.revokeObjectURL(url);\r\n        reject(new Error('Failed to load image'));\r\n      };\r\n\r\n      img.src = url;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get artifact type from file\r\n   */\r\n  private getArtifactType(file: File): string {\r\n    if (file.type.startsWith('image/')) return 'image';\r\n    if (file.type === 'application/pdf') return 'document';\r\n    if (file.type.startsWith('text/')) return 'text';\r\n    return 'other';\r\n  }\r\n\r\n  /**\r\n   * Clear all data for the current brand\r\n   */\r\n  clearBrandData(): void {\r\n    if (!this.brandId) {\r\n      console.warn('Cannot clear brand data: no brand selected');\r\n      return;\r\n    }\r\n\r\n    console.log(`🗑️ Clearing all artifacts data for brand ${this.brandId}`);\r\n\r\n    // Clear in-memory data\r\n    this.artifacts.clear();\r\n    this.folders.clear();\r\n    this.fileCache.clear();\r\n\r\n    // Clear storage\r\n    if (this.artifactsStorage) {\r\n      this.artifactsStorage.removeItem();\r\n    }\r\n    if (this.foldersStorage) {\r\n      this.foldersStorage.removeItem();\r\n    }\r\n\r\n    console.log(`✅ Cleared all artifacts data for brand ${this.brandId}`);\r\n  }\r\n\r\n  /**\r\n   * Get storage statistics for the current brand\r\n   */\r\n  getStorageStats(): { artifacts: any; folders: any } | null {\r\n    if (!this.isReady() || !this.artifactsStorage || !this.foldersStorage) {\r\n      return null;\r\n    }\r\n\r\n    return {\r\n      artifacts: this.artifactsStorage.getStorageStats(),\r\n      folders: this.foldersStorage.getStorageStats()\r\n    };\r\n  }\r\n}\r\n\r\n// Create a singleton instance\r\nexport const brandScopedArtifactsService = new BrandScopedArtifactsService();\r\n"], "names": [], "mappings": "AAAA,iCAAiC;AACjC,0EAA0E;;;;;AAE1E;;AAWA,+BAA+B;AAC/B,MAAM,wBAA8C;IAClD,aAAa,KAAK,OAAO;IACzB,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,oBAAoB;IACpB,iBAAiB;IACjB,sBAAsB;IACtB,SAAS;QACP,UAAU;QACV,UAAU;QACV,WAAW;IACb;AACF;AAEO,MAAM;IACH,YAAmC,IAAI,MAAM;IAC7C,UAAuC,IAAI,MAAM;IACjD,SAA+B,sBAAsB;IACrD,YAA+B,IAAI,MAAM;IACzC,UAAyB,KAAK;IAC9B,mBAA8C,KAAK;IACnD,iBAA4C,KAAK;IAEzD,YAAY,MAAsC,CAAE;QAClD,IAAI,QAAQ;YACV,IAAI,CAAC,MAAM,GAAG;gBAAE,GAAG,qBAAqB;gBAAE,GAAG,MAAM;YAAC;QACtD;IACF;IAEA;;GAEC,GACD,SAAS,OAAsB,EAAQ;QACrC,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS;YAC5B,QAAQ,mBAAmB;QAC7B;QAEA,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,WAAW,QAAQ;QAE3E,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC,KAAK;QACpB,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,IAAI,CAAC,SAAS,CAAC,KAAK;QAEpB,8BAA8B;QAC9B,IAAI,CAAC,OAAO,GAAG;QAEf,IAAI,SAAS;YACX,IAAI,CAAC,gBAAgB,GAAG,IAAI,oJAAA,CAAA,qBAAkB,CAAC;gBAAE;gBAAS,SAAS,oJAAA,CAAA,mBAAgB,CAAC,SAAS;YAAC;YAC9F,IAAI,CAAC,cAAc,GAAG,IAAI,oJAAA,CAAA,qBAAkB,CAAC;gBAAE;gBAAS,SAAS,oJAAA,CAAA,mBAAgB,CAAC,gBAAgB;YAAC;YAEnG,mCAAmC;YACnC,IAAI,CAAC,wBAAwB;YAC7B,IAAI,CAAC,wBAAwB;QAC/B,OAAO;YACL,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,cAAc,GAAG;YACtB,QAAQ,GAAG,CAAC,CAAC,gDAAgD,CAAC;QAChE;IACF;IAEA;;GAEC,GACD,oBAAmC;QACjC,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA;;GAEC,GACD,UAAmB;QACjB,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,gBAAgB,KAAK;IAC5D;IAEA;;GAEC,GACD,AAAQ,2BAAiC;QACvC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IAAI;YACF,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,OAAO;YAE/C,IAAI,aAAa,MAAM,OAAO,CAAC,YAAY;gBACzC,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,MAAM,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,EAAE;gBAE7E,IAAI,iBAAiB;gBACrB,UAAU,OAAO,CAAC,CAAC;oBACjB,gDAAgD;oBAChD,IAAI,CAAC,YAAY,OAAO,aAAa,UAAU;wBAC7C,QAAQ,IAAI,CAAC,mCAAmC;wBAChD;oBACF;oBAEA,mCAAmC;oBACnC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,EAAE;wBAClC,QAAQ,IAAI,CAAC,uDAAuD;wBACpE;oBACF;oBAEA,8BAA8B;oBAC9B,IAAI,CAAC,SAAS,UAAU,IAAI,OAAO,SAAS,UAAU,KAAK,UAAU;wBACnE,QAAQ,IAAI,CAAC,kDAAkD,SAAS,EAAE;wBAC1E,SAAS,UAAU,GAAG;4BACpB,SAAS,IAAI;4BACb,UAAU,IAAI;4BACd,UAAU,IAAI;wBAChB;oBACF,OAAO;wBACL,IAAI;4BACF,SAAS,UAAU,CAAC,OAAO,GAAG,SAAS,UAAU,CAAC,OAAO,GAAG,IAAI,KAAK,SAAS,UAAU,CAAC,OAAO,IAAI,IAAI;4BACxG,SAAS,UAAU,CAAC,QAAQ,GAAG,SAAS,UAAU,CAAC,QAAQ,GAAG,IAAI,KAAK,SAAS,UAAU,CAAC,QAAQ,IAAI,IAAI;4BAC3G,SAAS,UAAU,CAAC,QAAQ,GAAG,SAAS,UAAU,CAAC,QAAQ,GAAG,IAAI,KAAK,SAAS,UAAU,CAAC,QAAQ,IAAI,IAAI;wBAC7G,EAAE,OAAO,WAAW;4BAClB,QAAQ,IAAI,CAAC,uDAAuD,SAAS,EAAE;4BAC/E,SAAS,UAAU,GAAG;gCACpB,SAAS,IAAI;gCACb,UAAU,IAAI;gCACd,UAAU,IAAI;4BAChB;wBACF;oBACF;oBAEA,8BAA8B;oBAC9B,IAAI,CAAC,SAAS,KAAK,IAAI,OAAO,SAAS,KAAK,KAAK,UAAU;wBACzD,SAAS,KAAK,GAAG;4BACf,OAAO;4BACP,UAAU;wBACZ;oBACF,OAAO;wBACL,IAAI,SAAS,KAAK,CAAC,QAAQ,EAAE;4BAC3B,IAAI;gCACF,SAAS,KAAK,CAAC,QAAQ,GAAG,IAAI,KAAK,SAAS,KAAK,CAAC,QAAQ;4BAC5D,EAAE,OAAO,WAAW;gCAClB,SAAS,KAAK,CAAC,QAAQ,GAAG;4BAC5B;wBACF;wBACA,IAAI,OAAO,SAAS,KAAK,CAAC,KAAK,KAAK,UAAU;4BAC5C,SAAS,KAAK,CAAC,KAAK,GAAG;wBACzB;oBACF;oBAEA,mCAAmC;oBACnC,IAAI,OAAO,SAAS,QAAQ,KAAK,WAAW;wBAC1C,SAAS,QAAQ,GAAG;oBACtB;oBAEA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE;oBAChC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,IAAI,CAAC,YAAY,EAAE,SAAS,QAAQ,CAAC,aAAa,EAAE,SAAS,SAAS,IAAI,WAAW;oBACjI;gBACF;gBAEA,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,eAAe,2BAA2B,EAAE,IAAI,CAAC,OAAO,EAAE;YAClG,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,IAAI,CAAC,OAAO,EAAE;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACrE,gCAAgC;YAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,gBAAgB,CAAC,UAAU;YAClC;QACF;IACF;IAEA;;GAEC,GACD,MAAc,yBAAwC;QACpD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IAAI;YACF,MAAM,YAAY,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;YAClD,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,EAAE;YAE/E,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YAC9B,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,IAAI,CAAC,OAAO,EAAE;QACxE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;QACvE;IACF;IAEA;;GAEC,GACD,AAAQ,yBAA+B;QACrC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IAAI;YACF,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,OAAO;YAE3C,IAAI,WAAW,MAAM,OAAO,CAAC,UAAU;gBACrC,IAAI,eAAe;gBACnB,QAAQ,OAAO,CAAC,CAAC;oBACf,8CAA8C;oBAC9C,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;wBACzC,QAAQ,IAAI,CAAC,iCAAiC;wBAC9C;oBACF;oBAEA,mCAAmC;oBACnC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;wBAC9B,QAAQ,IAAI,CAAC,qDAAqD;wBAClE;oBACF;oBAEA,qDAAqD;oBACrD,IAAI,CAAC,OAAO,QAAQ,IAAI,OAAO,OAAO,QAAQ,KAAK,UAAU;wBAC3D,QAAQ,IAAI,CAAC,8CAA8C,OAAO,EAAE;wBACpE,OAAO,QAAQ,GAAG;4BAChB,SAAS,IAAI;4BACb,UAAU,IAAI;wBAChB;oBACF,OAAO;wBACL,uCAAuC;wBACvC,IAAI;4BACF,OAAO,QAAQ,CAAC,OAAO,GAAG,OAAO,QAAQ,CAAC,OAAO,GAAG,IAAI,KAAK,OAAO,QAAQ,CAAC,OAAO,IAAI,IAAI;4BAC5F,OAAO,QAAQ,CAAC,QAAQ,GAAG,OAAO,QAAQ,CAAC,QAAQ,GAAG,IAAI,KAAK,OAAO,QAAQ,CAAC,QAAQ,IAAI,IAAI;wBACjG,EAAE,OAAO,WAAW;4BAClB,QAAQ,IAAI,CAAC,wDAAwD,OAAO,EAAE;4BAC9E,OAAO,QAAQ,CAAC,OAAO,GAAG,IAAI;4BAC9B,OAAO,QAAQ,CAAC,QAAQ,GAAG,IAAI;wBACjC;oBACF;oBAEA,mCAAmC;oBACnC,IAAI,CAAC,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,WAAW,GAAG;wBAC7D,OAAO,WAAW,GAAG,EAAE;oBACzB;oBAEA,IAAI,OAAO,OAAO,SAAS,KAAK,WAAW;wBACzC,OAAO,SAAS,GAAG;oBACrB;oBAEA,IAAI,CAAC,OAAO,IAAI,EAAE;wBAChB,OAAO,IAAI,GAAG;oBAChB;oBAEA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE;oBAC5B;gBACF;gBACA,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,aAAa,yBAAyB,EAAE,IAAI,CAAC,OAAO,EAAE;YAChF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACnE,8BAA8B;YAC9B,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,cAAc,CAAC,UAAU;YAChC;QACF;IACF;IAEA;;GAEC,GACD,MAAc,uBAAsC;QAClD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IAAI;YACF,MAAM,UAAU,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM;YAC9C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAC5B,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,IAAI,CAAC,OAAO,EAAE;QACtE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;QACrE;IACF;IAEA;;GAEC,GACD,AAAQ,2BAAiC;QACvC,IAAI,CAAC,sBAAsB;QAE3B,6CAA6C;QAC7C,MAAM,iBAAiB;YACrB;gBAAE,IAAI;gBAAkB,MAAM;gBAAkB,aAAa;YAA+B;YAC5F;gBAAE,IAAI;gBAAY,MAAM;gBAAY,aAAa;YAAiC;YAClF;gBAAE,IAAI;gBAAa,MAAM;gBAAa,aAAa;YAAmC;YACtF;gBAAE,IAAI;gBAAc,MAAM;gBAAc,aAAa;YAAsC;SAC5F;QAED,IAAI,eAAe;QACnB,eAAe,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK;gBACzB,MAAM,SAAyB;oBAC7B;oBACA;oBACA;oBACA,aAAa,EAAE;oBACf,UAAU;wBACR,SAAS,IAAI;wBACb,UAAU,IAAI;oBAChB;gBACF;gBACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI;gBACrB,eAAe;YACjB;QACF;QAEA,IAAI,cAAc;YAChB,IAAI,CAAC,oBAAoB;QAC3B;IACF;IAEA;;GAEC,GACD,kBAA8B;QAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI;YACnB,QAAQ,IAAI,CAAC;YACb,OAAO,EAAE;QACX;QACA,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;IACzC;IAEA;;GAEC,GACD,qBAAiC;QAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI;YACnB,QAAQ,IAAI,CAAC;YACb,OAAO,EAAE;QACX;QAEA,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QACrE,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ;QAEzE,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,aAAa,MAAM,EAAE;QAClF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,gBAAgB,MAAM,EAAE;QAErF,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,UAAkB,EAAE,QAAiB,EAAQ;QAC7D,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI;YACnB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,WAAW,IAAI,EAAE,UAAU;QAEjG,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,UAAU;YACZ,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,SAAS,IAAI,CAAC,oBAAoB,EAAE,SAAS,QAAQ,EAAE;YACxF,SAAS,QAAQ,GAAG;YACpB,SAAS,UAAU,CAAC,QAAQ,GAAG,IAAI;YACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY;YAE/B,kBAAkB;YAClB,IAAI,CAAC,sBAAsB;YAC3B,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,WAAW,EAAE,UAAU;QAC/G,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,+BAA+B,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,YAAY;QAC9E;IACF;IAEA;;GAEC,GACD,YAAY,UAAkB,EAAwB;QACpD,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI;YACnB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;IAC5B;IAEA;;GAEC,GACD,gBAAkC;QAChC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI;YACnB,QAAQ,IAAI,CAAC;YACb,OAAO,EAAE;QACX;QACA,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM;IACvC;IAEA;;GAEC,GACD,AAAQ,aAAqB;QAC3B,OAAO,CAAC,SAAS,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC5E;IAEA;;GAEC,GACD,MAAM,gBACJ,KAAa,EACb,QAA2B,EAC3B,OAQC,EACoB;QACrB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,oBAAgC,EAAE;QAExC,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI;gBACF,gBAAgB;gBAChB,IAAI,CAAC,YAAY,CAAC;gBAElB,qBAAqB;gBACrB,MAAM,KAAK,IAAI,CAAC,UAAU;gBAE1B,oCAAoC;gBACpC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC;gBAE5C,6CAA6C;gBAC7C,MAAM,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,IAAI,EAAE;gBAExE,qCAAqC;gBACrC,MAAM,gBAAgB,SAAS,QAAQ,CAAC,UAAU,CAAC,YAC/C,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,GAAG,UAAU,CAAC,GAC/D;gBAEJ,yBAAyB;gBACzB,MAAM,WAAqB;oBACzB;oBACA,MAAM,SAAS,cAAc,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa;oBAC5D,MAAM,IAAI,CAAC,eAAe,CAAC;oBAC3B,UAAU,YAAY;oBACtB,YAAY,SAAS,cAAc;oBACnC,WAAW,SAAS,aAAa;oBACjC,UAAU,SAAS,YAAY;oBAC/B,SAAS;oBACT,cAAc;oBACd;oBACA;oBACA;oBACA,cAAc,SAAS,gBAAgB;oBACvC,aAAa,SAAS,eAAe,CAAC;oBACtC,UAAU,SAAS,YAAY;oBAC/B,YAAY;wBACV,SAAS,IAAI;wBACb,UAAU,IAAI;wBACd,UAAU,IAAI;oBAChB;oBACA,OAAO;wBACL,OAAO;wBACP,UAAU;oBACZ;oBACA,MAAM,EAAE;oBACR,YAAY,EAAE;gBAChB;gBAEA,8CAA8C;gBAC9C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI;gBAEvB,uBAAuB;gBACvB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI;gBACvB,kBAAkB,IAAI,CAAC;gBAEvB,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,IAAI,EAAE;YAC/E,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;gBACrD,MAAM;YACR;QACF;QAEA,kBAAkB;QAClB,MAAM,IAAI,CAAC,sBAAsB;QAEjC,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,kBAAkB,MAAM,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,EAAE;QACtG,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,aAAa,IAAU,EAAQ;QACrC,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACvC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,+BAA+B,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;QAChH;QAEA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YACjD,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC;QACzD;IACF;IAEA;;GAEC,GACD,MAAc,gBAAgB,IAAU,EAAgB;QACtD,MAAM,WAAgB;YACpB,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,IAAI;YACnB,cAAc,IAAI,KAAK,KAAK,YAAY;QAC1C;QAEA,8BAA8B;QAC9B,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAClC,IAAI;gBACF,MAAM,aAAa,MAAM,IAAI,CAAC,kBAAkB,CAAC;gBACjD,SAAS,UAAU,GAAG;YACxB,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,mCAAmC;YAClD;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,mBAAmB,IAAU,EAA8C;QACjF,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,MAAM,IAAI;YAChB,MAAM,MAAM,IAAI,eAAe,CAAC;YAEhC,IAAI,MAAM,GAAG;gBACX,IAAI,eAAe,CAAC;gBACpB,QAAQ;oBAAE,OAAO,IAAI,YAAY;oBAAE,QAAQ,IAAI,aAAa;gBAAC;YAC/D;YAEA,IAAI,OAAO,GAAG;gBACZ,IAAI,eAAe,CAAC;gBACpB,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,GAAG,GAAG;QACZ;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,IAAU,EAAU;QAC1C,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,OAAO;QAC3C,IAAI,KAAK,IAAI,KAAK,mBAAmB,OAAO;QAC5C,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,OAAO;QAC1C,OAAO;IACT;IAEA;;GAEC,GACD,iBAAuB;QACrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,IAAI,CAAC,OAAO,EAAE;QAEvE,uBAAuB;QACvB,IAAI,CAAC,SAAS,CAAC,KAAK;QACpB,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,IAAI,CAAC,SAAS,CAAC,KAAK;QAEpB,gBAAgB;QAChB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,UAAU;QAClC;QACA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,UAAU;QAChC;QAEA,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,IAAI,CAAC,OAAO,EAAE;IACtE;IAEA;;GAEC,GACD,kBAA2D;QACzD,IAAI,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACrE,OAAO;QACT;QAEA,OAAO;YACL,WAAW,IAAI,CAAC,gBAAgB,CAAC,eAAe;YAChD,SAAS,IAAI,CAAC,cAAc,CAAC,eAAe;QAC9C;IACF;AACF;AAGO,MAAM,8BAA8B,IAAI", "debugId": null}}, {"offset": {"line": 4284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/contexts/unified-brand-context.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';\r\nimport { useBrandProfilesFirebaseFirst } from '@/hooks/use-brand-profiles-firebase-first';\r\nimport { brandScopedArtifactsService } from '@/lib/services/brand-scoped-artifacts-service';\r\nimport { BrandScopedStorage, STORAGE_FEATURES, migrateAllGlobalStorage } from '@/lib/services/brand-scoped-storage';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\n\r\ninterface UnifiedBrandContextType {\r\n  // Current brand state\r\n  currentBrand: CompleteBrandProfile | null;\r\n\r\n  // All brands\r\n  brands: CompleteBrandProfile[];\r\n\r\n  // Loading states\r\n  loading: boolean;\r\n  saving: boolean;\r\n\r\n  // Actions\r\n  selectBrand: (brand: CompleteBrandProfile | null) => void;\r\n  saveProfile: (profile: CompleteBrandProfile) => Promise<string>;\r\n  updateProfile: (profileId: string, updates: Partial<CompleteBrandProfile>) => Promise<void>;\r\n  deleteProfile: (profileId: string) => Promise<void>;\r\n  refreshBrands: () => Promise<void>;\r\n\r\n  // Brand-scoped storage helpers\r\n  getBrandStorage: (feature: string) => BrandScopedStorage | null;\r\n  clearBrandData: (brandId: string) => void;\r\n  migrateBrandData: (brandId: string) => void;\r\n\r\n  // Error handling\r\n  error: string | null;\r\n}\r\n\r\nconst UnifiedBrandContext = createContext<UnifiedBrandContextType | undefined>(undefined);\r\n\r\ninterface UnifiedBrandProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport function UnifiedBrandProvider({ children }: UnifiedBrandProviderProps) {\r\n  const {\r\n    profiles: brands,\r\n    currentProfile,\r\n    loading,\r\n    saving,\r\n    error,\r\n    saveProfile,\r\n    updateProfile,\r\n    deleteProfile,\r\n    setCurrentProfile,\r\n    reload: refreshBrands,\r\n  } = useBrandProfilesFirebaseFirst();\r\n\r\n  const [currentBrand, setCurrentBrand] = useState<CompleteBrandProfile | null>(null);\r\n  const [brandScopedServices, setBrandScopedServices] = useState<Map<string, any>>(new Map());\r\n\r\n  // Use refs to store current values for event handlers\r\n  const currentBrandRef = useRef<CompleteBrandProfile | null>(null);\r\n  const setCurrentProfileRef = useRef(setCurrentProfile);\r\n  const updateAllBrandScopedServicesRef = useRef<(brand: CompleteBrandProfile | null) => void>();\r\n\r\n  // Update refs when values change\r\n  useEffect(() => {\r\n    currentBrandRef.current = currentBrand;\r\n  }, [currentBrand]);\r\n\r\n  useEffect(() => {\r\n    setCurrentProfileRef.current = setCurrentProfile;\r\n  }, [setCurrentProfile]);\r\n\r\n  // Sync current brand with the hook's current profile\r\n  useEffect(() => {\r\n    console.log('🔄 Unified brand sync effect triggered:', {\r\n      currentProfile: currentProfile?.businessName || currentProfile?.name,\r\n      currentBrand: currentBrand?.businessName || currentBrand?.name,\r\n      brandsCount: brands.length\r\n    });\r\n\r\n    // Only sync if currentProfile exists and is different from currentBrand\r\n    if (currentProfile && currentProfile !== currentBrand) {\r\n      console.log('✅ Syncing currentBrand with currentProfile:', currentProfile.businessName || currentProfile.name);\r\n      setCurrentBrand(currentProfile);\r\n      updateAllBrandScopedServices(currentProfile);\r\n    } else if (!currentProfile && !currentBrand && brands.length > 0) {\r\n      // Auto-select first brand only if no brand is selected at all and brands exist\r\n      // This should only happen on initial load, not during navigation\r\n      const savedBrandId = localStorage.getItem('selectedBrandId');\r\n      let brandToSelect = brands[0]; // Default to first brand\r\n\r\n      // Try to restore previously selected brand\r\n      if (savedBrandId) {\r\n        const savedBrand = brands.find(b => b.id === savedBrandId);\r\n        if (savedBrand) {\r\n          brandToSelect = savedBrand;\r\n          console.log('🔄 Restoring previously selected brand:', brandToSelect.businessName || brandToSelect.name);\r\n        }\r\n      }\r\n\r\n      if (!currentBrand) { // Only select if no brand is currently selected\r\n        console.log('🎯 Auto-selecting brand for initial load:', brandToSelect.businessName || brandToSelect.name);\r\n        setCurrentBrand(brandToSelect);\r\n        setCurrentProfile(brandToSelect);\r\n        updateAllBrandScopedServices(brandToSelect);\r\n      }\r\n    }\r\n  }, [currentProfile, brands.length]); // Removed currentBrand and setCurrentProfile to prevent infinite loop\r\n\r\n  // Update all brand-scoped services when brand changes\r\n  const updateAllBrandScopedServices = useCallback((brand: CompleteBrandProfile | null) => {\r\n    const brandId = brand?.id || null;\r\n    const brandName = brand?.businessName || brand?.name || 'none';\r\n\r\n    console.log('🔄 Updating ALL brand-scoped services for brand:', brandName, 'ID:', brandId);\r\n\r\n    try {\r\n      // Update artifacts service\r\n      brandScopedArtifactsService.setBrand(brandId);\r\n      console.log('✅ Updated artifacts service for brand:', brandName);\r\n\r\n      // TODO: Update other brand-scoped services here\r\n      // - Social media service\r\n      // - Content calendar service\r\n      // - Creative studio service\r\n      // - Quick content service\r\n      // - etc.\r\n\r\n      // Store the current brand ID for other services to use\r\n      if (brandId) {\r\n        localStorage.setItem('currentBrandId', brandId);\r\n        localStorage.setItem('currentBrandName', brandName);\r\n      } else {\r\n        localStorage.removeItem('currentBrandId');\r\n        localStorage.removeItem('currentBrandName');\r\n      }\r\n\r\n      console.log('✅ All brand-scoped services updated successfully');\r\n    } catch (error) {\r\n      console.error('❌ Failed to update brand-scoped services:', error);\r\n    }\r\n  }, []);\r\n\r\n  // Update the ref when the function changes\r\n  useEffect(() => {\r\n    updateAllBrandScopedServicesRef.current = updateAllBrandScopedServices;\r\n  }, [updateAllBrandScopedServices]);\r\n\r\n  const selectBrand = useCallback((brand: CompleteBrandProfile | null) => {\r\n    const brandName = brand?.businessName || brand?.name || 'null';\r\n    console.log('🎯 Unified selectBrand called with:', brandName);\r\n    console.log('📊 Current state before selection:', {\r\n      currentBrand: currentBrand?.businessName || currentBrand?.name,\r\n      currentProfile: currentProfile?.businessName || currentProfile?.name\r\n    });\r\n\r\n    // Log color information for debugging\r\n    if (brand) {\r\n      console.log('🎨 Selecting brand with colors:', {\r\n        primaryColor: brand.primaryColor,\r\n        accentColor: brand.accentColor,\r\n        backgroundColor: brand.backgroundColor\r\n      });\r\n    }\r\n\r\n    // Update both states immediately\r\n    setCurrentBrand(brand);\r\n    setCurrentProfile(brand);\r\n\r\n    // Update all brand-scoped services\r\n    updateAllBrandScopedServices(brand);\r\n\r\n    // Force update color persistence immediately\r\n    if (brand) {\r\n      const colorData = {\r\n        primaryColor: brand.primaryColor,\r\n        accentColor: brand.accentColor,\r\n        backgroundColor: brand.backgroundColor,\r\n        brandId: brand.id,\r\n        brandName: brand.businessName || brand.name,\r\n        updatedAt: new Date().toISOString()\r\n      };\r\n      localStorage.setItem('brandColors', JSON.stringify(colorData));\r\n      console.log('💾 Force updated color persistence:', colorData);\r\n    }\r\n\r\n    // Trigger a custom event for other components to listen to\r\n    const event = new CustomEvent('brandChanged', {\r\n      detail: {\r\n        brand,\r\n        brandId: brand?.id || null,\r\n        brandName: brandName\r\n      }\r\n    });\r\n    window.dispatchEvent(event);\r\n\r\n    console.log('✅ Unified brand selection completed, new brand:', brandName);\r\n  }, [currentBrand, currentProfile, setCurrentProfile, updateAllBrandScopedServices]);\r\n\r\n  // localStorage restoration is now handled in the main sync effect above\r\n\r\n  // Enhanced brand persistence - save both ID and full data\r\n  useEffect(() => {\r\n    if (currentBrand?.id) {\r\n      localStorage.setItem('selectedBrandId', currentBrand.id);\r\n      // Also save the full brand data for immediate restoration\r\n      localStorage.setItem('currentBrandData', JSON.stringify({\r\n        id: currentBrand.id,\r\n        businessName: currentBrand.businessName,\r\n        name: currentBrand.name,\r\n        primaryColor: currentBrand.primaryColor,\r\n        accentColor: currentBrand.accentColor,\r\n        backgroundColor: currentBrand.backgroundColor,\r\n        logoDataUrl: currentBrand.logoDataUrl,\r\n        // Store essential data for immediate UI restoration\r\n        businessType: currentBrand.businessType,\r\n        location: currentBrand.location,\r\n        description: currentBrand.description\r\n      }));\r\n      console.log('💾 Saved brand data to localStorage for persistence:', currentBrand.businessName || currentBrand.name);\r\n    } else {\r\n      localStorage.removeItem('selectedBrandId');\r\n      localStorage.removeItem('currentBrandData');\r\n      console.log('🗑️ Cleared brand data from localStorage');\r\n    }\r\n  }, [currentBrand]);\r\n\r\n  // Helper function to get brand-scoped storage for any feature\r\n  const getBrandStorage = useCallback((feature: string): BrandScopedStorage | null => {\r\n    if (!currentBrand?.id) {\r\n      console.warn(`Cannot create brand-scoped storage for ${feature}: no brand selected`);\r\n      return null;\r\n    }\r\n\r\n    return new BrandScopedStorage({ brandId: currentBrand.id, feature });\r\n  }, [currentBrand]);\r\n\r\n  // Helper function to clear all data for a specific brand\r\n  const clearBrandData = useCallback((brandId: string) => {\r\n    console.log('🗑️ Clearing all data for brand:', brandId);\r\n    BrandScopedStorage.clearBrandData(brandId);\r\n  }, []);\r\n\r\n  // Helper function to migrate global data to brand-scoped storage\r\n  const migrateBrandData = useCallback((brandId: string) => {\r\n    console.log('🔄 Migrating global data to brand-scoped for brand:', brandId);\r\n    const features = Object.values(STORAGE_FEATURES);\r\n    migrateAllGlobalStorage(brandId, features);\r\n  }, []);\r\n\r\n  // Listen for brand changes from other contexts (backward compatibility)\r\n  useEffect(() => {\r\n    const handleBrandChange = (event: any) => {\r\n      if (event.detail && event.detail.brand) {\r\n        const brand = event.detail.brand;\r\n        const brandName = brand.businessName || brand.name;\r\n        console.log('🔄 Unified context received brand change event from other context:', brandName);\r\n\r\n        // Only update if it's different from current brand\r\n        const currentBrandValue = currentBrandRef.current;\r\n        if (!currentBrandValue || currentBrandValue.id !== brand.id) {\r\n          console.log('🔄 Updating unified context with new brand:', brandName);\r\n          setCurrentBrand(brand);\r\n          setCurrentProfileRef.current(brand);\r\n          if (updateAllBrandScopedServicesRef.current) {\r\n            updateAllBrandScopedServicesRef.current(brand);\r\n          }\r\n        } else {\r\n          console.log('🔄 Brand already current in unified context:', brandName);\r\n        }\r\n      }\r\n    };\r\n\r\n    // Listen for the original brand context changes\r\n    const handleOriginalBrandChange = (event: any) => {\r\n      console.log('🔄 Unified context received original brand change event:', event.detail);\r\n      if (event.detail && event.detail.brand) {\r\n        handleBrandChange(event);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('brandChanged', handleBrandChange);\r\n    window.addEventListener('originalBrandChanged', handleOriginalBrandChange);\r\n\r\n    return () => {\r\n      window.removeEventListener('brandChanged', handleBrandChange);\r\n      window.removeEventListener('originalBrandChanged', handleOriginalBrandChange);\r\n    };\r\n  }, []); // Empty dependencies to prevent re-registering listeners\r\n\r\n  const contextValue: UnifiedBrandContextType = {\r\n    currentBrand,\r\n    brands,\r\n    loading,\r\n    saving,\r\n    error,\r\n    selectBrand,\r\n    saveProfile,\r\n    updateProfile,\r\n    deleteProfile,\r\n    refreshBrands,\r\n    getBrandStorage,\r\n    clearBrandData,\r\n    migrateBrandData,\r\n  };\r\n\r\n  return (\r\n    <UnifiedBrandContext.Provider value={contextValue}>\r\n      {children}\r\n    </UnifiedBrandContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useUnifiedBrand() {\r\n  const context = useContext(UnifiedBrandContext);\r\n  if (context === undefined) {\r\n    throw new Error('useUnifiedBrand must be used within a UnifiedBrandProvider');\r\n  }\r\n  return context;\r\n}\r\n\r\n// Convenience hooks\r\nexport function useCurrentBrand(): CompleteBrandProfile | null {\r\n  const { currentBrand } = useUnifiedBrand();\r\n  return currentBrand;\r\n}\r\n\r\nexport function useBrands(): CompleteBrandProfile[] {\r\n  const { brands } = useUnifiedBrand();\r\n  return brands;\r\n}\r\n\r\nexport function useBrandActions() {\r\n  const { selectBrand, saveProfile, updateProfile, deleteProfile, refreshBrands } = useUnifiedBrand();\r\n  return { selectBrand, saveProfile, updateProfile, deleteProfile, refreshBrands };\r\n}\r\n\r\nexport function useBrandStorage(feature: string): BrandScopedStorage | null {\r\n  const { getBrandStorage } = useUnifiedBrand();\r\n  return getBrandStorage(feature);\r\n}\r\n\r\n// Hook to listen for brand changes\r\nexport function useBrandChangeListener(callback: (brand: CompleteBrandProfile | null) => void) {\r\n  const { currentBrand } = useUnifiedBrand();\r\n  const callbackRef = useRef(callback);\r\n\r\n  // Update callback ref when callback changes\r\n  useEffect(() => {\r\n    callbackRef.current = callback;\r\n  }, [callback]);\r\n\r\n  // Call callback when brand changes (no callback in dependencies to prevent infinite loop)\r\n  useEffect(() => {\r\n    callbackRef.current(currentBrand);\r\n  }, [currentBrand]);\r\n\r\n  // Listen for brand change events (no callback in dependencies)\r\n  useEffect(() => {\r\n    const handleBrandChange = (event: any) => {\r\n      if (event.detail && event.detail.brand) {\r\n        callbackRef.current(event.detail.brand);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('brandChanged', handleBrandChange);\r\n    return () => window.removeEventListener('brandChanged', handleBrandChange);\r\n  }, []); // Empty dependencies to prevent re-registration\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAmCA,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAuC;AAMxE,SAAS,qBAAqB,EAAE,QAAQ,EAA6B;IAC1E,MAAM,EACJ,UAAU,MAAM,EAChB,cAAc,EACd,OAAO,EACP,MAAM,EACN,KAAK,EACL,WAAW,EACX,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,QAAQ,aAAa,EACtB,GAAG,CAAA,GAAA,6JAAA,CAAA,gCAA6B,AAAD;IAEhC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,IAAI;IAErF,sDAAsD;IACtD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA+B;IAC5D,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACpC,MAAM,kCAAkC,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAE7C,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,OAAO,GAAG;IAC5B,GAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB,OAAO,GAAG;IACjC,GAAG;QAAC;KAAkB;IAEtB,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,2CAA2C;YACrD,gBAAgB,gBAAgB,gBAAgB,gBAAgB;YAChE,cAAc,cAAc,gBAAgB,cAAc;YAC1D,aAAa,OAAO,MAAM;QAC5B;QAEA,wEAAwE;QACxE,IAAI,kBAAkB,mBAAmB,cAAc;YACrD,QAAQ,GAAG,CAAC,+CAA+C,eAAe,YAAY,IAAI,eAAe,IAAI;YAC7G,gBAAgB;YAChB,6BAA6B;QAC/B,OAAO,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,OAAO,MAAM,GAAG,GAAG;YAChE,+EAA+E;YAC/E,iEAAiE;YACjE,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,gBAAgB,MAAM,CAAC,EAAE,EAAE,yBAAyB;YAExD,2CAA2C;YAC3C,IAAI,cAAc;gBAChB,MAAM,aAAa,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC7C,IAAI,YAAY;oBACd,gBAAgB;oBAChB,QAAQ,GAAG,CAAC,2CAA2C,cAAc,YAAY,IAAI,cAAc,IAAI;gBACzG;YACF;YAEA,IAAI,CAAC,cAAc;gBACjB,QAAQ,GAAG,CAAC,6CAA6C,cAAc,YAAY,IAAI,cAAc,IAAI;gBACzG,gBAAgB;gBAChB,kBAAkB;gBAClB,6BAA6B;YAC/B;QACF;IACF,GAAG;QAAC;QAAgB,OAAO,MAAM;KAAC,GAAG,sEAAsE;IAE3G,sDAAsD;IACtD,MAAM,+BAA+B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChD,MAAM,UAAU,OAAO,MAAM;QAC7B,MAAM,YAAY,OAAO,gBAAgB,OAAO,QAAQ;QAExD,QAAQ,GAAG,CAAC,oDAAoD,WAAW,OAAO;QAElF,IAAI;YACF,2BAA2B;YAC3B,iKAAA,CAAA,8BAA2B,CAAC,QAAQ,CAAC;YACrC,QAAQ,GAAG,CAAC,0CAA0C;YAEtD,gDAAgD;YAChD,yBAAyB;YACzB,6BAA6B;YAC7B,4BAA4B;YAC5B,0BAA0B;YAC1B,SAAS;YAET,uDAAuD;YACvD,IAAI,SAAS;gBACX,aAAa,OAAO,CAAC,kBAAkB;gBACvC,aAAa,OAAO,CAAC,oBAAoB;YAC3C,OAAO;gBACL,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;QAC7D;IACF,GAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gCAAgC,OAAO,GAAG;IAC5C,GAAG;QAAC;KAA6B;IAEjC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,YAAY,OAAO,gBAAgB,OAAO,QAAQ;QACxD,QAAQ,GAAG,CAAC,uCAAuC;QACnD,QAAQ,GAAG,CAAC,sCAAsC;YAChD,cAAc,cAAc,gBAAgB,cAAc;YAC1D,gBAAgB,gBAAgB,gBAAgB,gBAAgB;QAClE;QAEA,sCAAsC;QACtC,IAAI,OAAO;YACT,QAAQ,GAAG,CAAC,mCAAmC;gBAC7C,cAAc,MAAM,YAAY;gBAChC,aAAa,MAAM,WAAW;gBAC9B,iBAAiB,MAAM,eAAe;YACxC;QACF;QAEA,iCAAiC;QACjC,gBAAgB;QAChB,kBAAkB;QAElB,mCAAmC;QACnC,6BAA6B;QAE7B,6CAA6C;QAC7C,IAAI,OAAO;YACT,MAAM,YAAY;gBAChB,cAAc,MAAM,YAAY;gBAChC,aAAa,MAAM,WAAW;gBAC9B,iBAAiB,MAAM,eAAe;gBACtC,SAAS,MAAM,EAAE;gBACjB,WAAW,MAAM,YAAY,IAAI,MAAM,IAAI;gBAC3C,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;YACnD,QAAQ,GAAG,CAAC,uCAAuC;QACrD;QAEA,2DAA2D;QAC3D,MAAM,QAAQ,IAAI,YAAY,gBAAgB;YAC5C,QAAQ;gBACN;gBACA,SAAS,OAAO,MAAM;gBACtB,WAAW;YACb;QACF;QACA,OAAO,aAAa,CAAC;QAErB,QAAQ,GAAG,CAAC,mDAAmD;IACjE,GAAG;QAAC;QAAc;QAAgB;QAAmB;KAA6B;IAElF,wEAAwE;IAExE,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,IAAI;YACpB,aAAa,OAAO,CAAC,mBAAmB,aAAa,EAAE;YACvD,0DAA0D;YAC1D,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACtD,IAAI,aAAa,EAAE;gBACnB,cAAc,aAAa,YAAY;gBACvC,MAAM,aAAa,IAAI;gBACvB,cAAc,aAAa,YAAY;gBACvC,aAAa,aAAa,WAAW;gBACrC,iBAAiB,aAAa,eAAe;gBAC7C,aAAa,aAAa,WAAW;gBACrC,oDAAoD;gBACpD,cAAc,aAAa,YAAY;gBACvC,UAAU,aAAa,QAAQ;gBAC/B,aAAa,aAAa,WAAW;YACvC;YACA,QAAQ,GAAG,CAAC,wDAAwD,aAAa,YAAY,IAAI,aAAa,IAAI;QACpH,OAAO;YACL,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,QAAQ,GAAG,CAAC;QACd;IACF,GAAG;QAAC;KAAa;IAEjB,8DAA8D;IAC9D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,CAAC,cAAc,IAAI;YACrB,QAAQ,IAAI,CAAC,CAAC,uCAAuC,EAAE,QAAQ,mBAAmB,CAAC;YACnF,OAAO;QACT;QAEA,OAAO,IAAI,oJAAA,CAAA,qBAAkB,CAAC;YAAE,SAAS,aAAa,EAAE;YAAE;QAAQ;IACpE,GAAG;QAAC;KAAa;IAEjB,yDAAyD;IACzD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,QAAQ,GAAG,CAAC,oCAAoC;QAChD,oJAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;IACpC,GAAG,EAAE;IAEL,iEAAiE;IACjE,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,QAAQ,GAAG,CAAC,uDAAuD;QACnE,MAAM,WAAW,OAAO,MAAM,CAAC,oJAAA,CAAA,mBAAgB;QAC/C,CAAA,GAAA,oJAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS;IACnC,GAAG,EAAE;IAEL,wEAAwE;IACxE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB,CAAC;YACzB,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,KAAK,EAAE;gBACtC,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK;gBAChC,MAAM,YAAY,MAAM,YAAY,IAAI,MAAM,IAAI;gBAClD,QAAQ,GAAG,CAAC,sEAAsE;gBAElF,mDAAmD;gBACnD,MAAM,oBAAoB,gBAAgB,OAAO;gBACjD,IAAI,CAAC,qBAAqB,kBAAkB,EAAE,KAAK,MAAM,EAAE,EAAE;oBAC3D,QAAQ,GAAG,CAAC,+CAA+C;oBAC3D,gBAAgB;oBAChB,qBAAqB,OAAO,CAAC;oBAC7B,IAAI,gCAAgC,OAAO,EAAE;wBAC3C,gCAAgC,OAAO,CAAC;oBAC1C;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC,gDAAgD;gBAC9D;YACF;QACF;QAEA,gDAAgD;QAChD,MAAM,4BAA4B,CAAC;YACjC,QAAQ,GAAG,CAAC,4DAA4D,MAAM,MAAM;YACpF,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,KAAK,EAAE;gBACtC,kBAAkB;YACpB;QACF;QAEA,OAAO,gBAAgB,CAAC,gBAAgB;QACxC,OAAO,gBAAgB,CAAC,wBAAwB;QAEhD,OAAO;YACL,OAAO,mBAAmB,CAAC,gBAAgB;YAC3C,OAAO,mBAAmB,CAAC,wBAAwB;QACrD;IACF,GAAG,EAAE,GAAG,yDAAyD;IAEjE,MAAM,eAAwC;QAC5C;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,oBAAoB,QAAQ;QAAC,OAAO;kBAClC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,YAAY,EAAE,GAAG;IACzB,OAAO;AACT;AAEO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,OAAO;AACT;AAEO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG;IAClF,OAAO;QAAE;QAAa;QAAa;QAAe;QAAe;IAAc;AACjF;AAEO,SAAS,gBAAgB,OAAe;IAC7C,MAAM,EAAE,eAAe,EAAE,GAAG;IAC5B,OAAO,gBAAgB;AACzB;AAGO,SAAS,uBAAuB,QAAsD;IAC3F,MAAM,EAAE,YAAY,EAAE,GAAG;IACzB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY,OAAO,GAAG;IACxB,GAAG;QAAC;KAAS;IAEb,0FAA0F;IAC1F,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY,OAAO,CAAC;IACtB,GAAG;QAAC;KAAa;IAEjB,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB,CAAC;YACzB,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,KAAK,EAAE;gBACtC,YAAY,OAAO,CAAC,MAAM,MAAM,CAAC,KAAK;YACxC;QACF;QAEA,OAAO,gBAAgB,CAAC,gBAAgB;QACxC,OAAO,IAAM,OAAO,mBAAmB,CAAC,gBAAgB;IAC1D,GAAG,EAAE,GAAG,gDAAgD;AAC1D", "debugId": null}}, {"offset": {"line": 4615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/brand/unified-brand-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport {\r\n  ChevronDown,\r\n  Plus,\r\n  Building2,\r\n  Check,\r\n  Settings,\r\n  Sparkles\r\n} from 'lucide-react';\r\nimport { useUnifiedBrand } from '@/contexts/unified-brand-context';\r\nimport { useRouter } from 'next/navigation';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\n\r\nexport function UnifiedBrandSelector() {\r\n  const router = useRouter();\r\n  const {\r\n    currentBrand,\r\n    brands,\r\n    loading,\r\n    selectBrand,\r\n  } = useUnifiedBrand();\r\n\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  const handleBrandSelect = (brand: CompleteBrandProfile) => {\r\n    console.log('🎯 Brand selector: selecting brand:', brand.businessName || brand.name);\r\n    selectBrand(brand);\r\n    setIsOpen(false);\r\n  };\r\n\r\n  const handleCreateNew = () => {\r\n    setIsOpen(false);\r\n    router.push('/brand-profile?mode=create');\r\n  };\r\n\r\n  const handleManageBrands = () => {\r\n    setIsOpen(false);\r\n    router.push('/brands');\r\n  };\r\n\r\n  // Get brand initials for avatar\r\n  const getBrandInitials = (brand: CompleteBrandProfile): string => {\r\n    const name = brand.businessName || brand.name || 'Brand';\r\n    return name\r\n      .split(' ')\r\n      .map(word => word.charAt(0))\r\n      .join('')\r\n      .toUpperCase()\r\n      .slice(0, 2);\r\n  };\r\n\r\n  // Get brand display name\r\n  const getBrandDisplayName = (brand: CompleteBrandProfile): string => {\r\n    return brand.businessName || brand.name || 'Unnamed Brand';\r\n  };\r\n\r\n  // Get brand type/category\r\n  const getBrandType = (brand: CompleteBrandProfile): string => {\r\n    return brand.businessType || 'Business';\r\n  };\r\n\r\n  // Loading state\r\n  if (loading && brands.length === 0) {\r\n    return (\r\n      <Button variant=\"outline\" disabled className=\"min-w-[200px]\">\r\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400 mr-2\"></div>\r\n        Loading brands...\r\n      </Button>\r\n    );\r\n  }\r\n\r\n  // No brands state\r\n  if (!loading && brands.length === 0) {\r\n    return (\r\n      <Button \r\n        onClick={handleCreateNew}\r\n        className=\"min-w-[200px] bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\"\r\n      >\r\n        <Plus className=\"w-4 h-4 mr-2\" />\r\n        Create First Brand\r\n      </Button>\r\n    );\r\n  }\r\n\r\n  // No current brand selected\r\n  if (!currentBrand) {\r\n    return (\r\n      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"outline\" className=\"min-w-[200px]\">\r\n            <Building2 className=\"w-4 h-4 mr-2\" />\r\n            Select Brand\r\n            <ChevronDown className=\"w-4 h-4 ml-2\" />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"start\" className=\"w-80\">\r\n          <DropdownMenuLabel className=\"flex items-center gap-2\">\r\n            <Sparkles className=\"w-4 h-4\" />\r\n            Brand Profiles ({brands.length})\r\n          </DropdownMenuLabel>\r\n          <DropdownMenuSeparator />\r\n          \r\n          {brands.map((brand) => (\r\n            <DropdownMenuItem\r\n              key={brand.id}\r\n              onClick={() => handleBrandSelect(brand)}\r\n              className=\"flex items-center gap-3 p-3 cursor-pointer\"\r\n            >\r\n              <Avatar className=\"w-8 h-8\">\r\n                {brand.logoDataUrl ? (\r\n                  <AvatarImage src={brand.logoDataUrl} alt={getBrandDisplayName(brand)} />\r\n                ) : (\r\n                  <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs\">\r\n                    {getBrandInitials(brand)}\r\n                  </AvatarFallback>\r\n                )}\r\n              </Avatar>\r\n              <div className=\"flex-1\">\r\n                <div className=\"font-medium\">{getBrandDisplayName(brand)}</div>\r\n                <div className=\"text-sm text-gray-500\">{getBrandType(brand)}</div>\r\n              </div>\r\n            </DropdownMenuItem>\r\n          ))}\r\n          \r\n          <DropdownMenuSeparator />\r\n          <DropdownMenuItem onClick={handleCreateNew} className=\"flex items-center gap-2\">\r\n            <div className=\"w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center\">\r\n              <Plus className=\"w-4 h-4\" />\r\n            </div>\r\n            <span>Create New Brand</span>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={handleManageBrands} className=\"flex items-center gap-2\">\r\n            <Settings className=\"w-4 h-4 ml-2\" />\r\n            <span>Manage Brands</span>\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  }\r\n\r\n  // Current brand selected\r\n  return (\r\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button \r\n          variant=\"outline\" \r\n          className=\"min-w-[200px] border-2 border-blue-200 hover:border-blue-300 bg-blue-50 hover:bg-blue-100\"\r\n        >\r\n          <div className=\"flex items-center gap-2 flex-1\">\r\n            <Avatar className=\"w-6 h-6\">\r\n              {currentBrand.logoDataUrl ? (\r\n                <AvatarImage src={currentBrand.logoDataUrl} alt={getBrandDisplayName(currentBrand)} />\r\n              ) : (\r\n                <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs\">\r\n                  {getBrandInitials(currentBrand)}\r\n                </AvatarFallback>\r\n              )}\r\n            </Avatar>\r\n            <div className=\"flex-1 text-left\">\r\n              <div className=\"font-medium text-sm\">{getBrandDisplayName(currentBrand)}</div>\r\n              <div className=\"flex items-center gap-1 text-xs text-gray-500\">\r\n                <span>{brands.length} brands</span>\r\n                <Badge variant=\"secondary\" className=\"bg-green-100 text-green-800 text-xs px-1 py-0\">\r\n                  Active\r\n                </Badge>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <ChevronDown className=\"w-4 h-4 ml-2\" />\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"start\" className=\"w-80\">\r\n        <DropdownMenuLabel className=\"flex items-center gap-2\">\r\n          <Sparkles className=\"w-4 h-4\" />\r\n          Brand Profiles ({brands.length})\r\n        </DropdownMenuLabel>\r\n        <DropdownMenuSeparator />\r\n        \r\n        {brands.map((brand) => {\r\n          const isSelected = currentBrand.id === brand.id;\r\n          return (\r\n            <DropdownMenuItem\r\n              key={brand.id}\r\n              onClick={() => handleBrandSelect(brand)}\r\n              className=\"flex items-center gap-3 p-3 cursor-pointer\"\r\n            >\r\n              <Avatar className=\"w-8 h-8\">\r\n                {brand.logoDataUrl ? (\r\n                  <AvatarImage src={brand.logoDataUrl} alt={getBrandDisplayName(brand)} />\r\n                ) : (\r\n                  <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs\">\r\n                    {getBrandInitials(brand)}\r\n                  </AvatarFallback>\r\n                )}\r\n              </Avatar>\r\n              <div className=\"flex-1\">\r\n                <div className=\"font-medium\">{getBrandDisplayName(brand)}</div>\r\n                <div className=\"text-sm text-gray-500\">{getBrandType(brand)}</div>\r\n              </div>\r\n              {isSelected && (\r\n                <Check className=\"w-4 h-4 text-green-600\" />\r\n              )}\r\n            </DropdownMenuItem>\r\n          );\r\n        })}\r\n        \r\n        <DropdownMenuSeparator />\r\n        <DropdownMenuItem onClick={handleCreateNew} className=\"flex items-center gap-2\">\r\n          <div className=\"w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center\">\r\n            <Plus className=\"w-4 h-4\" />\r\n          </div>\r\n          <span>Create New Brand</span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem onClick={handleManageBrands} className=\"flex items-center gap-2\">\r\n          <Settings className=\"w-4 h-4 ml-2\" />\r\n          <span>Manage Brands</span>\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n\r\n// Export as default for easy replacement\r\nexport default UnifiedBrandSelector;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAQA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAvBA;;;;;;;;;;AA0BO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EACJ,YAAY,EACZ,MAAM,EACN,OAAO,EACP,WAAW,EACZ,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,oBAAoB,CAAC;QACzB,QAAQ,GAAG,CAAC,uCAAuC,MAAM,YAAY,IAAI,MAAM,IAAI;QACnF,YAAY;QACZ,UAAU;IACZ;IAEA,MAAM,kBAAkB;QACtB,UAAU;QACV,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB;QACzB,UAAU;QACV,OAAO,IAAI,CAAC;IACd;IAEA,gCAAgC;IAChC,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,YAAY,IAAI,MAAM,IAAI,IAAI;QACjD,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,yBAAyB;IACzB,MAAM,sBAAsB,CAAC;QAC3B,OAAO,MAAM,YAAY,IAAI,MAAM,IAAI,IAAI;IAC7C;IAEA,0BAA0B;IAC1B,MAAM,eAAe,CAAC;QACpB,OAAO,MAAM,YAAY,IAAI;IAC/B;IAEA,gBAAgB;IAChB,IAAI,WAAW,OAAO,MAAM,KAAK,GAAG;QAClC,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,QAAQ;YAAC,WAAU;;8BAC3C,8OAAC;oBAAI,WAAU;;;;;;gBAA0E;;;;;;;IAI/F;IAEA,kBAAkB;IAClB,IAAI,CAAC,WAAW,OAAO,MAAM,KAAK,GAAG;QACnC,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAS;YACT,WAAU;;8BAEV,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;IAIvC;IAEA,4BAA4B;IAC5B,IAAI,CAAC,cAAc;QACjB,qBACE,8OAAC,4IAAA,CAAA,eAAY;YAAC,MAAM;YAAQ,cAAc;;8BACxC,8OAAC,4IAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,WAAU;;0CAClC,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;0CAEtC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAG3B,8OAAC,4IAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAQ,WAAU;;sCAC3C,8OAAC,4IAAA,CAAA,oBAAiB;4BAAC,WAAU;;8CAC3B,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;gCACf,OAAO,MAAM;gCAAC;;;;;;;sCAEjC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;wBAErB,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,4IAAA,CAAA,mBAAgB;gCAEf,SAAS,IAAM,kBAAkB;gCACjC,WAAU;;kDAEV,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDACf,MAAM,WAAW,iBAChB,8OAAC,kIAAA,CAAA,cAAW;4CAAC,KAAK,MAAM,WAAW;4CAAE,KAAK,oBAAoB;;;;;iEAE9D,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,iBAAiB;;;;;;;;;;;kDAIxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAe,oBAAoB;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DAAyB,aAAa;;;;;;;;;;;;;+BAflD,MAAM,EAAE;;;;;sCAoBjB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sCACtB,8OAAC,4IAAA,CAAA,mBAAgB;4BAAC,SAAS;4BAAiB,WAAU;;8CACpD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC,4IAAA,CAAA,mBAAgB;4BAAC,SAAS;4BAAoB,WAAU;;8CACvD,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,yBAAyB;IACzB,qBACE,8OAAC,4IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;8CACf,aAAa,WAAW,iBACvB,8OAAC,kIAAA,CAAA,cAAW;wCAAC,KAAK,aAAa,WAAW;wCAAE,KAAK,oBAAoB;;;;;6DAErE,8OAAC,kIAAA,CAAA,iBAAc;wCAAC,WAAU;kDACvB,iBAAiB;;;;;;;;;;;8CAIxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuB,oBAAoB;;;;;;sDAC1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAM,OAAO,MAAM;wDAAC;;;;;;;8DACrB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;;;;;;;sCAM3F,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAG3B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAQ,WAAU;;kCAC3C,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;;0CAC3B,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;4BACf,OAAO,MAAM;4BAAC;;;;;;;kCAEjC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;oBAErB,OAAO,GAAG,CAAC,CAAC;wBACX,MAAM,aAAa,aAAa,EAAE,KAAK,MAAM,EAAE;wBAC/C,qBACE,8OAAC,4IAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,kBAAkB;4BACjC,WAAU;;8CAEV,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;8CACf,MAAM,WAAW,iBAChB,8OAAC,kIAAA,CAAA,cAAW;wCAAC,KAAK,MAAM,WAAW;wCAAE,KAAK,oBAAoB;;;;;6DAE9D,8OAAC,kIAAA,CAAA,iBAAc;wCAAC,WAAU;kDACvB,iBAAiB;;;;;;;;;;;8CAIxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAe,oBAAoB;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDAAyB,aAAa;;;;;;;;;;;;gCAEtD,4BACC,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;2BAlBd,MAAM,EAAE;;;;;oBAsBnB;kCAEA,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCACtB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS;wBAAiB,WAAU;;0CACpD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS;wBAAoB,WAAU;;0CACvD,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;uCAGe", "debugId": null}}, {"offset": {"line": 5192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/layout/app-sidebar.tsx"], "sourcesContent": ["// src/components/layout/app-sidebar.tsx\r\n\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Link from \"next/link\";\r\nimport {\r\n  Bot,\r\n  CalendarDays,\r\n  Sparkles,\r\n  Link as LinkIcon,\r\n  Wand,\r\n  Settings,\r\n  Zap,\r\n  Calendar,\r\n  Archive,\r\n  LayoutDashboard,\r\n  Building2,\r\n  Palette,\r\n  Coins,\r\n} from \"lucide-react\";\r\nimport { UnifiedBrandSelector } from '@/components/brand/unified-brand-selector';\r\nimport { usePathname } from \"next/navigation\";\r\nimport { CreditsIndicator } from \"@/components/pricing/CreditsDisplay\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport {\r\n  Sidebar,\r\n  SidebarHeader,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarMenu,\r\n  SidebarMenuItem,\r\n  SidebarMenuButton,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function AppSidebar() {\r\n  const pathname = usePathname();\r\n  const isActive = (path: string) => pathname.startsWith(path);\r\n\r\n  return (\r\n    <Sidebar>\r\n      <SidebarHeader>\r\n        <Link href=\"/\" className=\"flex items-center gap-2\">\r\n          <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\r\n            <Sparkles className=\"w-5 h-5 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-xl font-bold text-primary-foreground font-headline\">\r\n            Crevo\r\n          </h1>\r\n        </Link>\r\n\r\n        {/* Unified Brand Selector */}\r\n        <div className=\"px-2 py-2\">\r\n          <UnifiedBrandSelector />\r\n        </div>\r\n      </SidebarHeader>\r\n      <SidebarContent>\r\n        <SidebarMenu>\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              asChild\r\n              isActive={isActive(\"/dashboard\")}\r\n              tooltip=\"Dashboard\"\r\n            >\r\n              <Link href=\"/dashboard\">\r\n                <LayoutDashboard />\r\n                <span>Dashboard</span>\r\n              </Link>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              asChild\r\n              isActive={isActive(\"/brands\")}\r\n              tooltip=\"Manage Brands\"\r\n            >\r\n              <Link href=\"/brands\">\r\n                <Building2 />\r\n                <span>Manage Brands</span>\r\n              </Link>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              asChild\r\n              isActive={isActive(\"/brand-profile\")}\r\n              tooltip=\"Brand Profile\"\r\n            >\r\n              <Link href=\"/brand-profile\">\r\n                <Sparkles />\r\n                <span>Brand Profile</span>\r\n              </Link>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              asChild\r\n              isActive={isActive(\"/quick-content\")}\r\n              tooltip=\"Quick Content\"\r\n            >\r\n              <Link href=\"/quick-content\">\r\n                <Zap />\r\n                <span>Quick Content</span>\r\n              </Link>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              asChild\r\n              isActive={isActive(\"/creative-studio\")}\r\n              tooltip=\"Creative Studio\"\r\n            >\r\n              <Link href=\"/creative-studio\">\r\n                <Palette />\r\n                <span>Creative Studio</span>\r\n              </Link>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              asChild\r\n              isActive={isActive(\"/artifacts-brand-scoped\")}\r\n              tooltip=\"Artifacts\"\r\n            >\r\n              <Link href=\"/artifacts-brand-scoped\">\r\n                <Archive />\r\n                <span>Artifacts</span>\r\n              </Link>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              asChild\r\n              isActive={isActive(\"/content-calendar\")}\r\n              tooltip=\"Content Calendar\"\r\n            >\r\n              <Link href=\"/content-calendar\">\r\n                <Calendar />\r\n                <span>Content Calendar</span>\r\n              </Link>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              asChild\r\n              isActive={isActive(\"/social-connect\")}\r\n              tooltip=\"Social Media Connect\"\r\n            >\r\n              <Link href=\"/social-connect\">\r\n                <LinkIcon />\r\n                <span>Social Media Connect</span>\r\n              </Link>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n\r\n\r\n        </SidebarMenu>\r\n      </SidebarContent>\r\n      <SidebarFooter>\r\n      </SidebarFooter>\r\n    </Sidebar>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;AAIxC;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AAGA;AAvBA;;;;;;;AAiCO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,WAAW,CAAC,OAAiB,SAAS,UAAU,CAAC;IAEvD,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,gBAAa;;kCACZ,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;gCAAG,WAAU;0CAA0D;;;;;;;;;;;;kCAM1E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2JAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;0BAGzB,8OAAC,mIAAA,CAAA,iBAAc;0BACb,cAAA,8OAAC,mIAAA,CAAA,cAAW;;sCACV,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,UAAU,SAAS;gCACnB,SAAQ;0CAER,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,4NAAA,CAAA,kBAAe;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;sCAKZ,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,UAAU,SAAS;gCACnB,SAAQ;0CAER,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,gNAAA,CAAA,YAAS;;;;;sDACV,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;sCAKZ,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,UAAU,SAAS;gCACnB,SAAQ;0CAER,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,0MAAA,CAAA,WAAQ;;;;;sDACT,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;sCAKZ,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,UAAU,SAAS;gCACnB,SAAQ;0CAER,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,gMAAA,CAAA,MAAG;;;;;sDACJ,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;sCAKZ,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,UAAU,SAAS;gCACnB,SAAQ;0CAER,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,wMAAA,CAAA,UAAO;;;;;sDACR,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;sCAKZ,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,UAAU,SAAS;gCACnB,SAAQ;0CAER,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,wMAAA,CAAA,UAAO;;;;;sDACR,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;sCAKZ,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,UAAU,SAAS;gCACnB,SAAQ;0CAER,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,0MAAA,CAAA,WAAQ;;;;;sDACT,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;sCAKZ,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,UAAU,SAAS;gCACnB,SAAQ;0CAER,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,kMAAA,CAAA,OAAQ;;;;;sDACT,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC,mIAAA,CAAA,gBAAa;;;;;;;;;;;AAIpB", "debugId": null}}, {"offset": {"line": 5593, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5674, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5806, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Progress = React.forwardRef<\r\n  React.ElementRef<typeof ProgressPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\r\n>(({ className, value, ...props }, ref) => (\r\n  <ProgressPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ProgressPrimitive.Indicator\r\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\r\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n    />\r\n  </ProgressPrimitive.Root>\r\n))\r\nProgress.displayName = ProgressPrimitive.Root.displayName\r\n\r\nexport { Progress }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5845, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5909, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/firebase/services/brand-profile-service.ts"], "sourcesContent": ["// Brand Profile Firebase service\r\nimport { query, where, orderBy, limit, getDocs, collection } from 'firebase/firestore';\r\nimport { db } from '../config';\r\nimport { DatabaseService } from '../database';\r\nimport { COLLECTIONS, BrandProfileDocument, BrandProfileDocumentSchema } from '../schema';\r\nimport type { BrandProfile, CompleteBrandProfile } from '@/lib/types';\r\n\r\nexport class BrandProfileService extends DatabaseService<BrandProfileDocument> {\r\n  constructor() {\r\n    super(COLLECTIONS.BRAND_PROFILES);\r\n  }\r\n\r\n  // Convert from app BrandProfile to Firestore document\r\n  private toFirestoreDocument(\r\n    profile: BrandProfile | CompleteBrandProfile,\r\n    userId: string\r\n  ): Omit<BrandProfileDocument, 'id' | 'createdAt' | 'updatedAt'> {\r\n    // Helper function to clean undefined values and empty strings\r\n    const cleanObject = (obj: any): any => {\r\n      if (obj === null || obj === undefined || obj === '') return undefined;\r\n      if (Array.isArray(obj)) return obj.filter(item => item !== undefined && item !== null && item !== '');\r\n      if (typeof obj === 'object') {\r\n        const cleaned: any = {};\r\n        for (const [key, value] of Object.entries(obj)) {\r\n          const cleanedValue = cleanObject(value);\r\n          if (cleanedValue !== undefined) {\r\n            cleaned[key] = cleanedValue;\r\n          }\r\n        }\r\n        return Object.keys(cleaned).length > 0 ? cleaned : undefined;\r\n      }\r\n      return obj;\r\n    };\r\n\r\n    // Helper function to validate and clean URL\r\n    const cleanUrl = (url: string | undefined): string | undefined => {\r\n      if (!url || url.trim() === '') return undefined;\r\n      const trimmed = url.trim();\r\n      // Basic URL validation - must start with http:// or https://\r\n      if (trimmed.startsWith('http://') || trimmed.startsWith('https://')) {\r\n        return trimmed;\r\n      }\r\n      // If it looks like a domain, add https://\r\n      if (trimmed.includes('.') && !trimmed.includes(' ')) {\r\n        return `https://${trimmed}`;\r\n      }\r\n      return undefined;\r\n    };\r\n\r\n    const data = {\r\n      userId,\r\n      name: profile.businessName || 'Untitled Business',\r\n      businessType: profile.businessType || 'General',\r\n      description: profile.businessDescription || profile.description || '',\r\n      location: cleanObject(profile.location),\r\n      website: cleanUrl(profile.websiteUrl || (profile as any).website),\r\n      logoDataUrl: profile.logoDataUrl || '', // Added logo support\r\n      // Brand colors - essential for brand consistency\r\n      primaryColor: profile.primaryColor || '#3B82F6',\r\n      accentColor: profile.accentColor || '#10B981',\r\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\r\n      socialMedia: cleanObject({\r\n        instagram: profile.socialMedia?.instagram || '',\r\n        facebook: profile.socialMedia?.facebook || '',\r\n        twitter: profile.socialMedia?.twitter || '',\r\n        linkedin: profile.socialMedia?.linkedin || '',\r\n        tiktok: profile.socialMedia?.tiktok || '',\r\n      }),\r\n      brandColors: Array.isArray(profile.brandColors) ? profile.brandColors.filter(c => c) : [],\r\n      brandFonts: Array.isArray(profile.brandFonts) ? profile.brandFonts.filter(f => f) : [],\r\n      visualStyle: profile.visualStyle || '',\r\n      targetAudience: profile.targetAudience || '',\r\n      brandVoice: profile.brandVoice || '',\r\n\r\n      // Store analysis data for future use\r\n      analysisData: {\r\n        websiteUrl: profile.websiteUrl || '',\r\n        lastAnalyzed: new Date().toISOString(),\r\n        visualStyle: profile.visualStyle || '',\r\n        writingTone: profile.writingTone || '',\r\n        contentThemes: profile.contentThemes || '',\r\n        hasBeenAnalyzed: !!(profile.visualStyle && profile.writingTone && profile.contentThemes)\r\n      },\r\n\r\n      services: Array.isArray(profile.services) ? profile.services.map(service => ({\r\n        name: service?.name || '',\r\n        description: service?.description || '',\r\n        category: service?.category || '',\r\n      })).filter(s => s.name) : [],\r\n      designExamples: Array.isArray(profile.designExamples) ? profile.designExamples.map(example => ({\r\n        url: cleanUrl(example?.url) || '',\r\n        description: example?.description || '',\r\n        type: (example?.type as 'logo' | 'banner' | 'post' | 'story' | 'other') || 'other',\r\n      })).filter(e => e.url) : [],\r\n      isComplete: 'services' in profile && Array.isArray(profile.services) && profile.services.length > 0,\r\n      version: '1.0',\r\n    };\r\n\r\n    // Remove any remaining undefined values\r\n    return cleanObject(data);\r\n  }\r\n\r\n  // Convert from Firestore document to app BrandProfile\r\n  private fromFirestoreDocument(doc: BrandProfileDocument): CompleteBrandProfile {\r\n    return {\r\n      // Basic Information\r\n      businessName: doc.name,\r\n      businessType: doc.businessType || '',\r\n      location: doc.location || '',\r\n      description: doc.description || '',\r\n\r\n      // Website and Logo\r\n      websiteUrl: doc.website || '',\r\n      logoDataUrl: doc.logoDataUrl || '', // Added logo support\r\n\r\n      // Services & Target Audience\r\n      services: doc.services || [],\r\n      targetAudience: doc.targetAudience || '',\r\n      keyFeatures: '', // Not stored in Firebase yet\r\n      competitiveAdvantages: '', // Not stored in Firebase yet\r\n\r\n      // Contact Information\r\n      contactPhone: '', // Not stored in Firebase yet\r\n      contactEmail: '', // Not stored in Firebase yet\r\n      contactAddress: '', // Not stored in Firebase yet\r\n\r\n      // Brand Identity & Voice\r\n      visualStyle: doc.visualStyle || '',\r\n      writingTone: '', // Not stored in Firebase yet\r\n      contentThemes: '', // Not stored in Firebase yet\r\n\r\n      // Brand Colors\r\n      primaryColor: doc.primaryColor || '#3B82F6',\r\n      accentColor: doc.accentColor || '#10B981',\r\n      backgroundColor: doc.backgroundColor || '#F8FAFC',\r\n\r\n      // Social Media\r\n      facebookUrl: doc.socialMedia?.facebook || '',\r\n      instagramUrl: doc.socialMedia?.instagram || '',\r\n      twitterUrl: doc.socialMedia?.twitter || '',\r\n      linkedinUrl: doc.socialMedia?.linkedin || '',\r\n\r\n      // Design Examples\r\n      designExamples: doc.designExamples || [],\r\n\r\n      // Metadata fields\r\n      id: doc.id,\r\n      createdAt: doc.createdAt instanceof Date ? doc.createdAt.toISOString() : new Date().toISOString(),\r\n      updatedAt: doc.updatedAt instanceof Date ? doc.updatedAt.toISOString() : new Date().toISOString(),\r\n      version: doc.version || '1.0',\r\n    };\r\n  }\r\n\r\n  // Save brand profile\r\n  async saveBrandProfile(\r\n    profile: BrandProfile | CompleteBrandProfile,\r\n    userId: string\r\n  ): Promise<string> {\r\n    const firestoreData = this.toFirestoreDocument(profile, userId);\r\n\r\n    // Validate data\r\n    const validatedData = BrandProfileDocumentSchema.omit({\r\n      id: true,\r\n      createdAt: true,\r\n      updatedAt: true,\r\n    }).parse(firestoreData);\r\n\r\n    return await this.create(validatedData);\r\n  }\r\n\r\n  // Update brand profile\r\n  async updateBrandProfile(\r\n    id: string,\r\n    profile: Partial<BrandProfile | CompleteBrandProfile>\r\n  ): Promise<void> {\r\n    const updateData: Partial<BrandProfileDocument> = {};\r\n\r\n    if (profile.businessName) updateData.name = profile.businessName;\r\n    if (profile.businessType) updateData.businessType = profile.businessType;\r\n    if (profile.businessDescription) updateData.description = profile.businessDescription;\r\n    if (profile.location) updateData.location = profile.location;\r\n    if (profile.website) updateData.website = profile.website;\r\n    if (profile.socialMedia) updateData.socialMedia = profile.socialMedia;\r\n    if (profile.brandColors) updateData.brandColors = profile.brandColors;\r\n    if (profile.brandFonts) updateData.brandFonts = profile.brandFonts;\r\n    if (profile.visualStyle) updateData.visualStyle = profile.visualStyle;\r\n    if (profile.targetAudience) updateData.targetAudience = profile.targetAudience;\r\n    if (profile.brandVoice) updateData.brandVoice = profile.brandVoice;\r\n    if (profile.services) {\r\n      updateData.services = profile.services.map(service => ({\r\n        name: service.name,\r\n        description: service.description,\r\n        category: service.category,\r\n      }));\r\n      updateData.isComplete = true;\r\n    }\r\n    if (profile.designExamples) {\r\n      updateData.designExamples = profile.designExamples.map(example => ({\r\n        url: example.url,\r\n        description: example.description,\r\n        type: example.type as 'logo' | 'banner' | 'post' | 'story' | 'other',\r\n      }));\r\n    }\r\n\r\n    await this.update(id, updateData);\r\n  }\r\n\r\n  // Get user's brand profiles as app format\r\n  async getUserBrandProfiles(userId: string): Promise<CompleteBrandProfile[]> {\r\n    const docs = await this.getByUserId(userId, {\r\n      orderBy: 'updatedAt',\r\n      orderDirection: 'desc',\r\n    });\r\n\r\n    return docs.map(doc => this.fromFirestoreDocument(doc));\r\n  }\r\n\r\n  // Get the most recent complete brand profile\r\n  async getLatestCompleteBrandProfile(userId: string): Promise<CompleteBrandProfile | null> {\r\n    const q = query(\r\n      collection(db, COLLECTIONS.BRAND_PROFILES),\r\n      where('userId', '==', userId),\r\n      where('isComplete', '==', true),\r\n      orderBy('updatedAt', 'desc'),\r\n      limit(1)\r\n    );\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    if (querySnapshot.empty) {\r\n      return null;\r\n    }\r\n\r\n    const doc = querySnapshot.docs[0];\r\n    const docData = {\r\n      id: doc.id,\r\n      ...doc.data(),\r\n    } as BrandProfileDocument;\r\n\r\n    return this.fromFirestoreDocument(docData);\r\n  }\r\n\r\n  // Get brand profile by ID as app format\r\n  async getBrandProfileById(id: string): Promise<CompleteBrandProfile | null> {\r\n    const doc = await this.getById(id);\r\n    if (!doc) return null;\r\n\r\n    return this.fromFirestoreDocument(doc);\r\n  }\r\n\r\n\r\n}\r\n\r\n// Export singleton instance\r\nexport const brandProfileFirebaseService = new BrandProfileService();\r\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;AACjC;AAAA;AACA;AACA;AACA;;;;;AAGO,MAAM,4BAA4B,kIAAA,CAAA,kBAAe;IACtD,aAAc;QACZ,KAAK,CAAC,gIAAA,CAAA,cAAW,CAAC,cAAc;IAClC;IAEA,sDAAsD;IAC9C,oBACN,OAA4C,EAC5C,MAAc,EACgD;QAC9D,8DAA8D;QAC9D,MAAM,cAAc,CAAC;YACnB,IAAI,QAAQ,QAAQ,QAAQ,aAAa,QAAQ,IAAI,OAAO;YAC5D,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO,IAAI,MAAM,CAAC,CAAA,OAAQ,SAAS,aAAa,SAAS,QAAQ,SAAS;YAClG,IAAI,OAAO,QAAQ,UAAU;gBAC3B,MAAM,UAAe,CAAC;gBACtB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;oBAC9C,MAAM,eAAe,YAAY;oBACjC,IAAI,iBAAiB,WAAW;wBAC9B,OAAO,CAAC,IAAI,GAAG;oBACjB;gBACF;gBACA,OAAO,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,IAAI,UAAU;YACrD;YACA,OAAO;QACT;QAEA,4CAA4C;QAC5C,MAAM,WAAW,CAAC;YAChB,IAAI,CAAC,OAAO,IAAI,IAAI,OAAO,IAAI,OAAO;YACtC,MAAM,UAAU,IAAI,IAAI;YACxB,6DAA6D;YAC7D,IAAI,QAAQ,UAAU,CAAC,cAAc,QAAQ,UAAU,CAAC,aAAa;gBACnE,OAAO;YACT;YACA,0CAA0C;YAC1C,IAAI,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,QAAQ,CAAC,MAAM;gBACnD,OAAO,CAAC,QAAQ,EAAE,SAAS;YAC7B;YACA,OAAO;QACT;QAEA,MAAM,OAAO;YACX;YACA,MAAM,QAAQ,YAAY,IAAI;YAC9B,cAAc,QAAQ,YAAY,IAAI;YACtC,aAAa,QAAQ,mBAAmB,IAAI,QAAQ,WAAW,IAAI;YACnE,UAAU,YAAY,QAAQ,QAAQ;YACtC,SAAS,SAAS,QAAQ,UAAU,IAAI,AAAC,QAAgB,OAAO;YAChE,aAAa,QAAQ,WAAW,IAAI;YACpC,iDAAiD;YACjD,cAAc,QAAQ,YAAY,IAAI;YACtC,aAAa,QAAQ,WAAW,IAAI;YACpC,iBAAiB,QAAQ,eAAe,IAAI;YAC5C,aAAa,YAAY;gBACvB,WAAW,QAAQ,WAAW,EAAE,aAAa;gBAC7C,UAAU,QAAQ,WAAW,EAAE,YAAY;gBAC3C,SAAS,QAAQ,WAAW,EAAE,WAAW;gBACzC,UAAU,QAAQ,WAAW,EAAE,YAAY;gBAC3C,QAAQ,QAAQ,WAAW,EAAE,UAAU;YACzC;YACA,aAAa,MAAM,OAAO,CAAC,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,KAAK,EAAE;YACzF,YAAY,MAAM,OAAO,CAAC,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,CAAC,CAAA,IAAK,KAAK,EAAE;YACtF,aAAa,QAAQ,WAAW,IAAI;YACpC,gBAAgB,QAAQ,cAAc,IAAI;YAC1C,YAAY,QAAQ,UAAU,IAAI;YAElC,qCAAqC;YACrC,cAAc;gBACZ,YAAY,QAAQ,UAAU,IAAI;gBAClC,cAAc,IAAI,OAAO,WAAW;gBACpC,aAAa,QAAQ,WAAW,IAAI;gBACpC,aAAa,QAAQ,WAAW,IAAI;gBACpC,eAAe,QAAQ,aAAa,IAAI;gBACxC,iBAAiB,CAAC,CAAC,CAAC,QAAQ,WAAW,IAAI,QAAQ,WAAW,IAAI,QAAQ,aAAa;YACzF;YAEA,UAAU,MAAM,OAAO,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;oBAC3E,MAAM,SAAS,QAAQ;oBACvB,aAAa,SAAS,eAAe;oBACrC,UAAU,SAAS,YAAY;gBACjC,CAAC,GAAG,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,EAAE;YAC5B,gBAAgB,MAAM,OAAO,CAAC,QAAQ,cAAc,IAAI,QAAQ,cAAc,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;oBAC7F,KAAK,SAAS,SAAS,QAAQ;oBAC/B,aAAa,SAAS,eAAe;oBACrC,MAAM,AAAC,SAAS,QAA2D;gBAC7E,CAAC,GAAG,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,IAAI,EAAE;YAC3B,YAAY,cAAc,WAAW,MAAM,OAAO,CAAC,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,CAAC,MAAM,GAAG;YAClG,SAAS;QACX;QAEA,wCAAwC;QACxC,OAAO,YAAY;IACrB;IAEA,sDAAsD;IAC9C,sBAAsB,GAAyB,EAAwB;QAC7E,OAAO;YACL,oBAAoB;YACpB,cAAc,IAAI,IAAI;YACtB,cAAc,IAAI,YAAY,IAAI;YAClC,UAAU,IAAI,QAAQ,IAAI;YAC1B,aAAa,IAAI,WAAW,IAAI;YAEhC,mBAAmB;YACnB,YAAY,IAAI,OAAO,IAAI;YAC3B,aAAa,IAAI,WAAW,IAAI;YAEhC,6BAA6B;YAC7B,UAAU,IAAI,QAAQ,IAAI,EAAE;YAC5B,gBAAgB,IAAI,cAAc,IAAI;YACtC,aAAa;YACb,uBAAuB;YAEvB,sBAAsB;YACtB,cAAc;YACd,cAAc;YACd,gBAAgB;YAEhB,yBAAyB;YACzB,aAAa,IAAI,WAAW,IAAI;YAChC,aAAa;YACb,eAAe;YAEf,eAAe;YACf,cAAc,IAAI,YAAY,IAAI;YAClC,aAAa,IAAI,WAAW,IAAI;YAChC,iBAAiB,IAAI,eAAe,IAAI;YAExC,eAAe;YACf,aAAa,IAAI,WAAW,EAAE,YAAY;YAC1C,cAAc,IAAI,WAAW,EAAE,aAAa;YAC5C,YAAY,IAAI,WAAW,EAAE,WAAW;YACxC,aAAa,IAAI,WAAW,EAAE,YAAY;YAE1C,kBAAkB;YAClB,gBAAgB,IAAI,cAAc,IAAI,EAAE;YAExC,kBAAkB;YAClB,IAAI,IAAI,EAAE;YACV,WAAW,IAAI,SAAS,YAAY,OAAO,IAAI,SAAS,CAAC,WAAW,KAAK,IAAI,OAAO,WAAW;YAC/F,WAAW,IAAI,SAAS,YAAY,OAAO,IAAI,SAAS,CAAC,WAAW,KAAK,IAAI,OAAO,WAAW;YAC/F,SAAS,IAAI,OAAO,IAAI;QAC1B;IACF;IAEA,qBAAqB;IACrB,MAAM,iBACJ,OAA4C,EAC5C,MAAc,EACG;QACjB,MAAM,gBAAgB,IAAI,CAAC,mBAAmB,CAAC,SAAS;QAExD,gBAAgB;QAChB,MAAM,gBAAgB,gIAAA,CAAA,6BAA0B,CAAC,IAAI,CAAC;YACpD,IAAI;YACJ,WAAW;YACX,WAAW;QACb,GAAG,KAAK,CAAC;QAET,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC;IAC3B;IAEA,uBAAuB;IACvB,MAAM,mBACJ,EAAU,EACV,OAAqD,EACtC;QACf,MAAM,aAA4C,CAAC;QAEnD,IAAI,QAAQ,YAAY,EAAE,WAAW,IAAI,GAAG,QAAQ,YAAY;QAChE,IAAI,QAAQ,YAAY,EAAE,WAAW,YAAY,GAAG,QAAQ,YAAY;QACxE,IAAI,QAAQ,mBAAmB,EAAE,WAAW,WAAW,GAAG,QAAQ,mBAAmB;QACrF,IAAI,QAAQ,QAAQ,EAAE,WAAW,QAAQ,GAAG,QAAQ,QAAQ;QAC5D,IAAI,QAAQ,OAAO,EAAE,WAAW,OAAO,GAAG,QAAQ,OAAO;QACzD,IAAI,QAAQ,WAAW,EAAE,WAAW,WAAW,GAAG,QAAQ,WAAW;QACrE,IAAI,QAAQ,WAAW,EAAE,WAAW,WAAW,GAAG,QAAQ,WAAW;QACrE,IAAI,QAAQ,UAAU,EAAE,WAAW,UAAU,GAAG,QAAQ,UAAU;QAClE,IAAI,QAAQ,WAAW,EAAE,WAAW,WAAW,GAAG,QAAQ,WAAW;QACrE,IAAI,QAAQ,cAAc,EAAE,WAAW,cAAc,GAAG,QAAQ,cAAc;QAC9E,IAAI,QAAQ,UAAU,EAAE,WAAW,UAAU,GAAG,QAAQ,UAAU;QAClE,IAAI,QAAQ,QAAQ,EAAE;YACpB,WAAW,QAAQ,GAAG,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;oBACrD,MAAM,QAAQ,IAAI;oBAClB,aAAa,QAAQ,WAAW;oBAChC,UAAU,QAAQ,QAAQ;gBAC5B,CAAC;YACD,WAAW,UAAU,GAAG;QAC1B;QACA,IAAI,QAAQ,cAAc,EAAE;YAC1B,WAAW,cAAc,GAAG,QAAQ,cAAc,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;oBACjE,KAAK,QAAQ,GAAG;oBAChB,aAAa,QAAQ,WAAW;oBAChC,MAAM,QAAQ,IAAI;gBACpB,CAAC;QACH;QAEA,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI;IACxB;IAEA,0CAA0C;IAC1C,MAAM,qBAAqB,MAAc,EAAmC;QAC1E,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ;YAC1C,SAAS;YACT,gBAAgB;QAClB;QAEA,OAAO,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,CAAC,qBAAqB,CAAC;IACpD;IAEA,6CAA6C;IAC7C,MAAM,8BAA8B,MAAc,EAAwC;QACxF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,gIAAA,CAAA,cAAW,CAAC,cAAc,GACzC,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,SACtB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,cAAc,MAAM,OAC1B,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,SACrB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE;QAGR,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,IAAI,cAAc,KAAK,EAAE;YACvB,OAAO;QACT;QAEA,MAAM,MAAM,cAAc,IAAI,CAAC,EAAE;QACjC,MAAM,UAAU;YACd,IAAI,IAAI,EAAE;YACV,GAAG,IAAI,IAAI,EAAE;QACf;QAEA,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC;IAEA,wCAAwC;IACxC,MAAM,oBAAoB,EAAU,EAAwC;QAC1E,MAAM,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,KAAK,OAAO;QAEjB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC;AAGF;AAGO,MAAM,8BAA8B,IAAI", "debugId": null}}, {"offset": {"line": 6128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/firebase/services/generated-post-service.ts"], "sourcesContent": ["// Generated Posts Firebase service\r\nimport { query, where, orderBy, limit, getDocs, collection } from 'firebase/firestore';\r\nimport { db } from '../config';\r\nimport { DatabaseService } from '../database';\r\nimport { COLLECTIONS, GeneratedPostDocument, GeneratedPostDocumentSchema } from '../schema';\r\nimport type { GeneratedPost, Platform } from '@/lib/types';\r\n\r\nexport class GeneratedPostService extends DatabaseService<GeneratedPostDocument> {\r\n  constructor() {\r\n    super(COLLECTIONS.GENERATED_POSTS);\r\n  }\r\n\r\n  // Convert from app GeneratedPost to Firestore document\r\n  private toFirestoreDocument(\r\n    post: GeneratedPost,\r\n    userId: string,\r\n    brandProfileId: string\r\n  ): Omit<GeneratedPostDocument, 'id' | 'createdAt' | 'updatedAt'> {\r\n    // Helper function to clean undefined values and flatten nested objects\r\n    const cleanValue = (value: any, defaultValue: any = '') => {\r\n      return value !== undefined && value !== null ? value : defaultValue;\r\n    };\r\n\r\n    // Helper function to handle base64 and long strings for Firestore\r\n    const sanitizeForFirestore = (value: any): string => {\r\n      if (value === null || value === undefined) return '';\r\n      if (typeof value === 'string') {\r\n        // Handle base64 data URLs - they're too long for Firestore\r\n        if (value.startsWith('data:image/') && value.includes('base64,')) {\r\n          // Extract just the format info, not the actual base64 data\r\n          const formatMatch = value.match(/data:(image\\/[^;]+)/);\r\n          return formatMatch ? `[Base64 ${formatMatch[1]} image]` : '[Base64 image]';\r\n        }\r\n        // Limit string length to prevent Firestore issues\r\n        return value.length > 1000 ? value.substring(0, 1000) + '...[truncated]' : value;\r\n      }\r\n      if (typeof value === 'number' || typeof value === 'boolean') return String(value);\r\n      // Convert everything else to JSON string to ensure no nested entities\r\n      return JSON.stringify(value);\r\n    };\r\n\r\n    // Helper function to ensure valid platform enum value\r\n    const validatePlatform = (platform: string): GeneratedPostDocument['platform'] => {\r\n      const validPlatforms = ['instagram', 'facebook', 'twitter', 'linkedin', 'tiktok'] as const;\r\n      const normalizedPlatform = platform?.toLowerCase();\r\n      return validPlatforms.includes(normalizedPlatform as any) ? normalizedPlatform as GeneratedPostDocument['platform'] : 'instagram';\r\n    };\r\n\r\n    // Helper function to ensure valid postType enum value\r\n    const validatePostType = (postType: string): GeneratedPostDocument['postType'] => {\r\n      const validPostTypes = ['post', 'story', 'reel', 'advertisement'] as const;\r\n      return validPostTypes.includes(postType as any) ? postType as GeneratedPostDocument['postType'] : 'post';\r\n    };\r\n\r\n    // Helper function to clamp analytics values between 0-100\r\n    const clampAnalyticsValue = (value: any, defaultValue: number): number => {\r\n      const numValue = typeof value === 'number' ? value : defaultValue;\r\n      return Math.min(100, Math.max(0, numValue));\r\n    };\r\n\r\n    // Handle hashtags - convert string to array if needed\r\n    const hashtags = Array.isArray(post.hashtags)\r\n      ? post.hashtags.filter(tag => tag && typeof tag === 'string')\r\n      : typeof post.hashtags === 'string'\r\n        ? post.hashtags.split(' ').filter(tag => tag.startsWith('#'))\r\n        : [];\r\n\r\n    // Helper function to remove undefined values from objects\r\n    const removeUndefined = (obj: any): any => {\r\n      const cleaned: any = {};\r\n      for (const [key, value] of Object.entries(obj)) {\r\n        if (value !== undefined) {\r\n          cleaned[key] = value;\r\n        }\r\n      }\r\n      return cleaned;\r\n    };\r\n\r\n    // Build metadata object and remove undefined values\r\n    const metadata = removeUndefined({\r\n      businessType: post.businessType ? sanitizeForFirestore(post.businessType) : undefined,\r\n      visualStyle: post.visualStyle ? sanitizeForFirestore(post.visualStyle) : undefined,\r\n      targetAudience: post.targetAudience ? sanitizeForFirestore(post.targetAudience) : undefined,\r\n      generationPrompt: post.generationPrompt ? sanitizeForFirestore(post.generationPrompt) : undefined,\r\n      aiModel: post.aiModel ? sanitizeForFirestore(post.aiModel) : undefined,\r\n    });\r\n\r\n    return {\r\n      userId,\r\n      brandProfileId,\r\n      platform: validatePlatform(cleanValue(post.platform, 'instagram')),\r\n      postType: validatePostType(cleanValue(post.postType, 'post')),\r\n      content: removeUndefined({\r\n        text: sanitizeForFirestore(post.content).trim() || 'Generated content',\r\n        hashtags: hashtags.length > 0 ? hashtags.map(tag => String(tag)) : undefined,\r\n        mentions: undefined, // Not in current GeneratedPost type, so omit it\r\n        imageUrl: sanitizeForFirestore(post.imageUrl || post.variants?.[0]?.imageUrl || '') || undefined,\r\n        videoUrl: sanitizeForFirestore(post.videoUrl || '') || undefined,\r\n      }),\r\n      metadata,\r\n      analytics: {\r\n        qualityScore: clampAnalyticsValue(post.qualityScore, 75),\r\n        engagementPrediction: clampAnalyticsValue(post.engagementPrediction, 70),\r\n        brandAlignmentScore: clampAnalyticsValue(post.brandAlignmentScore, 80),\r\n        views: 0,\r\n        likes: 0,\r\n        shares: 0,\r\n        comments: 0,\r\n      },\r\n      status: post.status === 'posted' ? 'published' : 'draft',\r\n    };\r\n  }\r\n\r\n  // Convert from Firestore document to app GeneratedPost\r\n  private fromFirestoreDocument(doc: GeneratedPostDocument): GeneratedPost {\r\n    return {\r\n      id: doc.id,\r\n      platform: doc.platform as Platform,\r\n      postType: doc.postType,\r\n      content: doc.content.text,\r\n      hashtags: doc.content.hashtags || [],\r\n      imageUrl: doc.content.imageUrl,\r\n      videoUrl: doc.content.videoUrl,\r\n      businessType: doc.metadata.businessType,\r\n      visualStyle: doc.metadata.visualStyle,\r\n      targetAudience: doc.metadata.targetAudience,\r\n      generationPrompt: doc.metadata.generationPrompt,\r\n      aiModel: doc.metadata.aiModel,\r\n      qualityScore: doc.analytics?.qualityScore,\r\n      engagementPrediction: doc.analytics?.engagementPrediction,\r\n      brandAlignmentScore: doc.analytics?.brandAlignmentScore,\r\n      date: doc.createdAt instanceof Date ? doc.createdAt.toISOString() : new Date().toISOString(),\r\n      // Legacy fields for backward compatibility\r\n      status: doc.status === 'published' ? 'posted' : 'generated',\r\n      variants: [{\r\n        platform: doc.platform as Platform,\r\n        imageUrl: doc.content.imageUrl || '',\r\n      }],\r\n      catchyWords: '', // Not stored in Firestore, will be populated from new posts\r\n      subheadline: undefined, // Not stored in Firestore, will be populated from new posts\r\n      callToAction: undefined, // Not stored in Firestore, will be populated from new posts\r\n    };\r\n  }\r\n\r\n  // Save generated post\r\n  async saveGeneratedPost(\r\n    post: GeneratedPost,\r\n    userId: string,\r\n    brandProfileId: string\r\n  ): Promise<string> {\r\n    try {\r\n      console.log('🔄 Starting post save process...');\r\n      console.log('👤 User ID:', userId);\r\n      console.log('🏢 Brand Profile ID:', brandProfileId);\r\n      console.log('📝 Original post data:', JSON.stringify(post, null, 2));\r\n\r\n      const firestoreData = this.toFirestoreDocument(post, userId, brandProfileId);\r\n      console.log('🔧 Converted Firestore data:', JSON.stringify(firestoreData, null, 2));\r\n\r\n      // Additional validation to catch nested entities\r\n      const validateNoNestedEntities = (obj: any, path: string = ''): void => {\r\n        if (obj === null || obj === undefined) return;\r\n\r\n        if (typeof obj === 'object' && !Array.isArray(obj) && !(obj instanceof Date)) {\r\n          Object.keys(obj).forEach(key => {\r\n            const value = obj[key];\r\n            const currentPath = path ? `${path}.${key}` : key;\r\n\r\n            if (value !== null && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {\r\n              // Check if this is a simple object with only primitive values\r\n              const hasComplexNesting = Object.values(value).some(v =>\r\n                v !== null && typeof v === 'object' && !Array.isArray(v) && !(v instanceof Date)\r\n              );\r\n\r\n              if (hasComplexNesting) {\r\n                console.error(`❌ Complex nested entity found at ${currentPath}:`, value);\r\n                throw new Error(`Property ${currentPath} contains an invalid nested entity`);\r\n              }\r\n            }\r\n\r\n            validateNoNestedEntities(value, currentPath);\r\n          });\r\n        } else if (Array.isArray(obj)) {\r\n          obj.forEach((item, index) => {\r\n            validateNoNestedEntities(item, `${path}[${index}]`);\r\n          });\r\n        }\r\n      };\r\n\r\n      console.log('🔍 Validating for nested entities...');\r\n      validateNoNestedEntities(firestoreData);\r\n      console.log('✅ No nested entities found');\r\n\r\n      // Validate data with schema\r\n      console.log('📋 Validating with schema...');\r\n      const validatedData = GeneratedPostDocumentSchema.omit({\r\n        id: true,\r\n        createdAt: true,\r\n        updatedAt: true,\r\n      }).parse(firestoreData);\r\n\r\n      console.log('💾 Attempting to save to Firestore...');\r\n      const result = await this.create(validatedData);\r\n      console.log('✅ Successfully saved to Firestore with ID:', result);\r\n\r\n      return result;\r\n    } catch (error) {\r\n      console.error('❌ Failed to save post:', error);\r\n      console.error('📊 Post data that failed:', JSON.stringify(post, null, 2));\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Get user's generated posts as app format\r\n  async getUserGeneratedPosts(\r\n    userId: string,\r\n    options?: {\r\n      limit?: number;\r\n      platform?: Platform;\r\n      brandProfileId?: string;\r\n    }\r\n  ): Promise<GeneratedPost[]> {\r\n    let q = query(\r\n      collection(db, COLLECTIONS.GENERATED_POSTS),\r\n      where('userId', '==', userId),\r\n      orderBy('createdAt', 'desc')\r\n    );\r\n\r\n    if (options?.platform) {\r\n      q = query(q, where('platform', '==', options.platform));\r\n    }\r\n\r\n    if (options?.brandProfileId) {\r\n      q = query(q, where('brandProfileId', '==', options.brandProfileId));\r\n    }\r\n\r\n    if (options?.limit) {\r\n      q = query(q, limit(options.limit));\r\n    }\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    const docs = querySnapshot.docs.map(doc => ({\r\n      id: doc.id,\r\n      ...doc.data(),\r\n    })) as GeneratedPostDocument[];\r\n\r\n    return docs.map(doc => this.fromFirestoreDocument(doc));\r\n  }\r\n\r\n  // Get recent posts for a specific brand profile\r\n  async getRecentPostsForBrand(\r\n    userId: string,\r\n    brandProfileId: string,\r\n    limitCount: number = 10\r\n  ): Promise<GeneratedPost[]> {\r\n    const q = query(\r\n      collection(db, COLLECTIONS.GENERATED_POSTS),\r\n      where('userId', '==', userId),\r\n      where('brandProfileId', '==', brandProfileId),\r\n      orderBy('createdAt', 'desc'),\r\n      limit(limitCount)\r\n    );\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    const docs = querySnapshot.docs.map(doc => ({\r\n      id: doc.id,\r\n      ...doc.data(),\r\n    })) as GeneratedPostDocument[];\r\n\r\n    return docs.map(doc => this.fromFirestoreDocument(doc));\r\n  }\r\n\r\n  // Update post analytics\r\n  async updatePostAnalytics(\r\n    postId: string,\r\n    analytics: {\r\n      views?: number;\r\n      likes?: number;\r\n      shares?: number;\r\n      comments?: number;\r\n      qualityScore?: number;\r\n      engagementPrediction?: number;\r\n      brandAlignmentScore?: number;\r\n    }\r\n  ): Promise<void> {\r\n    const updateData: Partial<GeneratedPostDocument> = {};\r\n\r\n    if (analytics.views !== undefined ||\r\n      analytics.likes !== undefined ||\r\n      analytics.shares !== undefined ||\r\n      analytics.comments !== undefined ||\r\n      analytics.qualityScore !== undefined ||\r\n      analytics.engagementPrediction !== undefined ||\r\n      analytics.brandAlignmentScore !== undefined) {\r\n\r\n      // Get current document to merge analytics\r\n      const currentDoc = await this.getById(postId);\r\n      if (currentDoc) {\r\n        updateData.analytics = {\r\n          ...currentDoc.analytics,\r\n          ...analytics,\r\n        };\r\n      }\r\n    }\r\n\r\n    await this.update(postId, updateData);\r\n  }\r\n\r\n  // Update post status\r\n  async updatePostStatus(\r\n    postId: string,\r\n    status: GeneratedPostDocument['status'],\r\n    scheduledAt?: Date,\r\n    publishedAt?: Date\r\n  ): Promise<void> {\r\n    const updateData: Partial<GeneratedPostDocument> = { status };\r\n\r\n    if (scheduledAt) {\r\n      updateData.scheduledAt = scheduledAt;\r\n    }\r\n\r\n    if (publishedAt) {\r\n      updateData.publishedAt = publishedAt;\r\n    }\r\n\r\n    await this.update(postId, updateData);\r\n  }\r\n\r\n  // Get posts by status\r\n  async getPostsByStatus(\r\n    userId: string,\r\n    status: GeneratedPostDocument['status']\r\n  ): Promise<GeneratedPost[]> {\r\n    const q = query(\r\n      collection(db, COLLECTIONS.GENERATED_POSTS),\r\n      where('userId', '==', userId),\r\n      where('status', '==', status),\r\n      orderBy('createdAt', 'desc')\r\n    );\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    const docs = querySnapshot.docs.map(doc => ({\r\n      id: doc.id,\r\n      ...doc.data(),\r\n    })) as GeneratedPostDocument[];\r\n\r\n    return docs.map(doc => this.fromFirestoreDocument(doc));\r\n  }\r\n\r\n  // Delete old posts (cleanup utility)\r\n  async deleteOldPosts(userId: string, daysOld: number = 30): Promise<number> {\r\n    const cutoffDate = new Date();\r\n    cutoffDate.setDate(cutoffDate.getDate() - daysOld);\r\n\r\n    const q = query(\r\n      collection(db, COLLECTIONS.GENERATED_POSTS),\r\n      where('userId', '==', userId),\r\n      where('createdAt', '<', cutoffDate)\r\n    );\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    const batch = this.createBatch();\r\n\r\n    querySnapshot.docs.forEach(doc => {\r\n      batch.delete(doc.ref);\r\n    });\r\n\r\n    await this.executeBatch(batch);\r\n    return querySnapshot.docs.length;\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const generatedPostFirebaseService = new GeneratedPostService();\r\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;;AACnC;AAAA;AACA;AACA;AACA;;;;;AAGO,MAAM,6BAA6B,kIAAA,CAAA,kBAAe;IACvD,aAAc;QACZ,KAAK,CAAC,gIAAA,CAAA,cAAW,CAAC,eAAe;IACnC;IAEA,uDAAuD;IAC/C,oBACN,IAAmB,EACnB,MAAc,EACd,cAAsB,EACyC;QAC/D,uEAAuE;QACvE,MAAM,aAAa,CAAC,OAAY,eAAoB,EAAE;YACpD,OAAO,UAAU,aAAa,UAAU,OAAO,QAAQ;QACzD;QAEA,kEAAkE;QAClE,MAAM,uBAAuB,CAAC;YAC5B,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;YAClD,IAAI,OAAO,UAAU,UAAU;gBAC7B,2DAA2D;gBAC3D,IAAI,MAAM,UAAU,CAAC,kBAAkB,MAAM,QAAQ,CAAC,YAAY;oBAChE,2DAA2D;oBAC3D,MAAM,cAAc,MAAM,KAAK,CAAC;oBAChC,OAAO,cAAc,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG;gBAC5D;gBACA,kDAAkD;gBAClD,OAAO,MAAM,MAAM,GAAG,OAAO,MAAM,SAAS,CAAC,GAAG,QAAQ,mBAAmB;YAC7E;YACA,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW,OAAO,OAAO;YAC3E,sEAAsE;YACtE,OAAO,KAAK,SAAS,CAAC;QACxB;QAEA,sDAAsD;QACtD,MAAM,mBAAmB,CAAC;YACxB,MAAM,iBAAiB;gBAAC;gBAAa;gBAAY;gBAAW;gBAAY;aAAS;YACjF,MAAM,qBAAqB,UAAU;YACrC,OAAO,eAAe,QAAQ,CAAC,sBAA6B,qBAA0D;QACxH;QAEA,sDAAsD;QACtD,MAAM,mBAAmB,CAAC;YACxB,MAAM,iBAAiB;gBAAC;gBAAQ;gBAAS;gBAAQ;aAAgB;YACjE,OAAO,eAAe,QAAQ,CAAC,YAAmB,WAAgD;QACpG;QAEA,0DAA0D;QAC1D,MAAM,sBAAsB,CAAC,OAAY;YACvC,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ;YACrD,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;QACnC;QAEA,sDAAsD;QACtD,MAAM,WAAW,MAAM,OAAO,CAAC,KAAK,QAAQ,IACxC,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,MAAO,OAAO,OAAO,QAAQ,YAClD,OAAO,KAAK,QAAQ,KAAK,WACvB,KAAK,QAAQ,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,QACtD,EAAE;QAER,0DAA0D;QAC1D,MAAM,kBAAkB,CAAC;YACvB,MAAM,UAAe,CAAC;YACtB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;gBAC9C,IAAI,UAAU,WAAW;oBACvB,OAAO,CAAC,IAAI,GAAG;gBACjB;YACF;YACA,OAAO;QACT;QAEA,oDAAoD;QACpD,MAAM,WAAW,gBAAgB;YAC/B,cAAc,KAAK,YAAY,GAAG,qBAAqB,KAAK,YAAY,IAAI;YAC5E,aAAa,KAAK,WAAW,GAAG,qBAAqB,KAAK,WAAW,IAAI;YACzE,gBAAgB,KAAK,cAAc,GAAG,qBAAqB,KAAK,cAAc,IAAI;YAClF,kBAAkB,KAAK,gBAAgB,GAAG,qBAAqB,KAAK,gBAAgB,IAAI;YACxF,SAAS,KAAK,OAAO,GAAG,qBAAqB,KAAK,OAAO,IAAI;QAC/D;QAEA,OAAO;YACL;YACA;YACA,UAAU,iBAAiB,WAAW,KAAK,QAAQ,EAAE;YACrD,UAAU,iBAAiB,WAAW,KAAK,QAAQ,EAAE;YACrD,SAAS,gBAAgB;gBACvB,MAAM,qBAAqB,KAAK,OAAO,EAAE,IAAI,MAAM;gBACnD,UAAU,SAAS,MAAM,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,MAAO,OAAO,QAAQ;gBACnE,UAAU;gBACV,UAAU,qBAAqB,KAAK,QAAQ,IAAI,KAAK,QAAQ,EAAE,CAAC,EAAE,EAAE,YAAY,OAAO;gBACvF,UAAU,qBAAqB,KAAK,QAAQ,IAAI,OAAO;YACzD;YACA;YACA,WAAW;gBACT,cAAc,oBAAoB,KAAK,YAAY,EAAE;gBACrD,sBAAsB,oBAAoB,KAAK,oBAAoB,EAAE;gBACrE,qBAAqB,oBAAoB,KAAK,mBAAmB,EAAE;gBACnE,OAAO;gBACP,OAAO;gBACP,QAAQ;gBACR,UAAU;YACZ;YACA,QAAQ,KAAK,MAAM,KAAK,WAAW,cAAc;QACnD;IACF;IAEA,uDAAuD;IAC/C,sBAAsB,GAA0B,EAAiB;QACvE,OAAO;YACL,IAAI,IAAI,EAAE;YACV,UAAU,IAAI,QAAQ;YACtB,UAAU,IAAI,QAAQ;YACtB,SAAS,IAAI,OAAO,CAAC,IAAI;YACzB,UAAU,IAAI,OAAO,CAAC,QAAQ,IAAI,EAAE;YACpC,UAAU,IAAI,OAAO,CAAC,QAAQ;YAC9B,UAAU,IAAI,OAAO,CAAC,QAAQ;YAC9B,cAAc,IAAI,QAAQ,CAAC,YAAY;YACvC,aAAa,IAAI,QAAQ,CAAC,WAAW;YACrC,gBAAgB,IAAI,QAAQ,CAAC,cAAc;YAC3C,kBAAkB,IAAI,QAAQ,CAAC,gBAAgB;YAC/C,SAAS,IAAI,QAAQ,CAAC,OAAO;YAC7B,cAAc,IAAI,SAAS,EAAE;YAC7B,sBAAsB,IAAI,SAAS,EAAE;YACrC,qBAAqB,IAAI,SAAS,EAAE;YACpC,MAAM,IAAI,SAAS,YAAY,OAAO,IAAI,SAAS,CAAC,WAAW,KAAK,IAAI,OAAO,WAAW;YAC1F,2CAA2C;YAC3C,QAAQ,IAAI,MAAM,KAAK,cAAc,WAAW;YAChD,UAAU;gBAAC;oBACT,UAAU,IAAI,QAAQ;oBACtB,UAAU,IAAI,OAAO,CAAC,QAAQ,IAAI;gBACpC;aAAE;YACF,aAAa;YACb,aAAa;YACb,cAAc;QAChB;IACF;IAEA,sBAAsB;IACtB,MAAM,kBACJ,IAAmB,EACnB,MAAc,EACd,cAAsB,EACL;QACjB,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,eAAe;YAC3B,QAAQ,GAAG,CAAC,wBAAwB;YACpC,QAAQ,GAAG,CAAC,0BAA0B,KAAK,SAAS,CAAC,MAAM,MAAM;YAEjE,MAAM,gBAAgB,IAAI,CAAC,mBAAmB,CAAC,MAAM,QAAQ;YAC7D,QAAQ,GAAG,CAAC,gCAAgC,KAAK,SAAS,CAAC,eAAe,MAAM;YAEhF,iDAAiD;YACjD,MAAM,2BAA2B,CAAC,KAAU,OAAe,EAAE;gBAC3D,IAAI,QAAQ,QAAQ,QAAQ,WAAW;gBAEvC,IAAI,OAAO,QAAQ,YAAY,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,eAAe,IAAI,GAAG;oBAC5E,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,CAAA;wBACvB,MAAM,QAAQ,GAAG,CAAC,IAAI;wBACtB,MAAM,cAAc,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG;wBAE9C,IAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,CAAC,MAAM,OAAO,CAAC,UAAU,CAAC,CAAC,iBAAiB,IAAI,GAAG;4BACpG,8DAA8D;4BAC9D,MAAM,oBAAoB,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,CAAA,IAClD,MAAM,QAAQ,OAAO,MAAM,YAAY,CAAC,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,aAAa,IAAI;4BAGjF,IAAI,mBAAmB;gCACrB,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,YAAY,CAAC,CAAC,EAAE;gCAClE,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,YAAY,kCAAkC,CAAC;4BAC7E;wBACF;wBAEA,yBAAyB,OAAO;oBAClC;gBACF,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM;oBAC7B,IAAI,OAAO,CAAC,CAAC,MAAM;wBACjB,yBAAyB,MAAM,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC;oBACpD;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,yBAAyB;YACzB,QAAQ,GAAG,CAAC;YAEZ,4BAA4B;YAC5B,QAAQ,GAAG,CAAC;YACZ,MAAM,gBAAgB,gIAAA,CAAA,8BAA2B,CAAC,IAAI,CAAC;gBACrD,IAAI;gBACJ,WAAW;gBACX,WAAW;YACb,GAAG,KAAK,CAAC;YAET,QAAQ,GAAG,CAAC;YACZ,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YACjC,QAAQ,GAAG,CAAC,8CAA8C;YAE1D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,QAAQ,KAAK,CAAC,6BAA6B,KAAK,SAAS,CAAC,MAAM,MAAM;YACtE,MAAM;QACR;IACF;IAEA,2CAA2C;IAC3C,MAAM,sBACJ,MAAc,EACd,OAIC,EACyB;QAC1B,IAAI,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACV,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,gIAAA,CAAA,cAAW,CAAC,eAAe,GAC1C,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,SACtB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAGvB,IAAI,SAAS,UAAU;YACrB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QAEA,IAAI,SAAS,gBAAgB;YAC3B,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,kBAAkB,MAAM,QAAQ,cAAc;QACnE;QAEA,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,MAAM,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC1C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QAED,OAAO,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,CAAC,qBAAqB,CAAC;IACpD;IAEA,gDAAgD;IAChD,MAAM,uBACJ,MAAc,EACd,cAAsB,EACtB,aAAqB,EAAE,EACG;QAC1B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,gIAAA,CAAA,cAAW,CAAC,eAAe,GAC1C,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,SACtB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,kBAAkB,MAAM,iBAC9B,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,SACrB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE;QAGR,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,MAAM,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC1C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QAED,OAAO,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,CAAC,qBAAqB,CAAC;IACpD;IAEA,wBAAwB;IACxB,MAAM,oBACJ,MAAc,EACd,SAQC,EACc;QACf,MAAM,aAA6C,CAAC;QAEpD,IAAI,UAAU,KAAK,KAAK,aACtB,UAAU,KAAK,KAAK,aACpB,UAAU,MAAM,KAAK,aACrB,UAAU,QAAQ,KAAK,aACvB,UAAU,YAAY,KAAK,aAC3B,UAAU,oBAAoB,KAAK,aACnC,UAAU,mBAAmB,KAAK,WAAW;YAE7C,0CAA0C;YAC1C,MAAM,aAAa,MAAM,IAAI,CAAC,OAAO,CAAC;YACtC,IAAI,YAAY;gBACd,WAAW,SAAS,GAAG;oBACrB,GAAG,WAAW,SAAS;oBACvB,GAAG,SAAS;gBACd;YACF;QACF;QAEA,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ;IAC5B;IAEA,qBAAqB;IACrB,MAAM,iBACJ,MAAc,EACd,MAAuC,EACvC,WAAkB,EAClB,WAAkB,EACH;QACf,MAAM,aAA6C;YAAE;QAAO;QAE5D,IAAI,aAAa;YACf,WAAW,WAAW,GAAG;QAC3B;QAEA,IAAI,aAAa;YACf,WAAW,WAAW,GAAG;QAC3B;QAEA,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ;IAC5B;IAEA,sBAAsB;IACtB,MAAM,iBACJ,MAAc,EACd,MAAuC,EACb;QAC1B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,gIAAA,CAAA,cAAW,CAAC,eAAe,GAC1C,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,SACtB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,SACtB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAGvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,MAAM,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC1C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QAED,OAAO,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,CAAC,qBAAqB,CAAC;IACpD;IAEA,qCAAqC;IACrC,MAAM,eAAe,MAAc,EAAE,UAAkB,EAAE,EAAmB;QAC1E,MAAM,aAAa,IAAI;QACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;QAE1C,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,gIAAA,CAAA,cAAW,CAAC,eAAe,GAC1C,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,SACtB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,aAAa,KAAK;QAG1B,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,MAAM,QAAQ,IAAI,CAAC,WAAW;QAE9B,cAAc,IAAI,CAAC,OAAO,CAAC,CAAA;YACzB,MAAM,MAAM,CAAC,IAAI,GAAG;QACtB;QAEA,MAAM,IAAI,CAAC,YAAY,CAAC;QACxB,OAAO,cAAc,IAAI,CAAC,MAAM;IAClC;AACF;AAGO,MAAM,+BAA+B,IAAI", "debugId": null}}, {"offset": {"line": 6413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/firebase/migration.ts"], "sourcesContent": ["// Migration utilities for moving localStorage data to Firestore\r\nimport { brandProfileFirebaseService } from './services/brand-profile-service';\r\nimport { generatedPostFirebaseService } from './services/generated-post-service';\r\nimport { artifactFirebaseService } from './services/artifact-service';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\nimport type { GeneratedPost } from '@/lib/types';\r\n\r\n// Migration status tracking\r\nexport interface MigrationStatus {\r\n  brandProfiles: { migrated: number; failed: number };\r\n  generatedPosts: { migrated: number; failed: number };\r\n  artifacts: { migrated: number; failed: number };\r\n  errors: string[];\r\n}\r\n\r\n// Migrate brand profiles from localStorage to Firestore\r\nexport async function migrateBrandProfiles(userId: string): Promise<{\r\n  migrated: number;\r\n  failed: number;\r\n  errors: string[];\r\n}> {\r\n  const errors: string[] = [];\r\n  let migrated = 0;\r\n  let failed = 0;\r\n\r\n  try {\r\n    // Get current brand profile\r\n    const currentProfileData = localStorage.getItem('completeBrandProfile');\r\n    if (currentProfileData) {\r\n      try {\r\n        const currentProfile: CompleteBrandProfile = JSON.parse(currentProfileData);\r\n        console.log('Migrating brand profile:', currentProfile.businessName || 'Unnamed Profile');\r\n\r\n        // Clean and validate profile data before migration\r\n        const cleanedProfile = {\r\n          ...currentProfile,\r\n          businessName: currentProfile.businessName || 'Untitled Business',\r\n          businessType: currentProfile.businessType || 'General',\r\n          businessDescription: currentProfile.businessDescription || currentProfile.description || '',\r\n          location: currentProfile.location || '',\r\n          website: currentProfile.website || '',\r\n          socialMedia: currentProfile.socialMedia || {},\r\n          brandColors: Array.isArray(currentProfile.brandColors) ? currentProfile.brandColors : [],\r\n          brandFonts: Array.isArray(currentProfile.brandFonts) ? currentProfile.brandFonts : [],\r\n          visualStyle: currentProfile.visualStyle || '',\r\n          targetAudience: currentProfile.targetAudience || '',\r\n          brandVoice: currentProfile.brandVoice || '',\r\n          services: Array.isArray(currentProfile.services) ? currentProfile.services : [],\r\n          designExamples: Array.isArray(currentProfile.designExamples) ? currentProfile.designExamples : [],\r\n        };\r\n\r\n        await brandProfileFirebaseService.saveBrandProfile(cleanedProfile, userId);\r\n        migrated++;\r\n        console.log('✅ Migrated current brand profile successfully');\r\n      } catch (error) {\r\n        failed++;\r\n        const errorMessage = error instanceof Error ? error.message : String(error);\r\n        console.error('❌ Failed to migrate brand profile:', errorMessage);\r\n        errors.push(`Failed to migrate current brand profile: ${errorMessage}`);\r\n      }\r\n    }\r\n\r\n    // Get saved brand profiles list\r\n    const savedProfilesData = localStorage.getItem('completeBrandProfiles');\r\n    if (savedProfilesData) {\r\n      try {\r\n        const savedProfiles: CompleteBrandProfile[] = JSON.parse(savedProfilesData);\r\n\r\n        for (const profile of savedProfiles) {\r\n          try {\r\n            // Check if this profile was already migrated (avoid duplicates)\r\n            const existing = await brandProfileFirebaseService.getUserBrandProfiles(userId);\r\n            const isDuplicate = existing.some(existingProfile =>\r\n              existingProfile.businessName === profile.businessName &&\r\n              existingProfile.businessType === profile.businessType\r\n            );\r\n\r\n            if (!isDuplicate) {\r\n              await brandProfileFirebaseService.saveBrandProfile(profile, userId);\r\n              migrated++;\r\n            }\r\n          } catch (error) {\r\n            failed++;\r\n            errors.push(`Failed to migrate saved profile \"${profile.businessName}\": ${error}`);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        errors.push(`Failed to parse saved profiles: ${error}`);\r\n      }\r\n    }\r\n\r\n    // Also check for legacy brand profile format\r\n    const legacyProfileData = localStorage.getItem('brandProfile');\r\n    if (legacyProfileData) {\r\n      try {\r\n        const legacyProfile = JSON.parse(legacyProfileData);\r\n\r\n        // Convert legacy format to CompleteBrandProfile\r\n        const convertedProfile: CompleteBrandProfile = {\r\n          businessName: legacyProfile.businessName || '',\r\n          businessType: legacyProfile.businessType || '',\r\n          location: legacyProfile.location || '',\r\n          description: legacyProfile.description || '',\r\n          services: legacyProfile.services || [],\r\n          targetAudience: legacyProfile.targetAudience || '',\r\n          keyFeatures: legacyProfile.keyFeatures || '',\r\n          competitiveAdvantages: legacyProfile.competitiveAdvantages || '',\r\n          contactPhone: legacyProfile.contactPhone || '',\r\n          contactEmail: legacyProfile.contactEmail || '',\r\n          contactAddress: legacyProfile.contactAddress || '',\r\n          visualStyle: legacyProfile.visualStyle || '',\r\n          writingTone: legacyProfile.writingTone || '',\r\n          contentThemes: legacyProfile.contentThemes || '',\r\n          primaryColor: legacyProfile.primaryColor || '',\r\n          accentColor: legacyProfile.accentColor || '',\r\n          backgroundColor: legacyProfile.backgroundColor || '',\r\n          facebookUrl: legacyProfile.socialMedia?.facebook || '',\r\n          instagramUrl: legacyProfile.socialMedia?.instagram || '',\r\n          twitterUrl: legacyProfile.socialMedia?.twitter || '',\r\n          linkedinUrl: legacyProfile.socialMedia?.linkedin || '',\r\n          websiteUrl: legacyProfile.websiteUrl || '',\r\n          logoDataUrl: legacyProfile.logoDataUrl || '',\r\n          designExamples: legacyProfile.designExamples || [],\r\n        };\r\n\r\n        // Check for duplicates\r\n        const existing = await brandProfileFirebaseService.getUserBrandProfiles(userId);\r\n        const isDuplicate = existing.some(existingProfile =>\r\n          existingProfile.businessName === convertedProfile.businessName &&\r\n          existingProfile.businessType === convertedProfile.businessType\r\n        );\r\n\r\n        if (!isDuplicate) {\r\n          await brandProfileFirebaseService.saveBrandProfile(convertedProfile, userId);\r\n          migrated++;\r\n          console.log('Migrated legacy brand profile');\r\n        }\r\n      } catch (error) {\r\n        failed++;\r\n        errors.push(`Failed to migrate legacy brand profile: ${error}`);\r\n      }\r\n    }\r\n\r\n  } catch (error) {\r\n    errors.push(`Migration error: ${error}`);\r\n  }\r\n\r\n  return { migrated, failed, errors };\r\n}\r\n\r\n// Migrate generated posts from localStorage to Firestore\r\nexport async function migrateGeneratedPosts(userId: string, brandProfileId: string): Promise<{\r\n  migrated: number;\r\n  failed: number;\r\n  errors: string[];\r\n}> {\r\n  const errors: string[] = [];\r\n  let migrated = 0;\r\n  let failed = 0;\r\n\r\n  try {\r\n    const generatedPostsData = localStorage.getItem('generatedPosts');\r\n    if (generatedPostsData) {\r\n      try {\r\n        const posts: GeneratedPost[] = JSON.parse(generatedPostsData);\r\n\r\n        for (const post of posts) {\r\n          try {\r\n            console.log('Migrating post:', post.id);\r\n            console.log('Original post data structure:', JSON.stringify(post, null, 2));\r\n\r\n            // Deep clean function to handle all nested objects\r\n            const deepClean = (obj: any): any => {\r\n              if (obj === null || obj === undefined) return null;\r\n              if (typeof obj === 'string' || typeof obj === 'number' || typeof obj === 'boolean') return obj;\r\n              if (obj instanceof Date) return obj;\r\n              if (Array.isArray(obj)) {\r\n                return obj.map(item => {\r\n                  if (typeof item === 'object' && item !== null) {\r\n                    return JSON.stringify(item);\r\n                  }\r\n                  return String(item);\r\n                });\r\n              }\r\n              if (typeof obj === 'object') {\r\n                // For objects, convert to string to avoid nested entity issues\r\n                return JSON.stringify(obj);\r\n              }\r\n              return String(obj);\r\n            };\r\n\r\n            // Clean and validate post data before migration\r\n            const cleanedPost = {\r\n              id: post.id || `post_${Date.now()}`,\r\n              content: deepClean(post.content) || 'Generated content',\r\n              platform: String(post.platform || 'instagram'),\r\n              postType: String(post.postType || 'post'),\r\n              businessType: String(post.businessType || 'general'),\r\n              visualStyle: String(post.visualStyle || ''),\r\n              targetAudience: String(post.targetAudience || ''),\r\n              generationPrompt: String(post.generationPrompt || ''),\r\n              aiModel: String(post.aiModel || 'ai'),\r\n              qualityScore: Math.max(0, Number(post.qualityScore) || 75),\r\n              engagementPrediction: Math.max(0, Number(post.engagementPrediction) || 70),\r\n              brandAlignmentScore: Math.max(0, Number(post.brandAlignmentScore) || 80),\r\n              hashtags: Array.isArray(post.hashtags) ? post.hashtags.map(tag => String(tag)) : [],\r\n              status: String(post.status || 'generated'),\r\n              imageUrl: String(post.imageUrl || ''),\r\n              videoUrl: String(post.videoUrl || ''),\r\n              createdAt: post.createdAt ? new Date(post.createdAt) : new Date(),\r\n            };\r\n\r\n            console.log('Cleaned post data:', JSON.stringify(cleanedPost, null, 2));\r\n\r\n            await generatedPostFirebaseService.saveGeneratedPost(cleanedPost, userId, brandProfileId);\r\n            migrated++;\r\n            console.log('✅ Migrated post successfully:', post.id);\r\n          } catch (error) {\r\n            failed++;\r\n            const errorMessage = error instanceof Error ? error.message : String(error);\r\n            console.error('❌ Failed to migrate post:', post.id, errorMessage);\r\n            console.error('Full error:', error);\r\n            errors.push(`Failed to migrate post \"${post.id}\": ${errorMessage}`);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        errors.push(`Failed to parse generated posts: ${error}`);\r\n      }\r\n    }\r\n  } catch (error) {\r\n    errors.push(`Migration error: ${error}`);\r\n  }\r\n\r\n  return { migrated, failed, errors };\r\n}\r\n\r\n// Complete migration process\r\nexport async function migrateAllData(userId: string): Promise<MigrationStatus> {\r\n  const status: MigrationStatus = {\r\n    brandProfiles: { migrated: 0, failed: 0 },\r\n    generatedPosts: { migrated: 0, failed: 0 },\r\n    artifacts: { migrated: 0, failed: 0 },\r\n    errors: [],\r\n  };\r\n\r\n  console.log('Starting data migration to Firestore...');\r\n\r\n  // 1. Migrate brand profiles first\r\n  const brandProfileResult = await migrateBrandProfiles(userId);\r\n  status.brandProfiles = {\r\n    migrated: brandProfileResult.migrated,\r\n    failed: brandProfileResult.failed,\r\n  };\r\n  status.errors.push(...brandProfileResult.errors);\r\n\r\n  // 2. Get the first migrated brand profile ID for posts migration\r\n  let brandProfileId = '';\r\n  try {\r\n    const profiles = await brandProfileFirebaseService.getUserBrandProfiles(userId);\r\n    if (profiles.length > 0) {\r\n      brandProfileId = profiles[0].id;\r\n    }\r\n  } catch (error) {\r\n    status.errors.push(`Failed to get brand profile for posts migration: ${error}`);\r\n  }\r\n\r\n  // 3. Migrate generated posts if we have a brand profile\r\n  if (brandProfileId) {\r\n    const postsResult = await migrateGeneratedPosts(userId, brandProfileId);\r\n    status.generatedPosts = {\r\n      migrated: postsResult.migrated,\r\n      failed: postsResult.failed,\r\n    };\r\n    status.errors.push(...postsResult.errors);\r\n  }\r\n\r\n  // 4. Artifacts migration would go here (currently using in-memory storage)\r\n  // For now, artifacts are handled by the artifacts service directly\r\n\r\n  console.log('Migration completed:', status);\r\n  return status;\r\n}\r\n\r\n// Check if migration is needed\r\nexport function needsMigration(): boolean {\r\n  const hasLocalBrandProfile = localStorage.getItem('completeBrandProfile') ||\r\n    localStorage.getItem('brandProfile');\r\n  const hasLocalPosts = localStorage.getItem('generatedPosts');\r\n\r\n  return !!(hasLocalBrandProfile || hasLocalPosts);\r\n}\r\n\r\n// Clear localStorage after successful migration\r\nexport function clearMigratedData(): void {\r\n  const keysToRemove = [\r\n    'completeBrandProfile',\r\n    'completeBrandProfiles',\r\n    'brandProfile',\r\n    'generatedPosts',\r\n  ];\r\n\r\n  keysToRemove.forEach(key => {\r\n    try {\r\n      localStorage.removeItem(key);\r\n    } catch (error) {\r\n      console.warn(`Failed to remove ${key} from localStorage:`, error);\r\n    }\r\n  });\r\n\r\n  console.log('Cleared migrated data from localStorage');\r\n}\r\n\r\n// Migration hook for React components\r\nexport function useMigration() {\r\n  const [migrationStatus, setMigrationStatus] = React.useState<{\r\n    isNeeded: boolean;\r\n    isRunning: boolean;\r\n    isComplete: boolean;\r\n    status?: MigrationStatus;\r\n  }>({\r\n    isNeeded: false,\r\n    isRunning: false,\r\n    isComplete: false,\r\n  });\r\n\r\n  React.useEffect(() => {\r\n    setMigrationStatus(prev => ({\r\n      ...prev,\r\n      isNeeded: needsMigration(),\r\n    }));\r\n  }, []);\r\n\r\n  const runMigration = async (userId: string) => {\r\n    if (!userId) return;\r\n\r\n    setMigrationStatus(prev => ({ ...prev, isRunning: true }));\r\n\r\n    try {\r\n      const status = await migrateAllData(userId);\r\n\r\n      // Clear localStorage if migration was successful\r\n      if (status.errors.length === 0) {\r\n        clearMigratedData();\r\n      }\r\n\r\n      setMigrationStatus({\r\n        isNeeded: false,\r\n        isRunning: false,\r\n        isComplete: true,\r\n        status,\r\n      });\r\n    } catch (error) {\r\n      console.error('Migration failed:', error);\r\n      setMigrationStatus(prev => ({\r\n        ...prev,\r\n        isRunning: false,\r\n        status: {\r\n          ...prev.status!,\r\n          errors: [...(prev.status?.errors || []), `Migration failed: ${error}`],\r\n        },\r\n      }));\r\n    }\r\n  };\r\n\r\n  return {\r\n    ...migrationStatus,\r\n    runMigration,\r\n  };\r\n}\r\n\r\n// Add React import for the hook\r\nimport React from 'react';\r\n\r\n// Debug function to inspect post data structure\r\nfunction debugPostData() {\r\n  try {\r\n    const posts = JSON.parse(localStorage.getItem('generatedPosts') || '[]');\r\n    console.log('=== DEBUG: Generated Posts Data Structure ===');\r\n    console.log('Total posts:', posts.length);\r\n\r\n    posts.forEach((post: any, index: number) => {\r\n      console.log(`\\n--- Post ${index + 1} (ID: ${post.id}) ---`);\r\n      console.log('Content type:', typeof post.content);\r\n      console.log('Content value:', post.content);\r\n      console.log('Full post structure:', JSON.stringify(post, null, 2));\r\n\r\n      // Check for nested objects\r\n      Object.keys(post).forEach(key => {\r\n        const value = post[key];\r\n        if (typeof value === 'object' && value !== null && !(value instanceof Date)) {\r\n          console.log(`⚠️  Nested object found in \"${key}\":`, value);\r\n        }\r\n      });\r\n    });\r\n\r\n    console.log('=== END DEBUG ===');\r\n  } catch (error) {\r\n    console.error('Debug failed:', error);\r\n  }\r\n}\r\n\r\n// Make functions available globally for testing\r\ndeclare global {\r\n  interface Window {\r\n    testDatabase: () => Promise<void>;\r\n    testMigration: () => Promise<void>;\r\n    clearLocalStorage: () => void;\r\n    runMigration: () => Promise<void>;\r\n    debugPostData: () => void;\r\n  }\r\n}\r\n\r\n// Test function to save a completely clean post\r\nasync function testCleanPost() {\r\n  try {\r\n    console.log('🧪 Testing clean post save...');\r\n\r\n    const userId = 'test-user-' + Date.now();\r\n    const brandProfileId = 'test-brand-' + Date.now();\r\n\r\n    // Create a completely clean post with no nested objects\r\n    const cleanPost = {\r\n      id: 'test-post-' + Date.now(),\r\n      content: 'This is a simple test post content',\r\n      platform: 'instagram',\r\n      postType: 'post',\r\n      businessType: 'general',\r\n      visualStyle: 'modern',\r\n      targetAudience: 'general',\r\n      generationPrompt: 'test prompt',\r\n      aiModel: 'ai',\r\n      qualityScore: 75,\r\n      engagementPrediction: 70,\r\n      brandAlignmentScore: 80,\r\n      hashtags: ['#test', '#clean'],\r\n      status: 'generated',\r\n      imageUrl: '',\r\n      videoUrl: '',\r\n      createdAt: new Date(),\r\n    };\r\n\r\n    console.log('Clean post data:', cleanPost);\r\n\r\n    const { generatedPostFirebaseService } = await import('./services/generated-post-service');\r\n    await generatedPostFirebaseService.saveGeneratedPost(cleanPost, userId, brandProfileId);\r\n\r\n    console.log('✅ Clean post saved successfully!');\r\n  } catch (error) {\r\n    console.error('❌ Clean post save failed:', error);\r\n  }\r\n}\r\n\r\n// Assign to window for global access\r\nif (typeof window !== 'undefined') {\r\n  window.debugPostData = debugPostData;\r\n  (window as any).testCleanPost = testCleanPost;\r\n}\r\n"], "names": [], "mappings": "AAAA,gEAAgE;;;;;;;;;AAChE;AACA;AAgXA,gCAAgC;AAChC;;;AAnWO,eAAe,qBAAqB,MAAc;IAKvD,MAAM,SAAmB,EAAE;IAC3B,IAAI,WAAW;IACf,IAAI,SAAS;IAEb,IAAI;QACF,4BAA4B;QAC5B,MAAM,qBAAqB,aAAa,OAAO,CAAC;QAChD,IAAI,oBAAoB;YACtB,IAAI;gBACF,MAAM,iBAAuC,KAAK,KAAK,CAAC;gBACxD,QAAQ,GAAG,CAAC,4BAA4B,eAAe,YAAY,IAAI;gBAEvE,mDAAmD;gBACnD,MAAM,iBAAiB;oBACrB,GAAG,cAAc;oBACjB,cAAc,eAAe,YAAY,IAAI;oBAC7C,cAAc,eAAe,YAAY,IAAI;oBAC7C,qBAAqB,eAAe,mBAAmB,IAAI,eAAe,WAAW,IAAI;oBACzF,UAAU,eAAe,QAAQ,IAAI;oBACrC,SAAS,eAAe,OAAO,IAAI;oBACnC,aAAa,eAAe,WAAW,IAAI,CAAC;oBAC5C,aAAa,MAAM,OAAO,CAAC,eAAe,WAAW,IAAI,eAAe,WAAW,GAAG,EAAE;oBACxF,YAAY,MAAM,OAAO,CAAC,eAAe,UAAU,IAAI,eAAe,UAAU,GAAG,EAAE;oBACrF,aAAa,eAAe,WAAW,IAAI;oBAC3C,gBAAgB,eAAe,cAAc,IAAI;oBACjD,YAAY,eAAe,UAAU,IAAI;oBACzC,UAAU,MAAM,OAAO,CAAC,eAAe,QAAQ,IAAI,eAAe,QAAQ,GAAG,EAAE;oBAC/E,gBAAgB,MAAM,OAAO,CAAC,eAAe,cAAc,IAAI,eAAe,cAAc,GAAG,EAAE;gBACnG;gBAEA,MAAM,iKAAA,CAAA,8BAA2B,CAAC,gBAAgB,CAAC,gBAAgB;gBACnE;gBACA,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd;gBACA,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;gBACrE,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,OAAO,IAAI,CAAC,CAAC,yCAAyC,EAAE,cAAc;YACxE;QACF;QAEA,gCAAgC;QAChC,MAAM,oBAAoB,aAAa,OAAO,CAAC;QAC/C,IAAI,mBAAmB;YACrB,IAAI;gBACF,MAAM,gBAAwC,KAAK,KAAK,CAAC;gBAEzD,KAAK,MAAM,WAAW,cAAe;oBACnC,IAAI;wBACF,gEAAgE;wBAChE,MAAM,WAAW,MAAM,iKAAA,CAAA,8BAA2B,CAAC,oBAAoB,CAAC;wBACxE,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,kBAChC,gBAAgB,YAAY,KAAK,QAAQ,YAAY,IACrD,gBAAgB,YAAY,KAAK,QAAQ,YAAY;wBAGvD,IAAI,CAAC,aAAa;4BAChB,MAAM,iKAAA,CAAA,8BAA2B,CAAC,gBAAgB,CAAC,SAAS;4BAC5D;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd;wBACA,OAAO,IAAI,CAAC,CAAC,iCAAiC,EAAE,QAAQ,YAAY,CAAC,GAAG,EAAE,OAAO;oBACnF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,OAAO,IAAI,CAAC,CAAC,gCAAgC,EAAE,OAAO;YACxD;QACF;QAEA,6CAA6C;QAC7C,MAAM,oBAAoB,aAAa,OAAO,CAAC;QAC/C,IAAI,mBAAmB;YACrB,IAAI;gBACF,MAAM,gBAAgB,KAAK,KAAK,CAAC;gBAEjC,gDAAgD;gBAChD,MAAM,mBAAyC;oBAC7C,cAAc,cAAc,YAAY,IAAI;oBAC5C,cAAc,cAAc,YAAY,IAAI;oBAC5C,UAAU,cAAc,QAAQ,IAAI;oBACpC,aAAa,cAAc,WAAW,IAAI;oBAC1C,UAAU,cAAc,QAAQ,IAAI,EAAE;oBACtC,gBAAgB,cAAc,cAAc,IAAI;oBAChD,aAAa,cAAc,WAAW,IAAI;oBAC1C,uBAAuB,cAAc,qBAAqB,IAAI;oBAC9D,cAAc,cAAc,YAAY,IAAI;oBAC5C,cAAc,cAAc,YAAY,IAAI;oBAC5C,gBAAgB,cAAc,cAAc,IAAI;oBAChD,aAAa,cAAc,WAAW,IAAI;oBAC1C,aAAa,cAAc,WAAW,IAAI;oBAC1C,eAAe,cAAc,aAAa,IAAI;oBAC9C,cAAc,cAAc,YAAY,IAAI;oBAC5C,aAAa,cAAc,WAAW,IAAI;oBAC1C,iBAAiB,cAAc,eAAe,IAAI;oBAClD,aAAa,cAAc,WAAW,EAAE,YAAY;oBACpD,cAAc,cAAc,WAAW,EAAE,aAAa;oBACtD,YAAY,cAAc,WAAW,EAAE,WAAW;oBAClD,aAAa,cAAc,WAAW,EAAE,YAAY;oBACpD,YAAY,cAAc,UAAU,IAAI;oBACxC,aAAa,cAAc,WAAW,IAAI;oBAC1C,gBAAgB,cAAc,cAAc,IAAI,EAAE;gBACpD;gBAEA,uBAAuB;gBACvB,MAAM,WAAW,MAAM,iKAAA,CAAA,8BAA2B,CAAC,oBAAoB,CAAC;gBACxE,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,kBAChC,gBAAgB,YAAY,KAAK,iBAAiB,YAAY,IAC9D,gBAAgB,YAAY,KAAK,iBAAiB,YAAY;gBAGhE,IAAI,CAAC,aAAa;oBAChB,MAAM,iKAAA,CAAA,8BAA2B,CAAC,gBAAgB,CAAC,kBAAkB;oBACrE;oBACA,QAAQ,GAAG,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd;gBACA,OAAO,IAAI,CAAC,CAAC,wCAAwC,EAAE,OAAO;YAChE;QACF;IAEF,EAAE,OAAO,OAAO;QACd,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,OAAO;IACzC;IAEA,OAAO;QAAE;QAAU;QAAQ;IAAO;AACpC;AAGO,eAAe,sBAAsB,MAAc,EAAE,cAAsB;IAKhF,MAAM,SAAmB,EAAE;IAC3B,IAAI,WAAW;IACf,IAAI,SAAS;IAEb,IAAI;QACF,MAAM,qBAAqB,aAAa,OAAO,CAAC;QAChD,IAAI,oBAAoB;YACtB,IAAI;gBACF,MAAM,QAAyB,KAAK,KAAK,CAAC;gBAE1C,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI;wBACF,QAAQ,GAAG,CAAC,mBAAmB,KAAK,EAAE;wBACtC,QAAQ,GAAG,CAAC,iCAAiC,KAAK,SAAS,CAAC,MAAM,MAAM;wBAExE,mDAAmD;wBACnD,MAAM,YAAY,CAAC;4BACjB,IAAI,QAAQ,QAAQ,QAAQ,WAAW,OAAO;4BAC9C,IAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO,QAAQ,WAAW,OAAO;4BAC3F,IAAI,eAAe,MAAM,OAAO;4BAChC,IAAI,MAAM,OAAO,CAAC,MAAM;gCACtB,OAAO,IAAI,GAAG,CAAC,CAAA;oCACb,IAAI,OAAO,SAAS,YAAY,SAAS,MAAM;wCAC7C,OAAO,KAAK,SAAS,CAAC;oCACxB;oCACA,OAAO,OAAO;gCAChB;4BACF;4BACA,IAAI,OAAO,QAAQ,UAAU;gCAC3B,+DAA+D;gCAC/D,OAAO,KAAK,SAAS,CAAC;4BACxB;4BACA,OAAO,OAAO;wBAChB;wBAEA,gDAAgD;wBAChD,MAAM,cAAc;4BAClB,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;4BACnC,SAAS,UAAU,KAAK,OAAO,KAAK;4BACpC,UAAU,OAAO,KAAK,QAAQ,IAAI;4BAClC,UAAU,OAAO,KAAK,QAAQ,IAAI;4BAClC,cAAc,OAAO,KAAK,YAAY,IAAI;4BAC1C,aAAa,OAAO,KAAK,WAAW,IAAI;4BACxC,gBAAgB,OAAO,KAAK,cAAc,IAAI;4BAC9C,kBAAkB,OAAO,KAAK,gBAAgB,IAAI;4BAClD,SAAS,OAAO,KAAK,OAAO,IAAI;4BAChC,cAAc,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,YAAY,KAAK;4BACvD,sBAAsB,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,oBAAoB,KAAK;4BACvE,qBAAqB,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,mBAAmB,KAAK;4BACrE,UAAU,MAAM,OAAO,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,MAAO,OAAO,QAAQ,EAAE;4BACnF,QAAQ,OAAO,KAAK,MAAM,IAAI;4BAC9B,UAAU,OAAO,KAAK,QAAQ,IAAI;4BAClC,UAAU,OAAO,KAAK,QAAQ,IAAI;4BAClC,WAAW,KAAK,SAAS,GAAG,IAAI,KAAK,KAAK,SAAS,IAAI,IAAI;wBAC7D;wBAEA,QAAQ,GAAG,CAAC,sBAAsB,KAAK,SAAS,CAAC,aAAa,MAAM;wBAEpE,MAAM,kKAAA,CAAA,+BAA4B,CAAC,iBAAiB,CAAC,aAAa,QAAQ;wBAC1E;wBACA,QAAQ,GAAG,CAAC,iCAAiC,KAAK,EAAE;oBACtD,EAAE,OAAO,OAAO;wBACd;wBACA,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;wBACrE,QAAQ,KAAK,CAAC,6BAA6B,KAAK,EAAE,EAAE;wBACpD,QAAQ,KAAK,CAAC,eAAe;wBAC7B,OAAO,IAAI,CAAC,CAAC,wBAAwB,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,cAAc;oBACpE;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,OAAO,IAAI,CAAC,CAAC,iCAAiC,EAAE,OAAO;YACzD;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,OAAO;IACzC;IAEA,OAAO;QAAE;QAAU;QAAQ;IAAO;AACpC;AAGO,eAAe,eAAe,MAAc;IACjD,MAAM,SAA0B;QAC9B,eAAe;YAAE,UAAU;YAAG,QAAQ;QAAE;QACxC,gBAAgB;YAAE,UAAU;YAAG,QAAQ;QAAE;QACzC,WAAW;YAAE,UAAU;YAAG,QAAQ;QAAE;QACpC,QAAQ,EAAE;IACZ;IAEA,QAAQ,GAAG,CAAC;IAEZ,kCAAkC;IAClC,MAAM,qBAAqB,MAAM,qBAAqB;IACtD,OAAO,aAAa,GAAG;QACrB,UAAU,mBAAmB,QAAQ;QACrC,QAAQ,mBAAmB,MAAM;IACnC;IACA,OAAO,MAAM,CAAC,IAAI,IAAI,mBAAmB,MAAM;IAE/C,iEAAiE;IACjE,IAAI,iBAAiB;IACrB,IAAI;QACF,MAAM,WAAW,MAAM,iKAAA,CAAA,8BAA2B,CAAC,oBAAoB,CAAC;QACxE,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,iBAAiB,QAAQ,CAAC,EAAE,CAAC,EAAE;QACjC;IACF,EAAE,OAAO,OAAO;QACd,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,iDAAiD,EAAE,OAAO;IAChF;IAEA,wDAAwD;IACxD,IAAI,gBAAgB;QAClB,MAAM,cAAc,MAAM,sBAAsB,QAAQ;QACxD,OAAO,cAAc,GAAG;YACtB,UAAU,YAAY,QAAQ;YAC9B,QAAQ,YAAY,MAAM;QAC5B;QACA,OAAO,MAAM,CAAC,IAAI,IAAI,YAAY,MAAM;IAC1C;IAEA,2EAA2E;IAC3E,mEAAmE;IAEnE,QAAQ,GAAG,CAAC,wBAAwB;IACpC,OAAO;AACT;AAGO,SAAS;IACd,MAAM,uBAAuB,aAAa,OAAO,CAAC,2BAChD,aAAa,OAAO,CAAC;IACvB,MAAM,gBAAgB,aAAa,OAAO,CAAC;IAE3C,OAAO,CAAC,CAAC,CAAC,wBAAwB,aAAa;AACjD;AAGO,SAAS;IACd,MAAM,eAAe;QACnB;QACA;QACA;QACA;KACD;IAED,aAAa,OAAO,CAAC,CAAA;QACnB,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,IAAI,mBAAmB,CAAC,EAAE;QAC7D;IACF;IAEA,QAAQ,GAAG,CAAC;AACd;AAGO,SAAS;IACd,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAKzD;QACD,UAAU;QACV,WAAW;QACX,YAAY;IACd;IAEA,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,UAAU;YACZ,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ;QAEb,mBAAmB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAK,CAAC;QAExD,IAAI;YACF,MAAM,SAAS,MAAM,eAAe;YAEpC,iDAAiD;YACjD,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,GAAG;gBAC9B;YACF;YAEA,mBAAmB;gBACjB,UAAU;gBACV,WAAW;gBACX,YAAY;gBACZ;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,mBAAmB,CAAA,OAAQ,CAAC;oBAC1B,GAAG,IAAI;oBACP,WAAW;oBACX,QAAQ;wBACN,GAAG,KAAK,MAAM;wBACd,QAAQ;+BAAK,KAAK,MAAM,EAAE,UAAU,EAAE;4BAAG,CAAC,kBAAkB,EAAE,OAAO;yBAAC;oBACxE;gBACF,CAAC;QACH;IACF;IAEA,OAAO;QACL,GAAG,eAAe;QAClB;IACF;AACF;;AAKA,gDAAgD;AAChD,SAAS;IACP,IAAI;QACF,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;QACnE,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,gBAAgB,MAAM,MAAM;QAExC,MAAM,OAAO,CAAC,CAAC,MAAW;YACxB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;YAC1D,QAAQ,GAAG,CAAC,iBAAiB,OAAO,KAAK,OAAO;YAChD,QAAQ,GAAG,CAAC,kBAAkB,KAAK,OAAO;YAC1C,QAAQ,GAAG,CAAC,wBAAwB,KAAK,SAAS,CAAC,MAAM,MAAM;YAE/D,2BAA2B;YAC3B,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;gBACxB,MAAM,QAAQ,IAAI,CAAC,IAAI;gBACvB,IAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,CAAC,CAAC,iBAAiB,IAAI,GAAG;oBAC3E,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI,EAAE,CAAC,EAAE;gBACtD;YACF;QACF;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;IACjC;AACF;AAaA,gDAAgD;AAChD,eAAe;IACb,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,SAAS,eAAe,KAAK,GAAG;QACtC,MAAM,iBAAiB,gBAAgB,KAAK,GAAG;QAE/C,wDAAwD;QACxD,MAAM,YAAY;YAChB,IAAI,eAAe,KAAK,GAAG;YAC3B,SAAS;YACT,UAAU;YACV,UAAU;YACV,cAAc;YACd,aAAa;YACb,gBAAgB;YAChB,kBAAkB;YAClB,SAAS;YACT,cAAc;YACd,sBAAsB;YACtB,qBAAqB;YACrB,UAAU;gBAAC;gBAAS;aAAS;YAC7B,QAAQ;YACR,UAAU;YACV,UAAU;YACV,WAAW,IAAI;QACjB;QAEA,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,MAAM,EAAE,4BAA4B,EAAE,GAAG;QACzC,MAAM,6BAA6B,iBAAiB,CAAC,WAAW,QAAQ;QAExE,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;AACF;AAEA,qCAAqC;AACrC,uCAAmC;;AAGnC", "debugId": null}}, {"offset": {"line": 6811, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/migration/data-migration-dialog.tsx"], "sourcesContent": ["// Data migration dialog component\r\nimport React, { useState } from 'react';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { CheckCircle, AlertCircle, Loader2, Database, Upload } from 'lucide-react';\r\nimport { migrateAllData, needsMigration, clearMigratedData } from '@/lib/firebase/migration';\r\nimport { useUserId } from '@/hooks/use-firebase-auth';\r\nimport type { MigrationStatus } from '@/lib/firebase/migration';\r\n\r\ninterface DataMigrationDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  onComplete?: () => void;\r\n}\r\n\r\nexport function DataMigrationDialog({ \r\n  open, \r\n  onOpenChange, \r\n  onComplete \r\n}: DataMigrationDialogProps) {\r\n  const userId = useUserId();\r\n  const [migrationState, setMigrationState] = useState<{\r\n    status: 'idle' | 'running' | 'complete' | 'error';\r\n    progress: number;\r\n    currentStep: string;\r\n    result?: MigrationStatus;\r\n  }>({\r\n    status: 'idle',\r\n    progress: 0,\r\n    currentStep: '',\r\n  });\r\n\r\n  const runMigration = async () => {\r\n    if (!userId) {\r\n      setMigrationState(prev => ({\r\n        ...prev,\r\n        status: 'error',\r\n        currentStep: 'Authentication required',\r\n      }));\r\n      return;\r\n    }\r\n\r\n    setMigrationState({\r\n      status: 'running',\r\n      progress: 0,\r\n      currentStep: 'Starting migration...',\r\n    });\r\n\r\n    try {\r\n      // Step 1: Check what needs migration\r\n      setMigrationState(prev => ({\r\n        ...prev,\r\n        progress: 10,\r\n        currentStep: 'Checking local data...',\r\n      }));\r\n\r\n      if (!needsMigration()) {\r\n        setMigrationState({\r\n          status: 'complete',\r\n          progress: 100,\r\n          currentStep: 'No data to migrate',\r\n          result: {\r\n            brandProfiles: { migrated: 0, failed: 0 },\r\n            generatedPosts: { migrated: 0, failed: 0 },\r\n            artifacts: { migrated: 0, failed: 0 },\r\n            errors: [],\r\n          },\r\n        });\r\n        return;\r\n      }\r\n\r\n      // Step 2: Migrate brand profiles\r\n      setMigrationState(prev => ({\r\n        ...prev,\r\n        progress: 30,\r\n        currentStep: 'Migrating brand profiles...',\r\n      }));\r\n\r\n      // Step 3: Migrate generated posts\r\n      setMigrationState(prev => ({\r\n        ...prev,\r\n        progress: 60,\r\n        currentStep: 'Migrating generated posts...',\r\n      }));\r\n\r\n      // Step 4: Run full migration\r\n      const result = await migrateAllData(userId);\r\n\r\n      setMigrationState(prev => ({\r\n        ...prev,\r\n        progress: 90,\r\n        currentStep: 'Finalizing migration...',\r\n      }));\r\n\r\n      // Step 5: Clear localStorage if successful\r\n      if (result.errors.length === 0) {\r\n        clearMigratedData();\r\n      }\r\n\r\n      setMigrationState({\r\n        status: result.errors.length === 0 ? 'complete' : 'error',\r\n        progress: 100,\r\n        currentStep: result.errors.length === 0 ? 'Migration completed!' : 'Migration completed with errors',\r\n        result,\r\n      });\r\n\r\n      if (result.errors.length === 0 && onComplete) {\r\n        setTimeout(onComplete, 1000);\r\n      }\r\n    } catch (error) {\r\n      setMigrationState({\r\n        status: 'error',\r\n        progress: 0,\r\n        currentStep: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleClose = () => {\r\n    if (migrationState.status !== 'running') {\r\n      onOpenChange(false);\r\n      setMigrationState({\r\n        status: 'idle',\r\n        progress: 0,\r\n        currentStep: '',\r\n      });\r\n    }\r\n  };\r\n\r\n  const getMigrationSummary = () => {\r\n    if (!migrationState.result) return null;\r\n\r\n    const { brandProfiles, generatedPosts, artifacts } = migrationState.result;\r\n    const totalMigrated = brandProfiles.migrated + generatedPosts.migrated + artifacts.migrated;\r\n    const totalFailed = brandProfiles.failed + generatedPosts.failed + artifacts.failed;\r\n\r\n    return (\r\n      <div className=\"space-y-3\">\r\n        <div className=\"grid grid-cols-3 gap-4 text-sm\">\r\n          <div className=\"text-center\">\r\n            <div className=\"font-medium\">Brand Profiles</div>\r\n            <div className=\"text-green-600\">{brandProfiles.migrated} migrated</div>\r\n            {brandProfiles.failed > 0 && (\r\n              <div className=\"text-red-600\">{brandProfiles.failed} failed</div>\r\n            )}\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"font-medium\">Generated Posts</div>\r\n            <div className=\"text-green-600\">{generatedPosts.migrated} migrated</div>\r\n            {generatedPosts.failed > 0 && (\r\n              <div className=\"text-red-600\">{generatedPosts.failed} failed</div>\r\n            )}\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"font-medium\">Artifacts</div>\r\n            <div className=\"text-green-600\">{artifacts.migrated} migrated</div>\r\n            {artifacts.failed > 0 && (\r\n              <div className=\"text-red-600\">{artifacts.failed} failed</div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {totalMigrated > 0 && (\r\n          <Alert>\r\n            <CheckCircle className=\"h-4 w-4\" />\r\n            <AlertDescription>\r\n              Successfully migrated {totalMigrated} items to the cloud database.\r\n            </AlertDescription>\r\n          </Alert>\r\n        )}\r\n\r\n        {totalFailed > 0 && (\r\n          <Alert variant=\"destructive\">\r\n            <AlertCircle className=\"h-4 w-4\" />\r\n            <AlertDescription>\r\n              {totalFailed} items failed to migrate. Your local data is still available.\r\n            </AlertDescription>\r\n          </Alert>\r\n        )}\r\n\r\n        {migrationState.result.errors.length > 0 && (\r\n          <div className=\"max-h-32 overflow-y-auto\">\r\n            <div className=\"text-sm font-medium mb-2\">Migration Errors:</div>\r\n            <div className=\"space-y-1 text-xs text-red-600\">\r\n              {migrationState.result.errors.map((error, index) => (\r\n                <div key={index}>{error}</div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={handleClose}>\r\n      <DialogContent className=\"sm:max-w-md\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"flex items-center gap-2\">\r\n            <Database className=\"h-5 w-5\" />\r\n            Data Migration\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            Migrate your local data to the cloud database for better reliability and sync across devices.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-4\">\r\n          {migrationState.status === 'idle' && (\r\n            <div className=\"space-y-4\">\r\n              <Alert>\r\n                <Upload className=\"h-4 w-4\" />\r\n                <AlertDescription>\r\n                  We found local data that can be migrated to the cloud. This will enable:\r\n                  <ul className=\"mt-2 list-disc list-inside text-sm space-y-1\">\r\n                    <li>Sync across devices</li>\r\n                    <li>Better data reliability</li>\r\n                    <li>Real-time updates</li>\r\n                    <li>Backup and recovery</li>\r\n                  </ul>\r\n                </AlertDescription>\r\n              </Alert>\r\n\r\n              <div className=\"flex gap-2\">\r\n                <Button onClick={runMigration} className=\"flex-1\">\r\n                  Start Migration\r\n                </Button>\r\n                <Button variant=\"outline\" onClick={handleClose}>\r\n                  Skip for Now\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {migrationState.status === 'running' && (\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                <span className=\"text-sm\">{migrationState.currentStep}</span>\r\n              </div>\r\n              <Progress value={migrationState.progress} />\r\n            </div>\r\n          )}\r\n\r\n          {(migrationState.status === 'complete' || migrationState.status === 'error') && (\r\n            <div className=\"space-y-4\">\r\n              {getMigrationSummary()}\r\n              \r\n              <div className=\"flex gap-2\">\r\n                {migrationState.status === 'complete' && (\r\n                  <Button onClick={handleClose} className=\"flex-1\">\r\n                    <CheckCircle className=\"h-4 w-4 mr-2\" />\r\n                    Done\r\n                  </Button>\r\n                )}\r\n                {migrationState.status === 'error' && (\r\n                  <>\r\n                    <Button onClick={runMigration} variant=\"outline\">\r\n                      Retry\r\n                    </Button>\r\n                    <Button onClick={handleClose} className=\"flex-1\">\r\n                      Close\r\n                    </Button>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n\r\n// Hook to check if migration is needed and show dialog\r\nexport function useMigrationDialog() {\r\n  const [showDialog, setShowDialog] = useState(false);\r\n  const userId = useUserId();\r\n\r\n  React.useEffect(() => {\r\n    if (userId && needsMigration()) {\r\n      setShowDialog(true);\r\n    }\r\n  }, [userId]);\r\n\r\n  return {\r\n    showDialog,\r\n    setShowDialog,\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;;AAClC;AACA;AAOA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;;;;AASO,SAAS,oBAAoB,EAClC,IAAI,EACJ,YAAY,EACZ,UAAU,EACe;IACzB,MAAM,SAAS,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAKhD;QACD,QAAQ;QACR,UAAU;QACV,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ;YACX,kBAAkB,CAAA,OAAQ,CAAC;oBACzB,GAAG,IAAI;oBACP,QAAQ;oBACR,aAAa;gBACf,CAAC;YACD;QACF;QAEA,kBAAkB;YAChB,QAAQ;YACR,UAAU;YACV,aAAa;QACf;QAEA,IAAI;YACF,qCAAqC;YACrC,kBAAkB,CAAA,OAAQ,CAAC;oBACzB,GAAG,IAAI;oBACP,UAAU;oBACV,aAAa;gBACf,CAAC;YAED,IAAI,CAAC,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD,KAAK;gBACrB,kBAAkB;oBAChB,QAAQ;oBACR,UAAU;oBACV,aAAa;oBACb,QAAQ;wBACN,eAAe;4BAAE,UAAU;4BAAG,QAAQ;wBAAE;wBACxC,gBAAgB;4BAAE,UAAU;4BAAG,QAAQ;wBAAE;wBACzC,WAAW;4BAAE,UAAU;4BAAG,QAAQ;wBAAE;wBACpC,QAAQ,EAAE;oBACZ;gBACF;gBACA;YACF;YAEA,iCAAiC;YACjC,kBAAkB,CAAA,OAAQ,CAAC;oBACzB,GAAG,IAAI;oBACP,UAAU;oBACV,aAAa;gBACf,CAAC;YAED,kCAAkC;YAClC,kBAAkB,CAAA,OAAQ,CAAC;oBACzB,GAAG,IAAI;oBACP,UAAU;oBACV,aAAa;gBACf,CAAC;YAED,6BAA6B;YAC7B,MAAM,SAAS,MAAM,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD,EAAE;YAEpC,kBAAkB,CAAA,OAAQ,CAAC;oBACzB,GAAG,IAAI;oBACP,UAAU;oBACV,aAAa;gBACf,CAAC;YAED,2CAA2C;YAC3C,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,GAAG;gBAC9B,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD;YAClB;YAEA,kBAAkB;gBAChB,QAAQ,OAAO,MAAM,CAAC,MAAM,KAAK,IAAI,aAAa;gBAClD,UAAU;gBACV,aAAa,OAAO,MAAM,CAAC,MAAM,KAAK,IAAI,yBAAyB;gBACnE;YACF;YAEA,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,KAAK,YAAY;gBAC5C,WAAW,YAAY;YACzB;QACF,EAAE,OAAO,OAAO;YACd,kBAAkB;gBAChB,QAAQ;gBACR,UAAU;gBACV,aAAa,CAAC,kBAAkB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YAC9F;QACF;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,eAAe,MAAM,KAAK,WAAW;YACvC,aAAa;YACb,kBAAkB;gBAChB,QAAQ;gBACR,UAAU;gBACV,aAAa;YACf;QACF;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,eAAe,MAAM,EAAE,OAAO;QAEnC,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,eAAe,MAAM;QAC1E,MAAM,gBAAgB,cAAc,QAAQ,GAAG,eAAe,QAAQ,GAAG,UAAU,QAAQ;QAC3F,MAAM,cAAc,cAAc,MAAM,GAAG,eAAe,MAAM,GAAG,UAAU,MAAM;QAEnF,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAc;;;;;;8CAC7B,8OAAC;oCAAI,WAAU;;wCAAkB,cAAc,QAAQ;wCAAC;;;;;;;gCACvD,cAAc,MAAM,GAAG,mBACtB,8OAAC;oCAAI,WAAU;;wCAAgB,cAAc,MAAM;wCAAC;;;;;;;;;;;;;sCAGxD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAc;;;;;;8CAC7B,8OAAC;oCAAI,WAAU;;wCAAkB,eAAe,QAAQ;wCAAC;;;;;;;gCACxD,eAAe,MAAM,GAAG,mBACvB,8OAAC;oCAAI,WAAU;;wCAAgB,eAAe,MAAM;wCAAC;;;;;;;;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAc;;;;;;8CAC7B,8OAAC;oCAAI,WAAU;;wCAAkB,UAAU,QAAQ;wCAAC;;;;;;;gCACnD,UAAU,MAAM,GAAG,mBAClB,8OAAC;oCAAI,WAAU;;wCAAgB,UAAU,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;gBAKrD,gBAAgB,mBACf,8OAAC,iIAAA,CAAA,QAAK;;sCACJ,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC,iIAAA,CAAA,mBAAgB;;gCAAC;gCACO;gCAAc;;;;;;;;;;;;;gBAK1C,cAAc,mBACb,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC,iIAAA,CAAA,mBAAgB;;gCACd;gCAAY;;;;;;;;;;;;;gBAKlB,eAAe,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,mBACrC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAA2B;;;;;;sCAC1C,8OAAC;4BAAI,WAAU;sCACZ,eAAe,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACxC,8OAAC;8CAAiB;mCAAR;;;;;;;;;;;;;;;;;;;;;;IAOxB;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC;oBAAI,WAAU;;wBACZ,eAAe,MAAM,KAAK,wBACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;;sDACJ,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,mBAAgB;;gDAAC;8DAEhB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;8CAKV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAc,WAAU;sDAAS;;;;;;sDAGlD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;sDAAa;;;;;;;;;;;;;;;;;;wBAOrD,eAAe,MAAM,KAAK,2BACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAK,WAAU;sDAAW,eAAe,WAAW;;;;;;;;;;;;8CAEvD,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,OAAO,eAAe,QAAQ;;;;;;;;;;;;wBAI3C,CAAC,eAAe,MAAM,KAAK,cAAc,eAAe,MAAM,KAAK,OAAO,mBACzE,8OAAC;4BAAI,WAAU;;gCACZ;8CAED,8OAAC;oCAAI,WAAU;;wCACZ,eAAe,MAAM,KAAK,4BACzB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAa,WAAU;;8DACtC,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAI3C,eAAe,MAAM,KAAK,yBACzB;;8DACE,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAS;oDAAc,SAAQ;8DAAU;;;;;;8DAGjD,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAS;oDAAa,WAAU;8DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrE;AAGO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD;IAEvB,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,UAAU,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD,KAAK;YAC9B,cAAc;QAChB;IACF,GAAG;QAAC;KAAO;IAEX,OAAO;QACL;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 7439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/auth/auth-wrapper.tsx"], "sourcesContent": ["// Authentication wrapper component\r\nimport React from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Loader2, User, Zap } from 'lucide-react';\r\nimport { useFirebaseAuth } from '@/hooks/use-firebase-auth';\r\nimport { DataMigrationDialog, useMigrationDialog } from '@/components/migration/data-migration-dialog';\r\n\r\ninterface AuthWrapperProps {\r\n  children: React.ReactNode;\r\n  requireAuth?: boolean;\r\n}\r\n\r\nexport function AuthWrapper({ children, requireAuth = false }: AuthWrapperProps) {\r\n  const { user, loading, signInAnonymous } = useFirebaseAuth();\r\n  const { showDialog, setShowDialog } = useMigrationDialog();\r\n\r\n  // Show loading spinner while checking auth\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-4\" />\r\n          <p className=\"text-muted-foreground\">Loading...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show auth prompt if user is not signed in and auth is required\r\n  if (!user && requireAuth) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center p-4\">\r\n        <Card className=\"w-full max-w-md\">\r\n          <CardHeader className=\"text-center\">\r\n            <div className=\"mx-auto mb-4 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center\">\r\n              <User className=\"h-6 w-6 text-primary\" />\r\n            </div>\r\n            <CardTitle>Welcome to Nevis AI</CardTitle>\r\n            <CardDescription>\r\n              Sign in to access your personalized AI content generation platform\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-4\">\r\n            <Button \r\n              onClick={signInAnonymous} \r\n              className=\"w-full\"\r\n              size=\"lg\"\r\n            >\r\n              <Zap className=\"h-4 w-4 mr-2\" />\r\n              Get Started (Demo Mode)\r\n            </Button>\r\n            \r\n            <div className=\"text-center text-sm text-muted-foreground\">\r\n              Demo mode allows you to try all features without creating an account\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render children with migration dialog\r\n  return (\r\n    <>\r\n      {children}\r\n      <DataMigrationDialog \r\n        open={showDialog} \r\n        onOpenChange={setShowDialog}\r\n        onComplete={() => setShowDialog(false)}\r\n      />\r\n    </>\r\n  );\r\n}\r\n\r\n// Higher-order component for pages that require authentication\r\nexport function withAuth<P extends object>(\r\n  Component: React.ComponentType<P>,\r\n  requireAuth: boolean = true\r\n) {\r\n  return function AuthenticatedComponent(props: P) {\r\n    return (\r\n      <AuthWrapper requireAuth={requireAuth}>\r\n        <Component {...props} />\r\n      </AuthWrapper>\r\n    );\r\n  };\r\n}\r\n\r\n// Hook for getting auth status in components\r\nexport function useAuthStatus() {\r\n  const { user, loading } = useFirebaseAuth();\r\n  \r\n  return {\r\n    isAuthenticated: !!user,\r\n    isAnonymous: user?.isAnonymous || false,\r\n    isLoading: loading,\r\n    user,\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;;;;AAEnC;AACA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;AAOO,SAAS,YAAY,EAAE,QAAQ,EAAE,cAAc,KAAK,EAAoB;IAC7E,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IACzD,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD;IAEvD,2CAA2C;IAC3C,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,iEAAiE;IACjE,IAAI,CAAC,QAAQ,aAAa;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,MAAK;;kDAEL,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIlC,8OAAC;gCAAI,WAAU;0CAA4C;;;;;;;;;;;;;;;;;;;;;;;IAOrE;IAEA,wCAAwC;IACxC,qBACE;;YACG;0BACD,8OAAC,8JAAA,CAAA,sBAAmB;gBAClB,MAAM;gBACN,cAAc;gBACd,YAAY,IAAM,cAAc;;;;;;;;AAIxC;AAGO,SAAS,SACd,SAAiC,EACjC,cAAuB,IAAI;IAE3B,OAAO,SAAS,uBAAuB,KAAQ;QAC7C,qBACE,8OAAC;YAAY,aAAa;sBACxB,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAExC,OAAO;QACL,iBAAiB,CAAC,CAAC;QACnB,aAAa,MAAM,eAAe;QAClC,WAAW;QACX;IACF;AACF", "debugId": null}}, {"offset": {"line": 7637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/layout/brand-color-provider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { useUnifiedBrand } from '@/contexts/unified-brand-context';\r\nimport { hexToHsl } from '@/lib/utils';\r\n\r\n// FORCE COMPLETE CACHE PURGE - TIMESTAMP: 2025-01-21-15:30:00\r\nconst CACHE_BUSTER = `v6-ultimate-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n\r\ninterface BrandColorProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Helper function to get colors from localStorage as fallback\r\nfunction getColorsFromLocalStorage() {\r\n  try {\r\n    const completeBrandProfile = localStorage.getItem('completeBrandProfile');\r\n    if (completeBrandProfile) {\r\n      const profile = JSON.parse(completeBrandProfile);\r\n      return {\r\n        primaryColor: profile.primaryColor,\r\n        accentColor: profile.accentColor,\r\n        backgroundColor: profile.backgroundColor\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.warn('🎨 Failed to get colors from localStorage:', error);\r\n  }\r\n  return null;\r\n}\r\n\r\nexport function BrandColorProvider({ children }: BrandColorProviderProps) {\r\n  const { currentBrand, loading } = useUnifiedBrand();\r\n\r\n  // Inline color persistence function\r\n  const getPersistedColors = () => {\r\n    try {\r\n      const savedColors = localStorage.getItem('brandColors');\r\n      if (savedColors) {\r\n        const colors = JSON.parse(savedColors);\r\n        return {\r\n          primaryColor: colors.primaryColor || '#3b82f6',\r\n          accentColor: colors.accentColor || '#10b981',\r\n          backgroundColor: colors.backgroundColor || '#f8fafc'\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to get persisted colors:', error);\r\n    }\r\n\r\n    return {\r\n      primaryColor: '#3b82f6',\r\n      accentColor: '#10b981',\r\n      backgroundColor: '#f8fafc'\r\n    };\r\n  };\r\n\r\n  // Initialize with persisted colors to prevent flash of wrong colors\r\n  const [style, setStyle] = useState<React.CSSProperties>(() => {\r\n    const persistedColors = getPersistedColors();\r\n    console.log('🎨 Initializing BrandColorProvider with persisted colors:', persistedColors);\r\n    return {\r\n      '--primary-color': persistedColors.primaryColor,\r\n      '--accent-color': persistedColors.accentColor,\r\n      '--background-color': persistedColors.backgroundColor,\r\n    } as React.CSSProperties;\r\n  });\r\n\r\n  // FORCE COMPLETE MODULE RELOAD - Enhanced BrandColorProvider v6.0 ULTIMATE CACHE PURGE\r\n  console.log('🚀🚀🚀 ENHANCED BRAND COLOR PROVIDER v6.0 ULTIMATE CACHE PURGE LOADED 🚀🚀🚀');\r\n  console.log('🔥🔥🔥 AGGRESSIVE CACHE BUSTER: Forcing complete component reload');\r\n  console.log('🔥 CACHE_BUSTER:', CACHE_BUSTER);\r\n\r\n  // Add unique component identifier to force complete re-render\r\n  const componentId = `brand-color-provider-${CACHE_BUSTER}`;\r\n\r\n  // Listen for brand changes to update colors immediately\r\n  useEffect(() => {\r\n    const handleBrandChange = (event: any) => {\r\n      console.log('🎨 BrandColorProvider received brand change event:', event.detail);\r\n      if (event.detail && event.detail.brand) {\r\n        const brand = event.detail.brand;\r\n        console.log('🎨 Updating colors from brand change event:', {\r\n          primaryColor: brand.primaryColor,\r\n          accentColor: brand.accentColor,\r\n          backgroundColor: brand.backgroundColor\r\n        });\r\n\r\n        // Force immediate color update\r\n        const newStyle = {\r\n          '--primary-color': brand.primaryColor || '#3b82f6',\r\n          '--accent-color': brand.accentColor || '#10b981',\r\n          '--background-color': brand.backgroundColor || '#f8fafc',\r\n        } as React.CSSProperties;\r\n\r\n        setStyle(newStyle);\r\n        console.log('🎨 Applied immediate color update from brand change event');\r\n      }\r\n    };\r\n\r\n    window.addEventListener('brandChanged', handleBrandChange);\r\n    return () => window.removeEventListener('brandChanged', handleBrandChange);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    console.log('🎨🎨🎨 ENHANCED v6.0 ULTIMATE CACHE PURGE BrandColorProvider useEffect triggered:', {\r\n      currentBrand: currentBrand ? {\r\n        name: currentBrand.businessName || currentBrand.name,\r\n        id: currentBrand.id,\r\n        primaryColor: currentBrand.primaryColor,\r\n        accentColor: currentBrand.accentColor,\r\n        backgroundColor: currentBrand.backgroundColor,\r\n        allKeys: Object.keys(currentBrand)\r\n      } : null,\r\n      loading\r\n    });\r\n\r\n    // Log the full brand object to see what properties it actually has\r\n    if (currentBrand) {\r\n      console.log('🎨 Full currentBrand object:', currentBrand);\r\n    }\r\n\r\n    if (!currentBrand || loading) {\r\n      console.log('🎨 ENHANCED BrandColorProvider: Skipping color application - no brand or loading');\r\n      return;\r\n    }\r\n\r\n    console.log('🎨 ENHANCED BRAND COLOR PROVIDER - Applying brand colors for:', currentBrand.businessName || currentBrand.name);\r\n\r\n    // Get colors from brand object\r\n    let primaryColor = currentBrand.primaryColor;\r\n    let accentColor = currentBrand.accentColor;\r\n    let backgroundColor = currentBrand.backgroundColor;\r\n\r\n    console.log('🎨 Brand object colors:', { primaryColor, accentColor, backgroundColor });\r\n\r\n    // Always try localStorage fallback if any colors are missing or empty\r\n    const hasValidPrimary = primaryColor && primaryColor.trim() !== '' && primaryColor !== '#000000';\r\n    const hasValidAccent = accentColor && accentColor.trim() !== '' && accentColor !== '#000000';\r\n    const hasValidBackground = backgroundColor && backgroundColor.trim() !== '' && backgroundColor !== '#000000';\r\n\r\n    if (!hasValidPrimary || !hasValidAccent || !hasValidBackground) {\r\n      console.log('🎨 Colors missing or invalid in brand object, checking localStorage fallback...');\r\n      console.log('🎨 Validity check:', { hasValidPrimary, hasValidAccent, hasValidBackground });\r\n\r\n      const localStorageColors = getColorsFromLocalStorage();\r\n      if (localStorageColors) {\r\n        console.log('🎨 Found localStorage colors:', localStorageColors);\r\n        primaryColor = hasValidPrimary ? primaryColor : localStorageColors.primaryColor;\r\n        accentColor = hasValidAccent ? accentColor : localStorageColors.accentColor;\r\n        backgroundColor = hasValidBackground ? backgroundColor : localStorageColors.backgroundColor;\r\n        console.log('🎨 Using localStorage colors as fallback:', { primaryColor, accentColor, backgroundColor });\r\n      } else {\r\n        console.log('🎨 No localStorage colors found');\r\n      }\r\n    } else {\r\n      console.log('🎨 All brand colors are valid, using brand object colors');\r\n    }\r\n\r\n    const newStyle: React.CSSProperties = {};\r\n\r\n    // Apply primary color\r\n    if (primaryColor) {\r\n      const hslPrimary = hexToHsl(primaryColor);\r\n      if (hslPrimary) {\r\n        newStyle['--primary-hsl'] = hslPrimary;\r\n        newStyle['--primary'] = hslPrimary;\r\n        newStyle['--ring'] = hslPrimary;\r\n        console.log('🎨 Primary color applied:', primaryColor, '→', hslPrimary);\r\n      }\r\n    } else {\r\n      console.log('🎨 No primary color found in brand or localStorage');\r\n    }\r\n\r\n    // Apply accent color\r\n    if (accentColor) {\r\n      const hslAccent = hexToHsl(accentColor);\r\n      if (hslAccent) {\r\n        newStyle['--accent-hsl'] = hslAccent;\r\n        newStyle['--accent'] = hslAccent;\r\n        console.log('🎨 Accent color applied:', accentColor, '→', hslAccent);\r\n      }\r\n    } else {\r\n      console.log('🎨 No accent color found in brand or localStorage');\r\n    }\r\n\r\n    // Apply background color\r\n    if (backgroundColor) {\r\n      const hslBackground = hexToHsl(backgroundColor);\r\n      if (hslBackground) {\r\n        newStyle['--background-hsl'] = hslBackground;\r\n        newStyle['--background'] = hslBackground;\r\n        console.log('🎨 Background color applied:', backgroundColor, '→', hslBackground);\r\n      }\r\n    } else {\r\n      console.log('🎨 No background color found in brand or localStorage');\r\n    }\r\n\r\n    console.log('🎨 Setting style:', newStyle);\r\n    setStyle(newStyle);\r\n  }, [currentBrand, loading]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex-1 flex items-center justify-center\">\r\n        <p>Loading Brand Colors...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      key={componentId}\r\n      data-cache-buster={CACHE_BUSTER}\r\n      data-component=\"brand-color-provider-v6-ultimate\"\r\n      className=\"flex flex-1\"\r\n      style={style}\r\n    >\r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,8DAA8D;AAC9D,MAAM,eAAe,CAAC,YAAY,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAM3F,8DAA8D;AAC9D,SAAS;IACP,IAAI;QACF,MAAM,uBAAuB,aAAa,OAAO,CAAC;QAClD,IAAI,sBAAsB;YACxB,MAAM,UAAU,KAAK,KAAK,CAAC;YAC3B,OAAO;gBACL,cAAc,QAAQ,YAAY;gBAClC,aAAa,QAAQ,WAAW;gBAChC,iBAAiB,QAAQ,eAAe;YAC1C;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8CAA8C;IAC7D;IACA,OAAO;AACT;AAEO,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IACtE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;IAEhD,oCAAoC;IACpC,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,aAAa;gBACf,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,OAAO;oBACL,cAAc,OAAO,YAAY,IAAI;oBACrC,aAAa,OAAO,WAAW,IAAI;oBACnC,iBAAiB,OAAO,eAAe,IAAI;gBAC7C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;QAEA,OAAO;YACL,cAAc;YACd,aAAa;YACb,iBAAiB;QACnB;IACF;IAEA,oEAAoE;IACpE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,MAAM,kBAAkB;QACxB,QAAQ,GAAG,CAAC,6DAA6D;QACzE,OAAO;YACL,mBAAmB,gBAAgB,YAAY;YAC/C,kBAAkB,gBAAgB,WAAW;YAC7C,sBAAsB,gBAAgB,eAAe;QACvD;IACF;IAEA,uFAAuF;IACvF,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,oBAAoB;IAEhC,8DAA8D;IAC9D,MAAM,cAAc,CAAC,qBAAqB,EAAE,cAAc;IAE1D,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB,CAAC;YACzB,QAAQ,GAAG,CAAC,sDAAsD,MAAM,MAAM;YAC9E,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,KAAK,EAAE;gBACtC,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK;gBAChC,QAAQ,GAAG,CAAC,+CAA+C;oBACzD,cAAc,MAAM,YAAY;oBAChC,aAAa,MAAM,WAAW;oBAC9B,iBAAiB,MAAM,eAAe;gBACxC;gBAEA,+BAA+B;gBAC/B,MAAM,WAAW;oBACf,mBAAmB,MAAM,YAAY,IAAI;oBACzC,kBAAkB,MAAM,WAAW,IAAI;oBACvC,sBAAsB,MAAM,eAAe,IAAI;gBACjD;gBAEA,SAAS;gBACT,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,OAAO,gBAAgB,CAAC,gBAAgB;QACxC,OAAO,IAAM,OAAO,mBAAmB,CAAC,gBAAgB;IAC1D,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,qFAAqF;YAC/F,cAAc,eAAe;gBAC3B,MAAM,aAAa,YAAY,IAAI,aAAa,IAAI;gBACpD,IAAI,aAAa,EAAE;gBACnB,cAAc,aAAa,YAAY;gBACvC,aAAa,aAAa,WAAW;gBACrC,iBAAiB,aAAa,eAAe;gBAC7C,SAAS,OAAO,IAAI,CAAC;YACvB,IAAI;YACJ;QACF;QAEA,mEAAmE;QACnE,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC,gCAAgC;QAC9C;QAEA,IAAI,CAAC,gBAAgB,SAAS;YAC5B,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC,iEAAiE,aAAa,YAAY,IAAI,aAAa,IAAI;QAE3H,+BAA+B;QAC/B,IAAI,eAAe,aAAa,YAAY;QAC5C,IAAI,cAAc,aAAa,WAAW;QAC1C,IAAI,kBAAkB,aAAa,eAAe;QAElD,QAAQ,GAAG,CAAC,2BAA2B;YAAE;YAAc;YAAa;QAAgB;QAEpF,sEAAsE;QACtE,MAAM,kBAAkB,gBAAgB,aAAa,IAAI,OAAO,MAAM,iBAAiB;QACvF,MAAM,iBAAiB,eAAe,YAAY,IAAI,OAAO,MAAM,gBAAgB;QACnF,MAAM,qBAAqB,mBAAmB,gBAAgB,IAAI,OAAO,MAAM,oBAAoB;QAEnG,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,oBAAoB;YAC9D,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,sBAAsB;gBAAE;gBAAiB;gBAAgB;YAAmB;YAExF,MAAM,qBAAqB;YAC3B,IAAI,oBAAoB;gBACtB,QAAQ,GAAG,CAAC,iCAAiC;gBAC7C,eAAe,kBAAkB,eAAe,mBAAmB,YAAY;gBAC/E,cAAc,iBAAiB,cAAc,mBAAmB,WAAW;gBAC3E,kBAAkB,qBAAqB,kBAAkB,mBAAmB,eAAe;gBAC3F,QAAQ,GAAG,CAAC,6CAA6C;oBAAE;oBAAc;oBAAa;gBAAgB;YACxG,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QACF,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,MAAM,WAAgC,CAAC;QAEvC,sBAAsB;QACtB,IAAI,cAAc;YAChB,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD,EAAE;YAC5B,IAAI,YAAY;gBACd,QAAQ,CAAC,gBAAgB,GAAG;gBAC5B,QAAQ,CAAC,YAAY,GAAG;gBACxB,QAAQ,CAAC,SAAS,GAAG;gBACrB,QAAQ,GAAG,CAAC,6BAA6B,cAAc,KAAK;YAC9D;QACF,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,qBAAqB;QACrB,IAAI,aAAa;YACf,MAAM,YAAY,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD,EAAE;YAC3B,IAAI,WAAW;gBACb,QAAQ,CAAC,eAAe,GAAG;gBAC3B,QAAQ,CAAC,WAAW,GAAG;gBACvB,QAAQ,GAAG,CAAC,4BAA4B,aAAa,KAAK;YAC5D;QACF,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,yBAAyB;QACzB,IAAI,iBAAiB;YACnB,MAAM,gBAAgB,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD,EAAE;YAC/B,IAAI,eAAe;gBACjB,QAAQ,CAAC,mBAAmB,GAAG;gBAC/B,QAAQ,CAAC,eAAe,GAAG;gBAC3B,QAAQ,GAAG,CAAC,gCAAgC,iBAAiB,KAAK;YACpE;QACF,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,QAAQ,GAAG,CAAC,qBAAqB;QACjC,SAAS;IACX,GAAG;QAAC;QAAc;KAAQ;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;0BAAE;;;;;;;;;;;IAGT;IAEA,qBACE,8OAAC;QAEC,qBAAmB;QACnB,kBAAe;QACf,WAAU;QACV,OAAO;kBAEN;OANI;;;;;AASX", "debugId": null}}, {"offset": {"line": 7864, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/layout.tsx"], "sourcesContent": ["// src/app/layout.tsx\r\n\r\n\"use client\";\r\n\r\nimport './globals.css';\r\nimport type { Metadata } from 'next';\r\nimport { Toaster } from \"@/components/ui/toaster\"\r\nimport { SidebarProvider } from '@/components/ui/sidebar';\r\nimport { AppSidebar } from '@/components/layout/app-sidebar';\r\nimport { AuthWrapper } from '@/components/auth/auth-wrapper';\r\nimport { UnifiedBrandProvider } from '@/contexts/unified-brand-context';\r\nimport { BrandColorProvider } from '@/components/layout/brand-color-provider';\r\nimport React, { Suspense, useEffect, useState } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\n\r\n// Enable the client-side AppRoute overlay for faster perceived routing.\r\n// The AppRoute client component is lazy-loaded and will render client pages inside the layout.\r\nconst AppRouteClient = React.lazy(() => import('@/components/app-route/AppRoute').then(m => ({ default: m.default })));\r\n\r\n// Import test function for development\r\nif (typeof window !== 'undefined') {\r\n  import('@/lib/test-database');\r\n}\r\n\r\n\r\n\r\n\r\nfunction ConditionalLayout({ children, useAppRoute }: { children: React.ReactNode; useAppRoute?: boolean }) {\r\n  const pathname = usePathname();\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) {\r\n    return <div>Loading...</div>;\r\n  }\r\n\r\n  // Pages that should NOT show the sidebar (public pages only)\r\n  // Hide the sidebar for any route under /auth so auth pages render standalone\r\n  const shouldHideSidebar = pathname === '/' || (pathname ?? '').startsWith('/auth');\r\n\r\n  console.log('🔍 ConditionalLayout - pathname:', pathname, 'shouldHideSidebar:', shouldHideSidebar);\r\n\r\n  if (shouldHideSidebar) {\r\n    // For public/auth pages we keep the plain children rendering.\r\n    return <div className=\"w-full\">{children}</div>;\r\n  }\r\n\r\n\r\n  // Render the client AppRoute (lazy) instead of server-rendered children to make\r\n  // route transitions feel instantaneous. This mounts a client overlay that lazy-loads\r\n  // page components without a full server navigation.\r\n  return (\r\n    <SidebarProvider>\r\n      <AppSidebar />\r\n      <BrandColorProvider>\r\n        <Suspense fallback={<div className=\"p-6\">Loading...</div>}>\r\n          <AppRouteClient />\r\n        </Suspense>\r\n      </BrandColorProvider>\r\n    </SidebarProvider>\r\n  );\r\n\r\n  return (\r\n    <SidebarProvider>\r\n      <AppSidebar />\r\n      <BrandColorProvider>\r\n        {children}\r\n      </BrandColorProvider>\r\n    </SidebarProvider>\r\n  );\r\n}\r\n\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n\r\n  return (\r\n    <html lang=\"en\" suppressHydrationWarning>\r\n      <head>\r\n        <title>Crevo - AI-Powered Content Creation</title>\r\n        <meta name=\"description\" content=\"Transform your ideas into professional social media content with AI. Generate posts, designs, and campaigns that engage your audience and grow your brand.\" />\r\n      </head>\r\n  <body className=\"font-body antialiased overflow-x-hidden\" suppressHydrationWarning>\r\n        <AuthWrapper requireAuth={false}>\r\n          <UnifiedBrandProvider>\r\n            <ConditionalLayout useAppRoute={true}>\r\n              {children}\r\n            </ConditionalLayout>\r\n          </UnifiedBrandProvider>\r\n        </AuthWrapper>\r\n        <Toaster />\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,qBAAqB;;;;;AAMrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAaA,wEAAwE;AACxE,+FAA+F;AAC/F,MAAM,+BAAiB,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,IAAM,yIAA0C,IAAI,CAAC,CAAA,IAAK,CAAC;YAAE,SAAS,EAAE,OAAO;QAAC,CAAC;AAEnH,uCAAuC;AACvC,uCAAmC;;AAEnC;AAKA,SAAS,kBAAkB,EAAE,QAAQ,EAAE,WAAW,EAAwD;IACxG,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;sBAAI;;;;;;IACd;IAEA,6DAA6D;IAC7D,6EAA6E;IAC7E,MAAM,oBAAoB,aAAa,OAAO,CAAC,YAAY,EAAE,EAAE,UAAU,CAAC;IAE1E,QAAQ,GAAG,CAAC,oCAAoC,UAAU,sBAAsB;IAEhF,IAAI,mBAAmB;QACrB,8DAA8D;QAC9D,qBAAO,8OAAC;YAAI,WAAU;sBAAU;;;;;;IAClC;IAGA,gFAAgF;IAChF,qFAAqF;IACrF,oDAAoD;IACpD,qBACE,8OAAC,mIAAA,CAAA,kBAAe;;0BACd,8OAAC,8IAAA,CAAA,aAAU;;;;;0BACX,8OAAC,0JAAA,CAAA,qBAAkB;0BACjB,cAAA,8OAAC,qMAAA,CAAA,WAAQ;oBAAC,wBAAU,8OAAC;wBAAI,WAAU;kCAAM;;;;;;8BACvC,cAAA,8OAAC;;;;;;;;;;;;;;;;;;;;;;AAcX;AAGe,SAAS,WAAW,EACjC,QAAQ,EAGR;IAEA,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;;0BACtC,8OAAC;;kCACC,8OAAC;kCAAM;;;;;;kCACP,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;;;;;;0BAEvC,8OAAC;gBAAK,WAAU;gBAA0C,wBAAwB;;kCAC5E,8OAAC,6IAAA,CAAA,cAAW;wBAAC,aAAa;kCACxB,cAAA,8OAAC,+IAAA,CAAA,uBAAoB;sCACnB,cAAA,8OAAC;gCAAkB,aAAa;0CAC7B;;;;;;;;;;;;;;;;kCAIP,8OAAC,mIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;AAIhB", "debugId": null}}]}