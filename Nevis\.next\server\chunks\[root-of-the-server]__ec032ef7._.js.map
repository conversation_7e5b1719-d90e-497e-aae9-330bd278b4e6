{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/prompts/advanced-design-prompts.ts"], "sourcesContent": ["/**\r\n * Advanced Design Generation Prompts\r\n * \r\n * Professional-grade prompts incorporating design principles, composition rules,\r\n * typography best practices, color theory, and modern design trends.\r\n */\r\n\r\nexport const ADVANCED_DESIGN_PRINCIPLES = `\r\n**COMPOSITION & VISUAL HIERARCHY:**\r\n- Apply the Rule of Thirds: Position key elements along the grid lines or intersections\r\n- Create clear visual hierarchy using size, contrast, and positioning\r\n- Establish a strong focal point that draws the eye immediately\r\n- Use negative space strategically to create breathing room and emphasis\r\n- Balance elements using symmetrical or asymmetrical composition\r\n- Guide the viewer's eye through the design with leading lines and flow\r\n\r\n**TYPOGRAPHY EXCELLENCE:**\r\n- Establish clear typographic hierarchy (Primary headline, secondary text, body copy)\r\n- Use maximum 2-3 font families with strong contrast between them\r\n- Ensure text contrast ratio meets accessibility standards (4.5:1 minimum)\r\n- Apply proper letter spacing, line height, and text alignment\r\n- Scale typography appropriately for the platform and viewing distance\r\n- Use typography as a design element, not just information delivery\r\n\r\n**COLOR THEORY & HARMONY:**\r\n- Apply color psychology appropriate to the business type and message\r\n- Use complementary colors for high contrast and attention\r\n- Apply analogous colors for harmony and cohesion\r\n- Implement triadic color schemes for vibrant, balanced designs\r\n- Ensure sufficient contrast between text and background\r\n- Use the 60-30-10 rule: 60% dominant color, 30% secondary, 10% accent\r\n\r\n**MODERN DESIGN TRENDS:**\r\n- Embrace minimalism with purposeful use of white space\r\n- Use bold, geometric shapes and clean lines\r\n- Apply subtle gradients and depth effects when appropriate\r\n- Incorporate authentic, diverse photography when using people\r\n- Use consistent border radius and spacing throughout\r\n- Apply subtle shadows and depth for modern dimensionality\r\n`;\r\n\r\nexport const PLATFORM_SPECIFIC_GUIDELINES = {\r\n  instagram: `\r\n**INSTAGRAM OPTIMIZATION:**\r\n- Design for mobile-first viewing with bold, clear elements\r\n- Use high contrast colors that pop on small screens\r\n- Keep text large and readable (minimum 24px equivalent)\r\n- Center important elements for square crop compatibility\r\n- Use Instagram's native color palette trends\r\n- Design for both feed and story formats\r\n- Optimize for thumb-stopping power in fast scroll feeds\r\n- Logo placement: Bottom right corner or integrated naturally into design\r\n- Ensure logo is visible but doesn't overwhelm the main content\r\n`,\r\n\r\n  facebook: `\r\n**FACEBOOK OPTIMIZATION:**\r\n- Design for both desktop and mobile viewing\r\n- Use Facebook blue (#1877F2) strategically for CTAs\r\n- Optimize for news feed algorithm preferences\r\n- Include clear value proposition in visual hierarchy\r\n- Design for engagement and shareability\r\n- Use authentic, relatable imagery\r\n- Optimize for both organic and paid placement\r\n- Logo placement: Top left or bottom right corner for brand recognition\r\n- Ensure logo works well in both desktop and mobile formats\r\n`,\r\n\r\n  twitter: `\r\n**TWITTER/X OPTIMIZATION:**\r\n- Design for rapid consumption and high engagement\r\n- Use bold, contrasting colors that stand out in timeline\r\n- Keep text minimal and impactful\r\n- Design for retweet and quote tweet functionality\r\n- Use trending visual styles and memes appropriately\r\n- Optimize for both light and dark mode viewing\r\n- Create thumb-stopping visuals for fast-scrolling feeds\r\n- Logo placement: Small, subtle placement that doesn't interfere with content\r\n- Ensure logo is readable in both light and dark modes\r\n`,\r\n\r\n  linkedin: `\r\n**LINKEDIN OPTIMIZATION:**\r\n- Use professional, business-appropriate color schemes\r\n- Apply corporate design standards and clean aesthetics\r\n- Include clear value proposition for business audience\r\n- Use professional photography and imagery\r\n- Design for thought leadership and expertise positioning\r\n- Apply subtle, sophisticated design elements\r\n- Optimize for professional networking context\r\n- Logo placement: Prominent placement for brand authority and recognition\r\n- Ensure logo conveys professionalism and trustworthiness\r\n`\r\n};\r\n\r\nexport const BUSINESS_TYPE_DESIGN_DNA = {\r\n  restaurant: `\r\n**RESTAURANT DESIGN DNA:**\r\n- Use warm, appetizing colors (reds, oranges, warm yellows)\r\n- Include high-quality food photography with proper lighting\r\n- Apply rustic or modern clean aesthetics based on restaurant type\r\n- Use food-focused typography (script for upscale, bold sans for casual)\r\n- Include appetite-triggering visual elements\r\n- Apply golden hour lighting effects for food imagery\r\n- Use complementary colors that enhance food appeal\r\n- Show diverse people enjoying meals in authentic, social settings\r\n- Include cultural food elements that reflect local cuisine traditions\r\n- Display chefs, staff, and customers from the local community\r\n- Use table settings and dining environments that feel culturally authentic\r\n`,\r\n\r\n  fitness: `\r\n**FITNESS DESIGN DNA:**\r\n- Use energetic, motivational color schemes (bright blues, oranges, greens)\r\n- Include dynamic action shots and movement\r\n- Apply bold, strong typography with impact\r\n- Use high-contrast designs for motivation and energy\r\n- Include progress and achievement visual metaphors\r\n- Apply athletic and performance-focused imagery\r\n- Use inspiring and empowering visual language\r\n- Show diverse athletes and fitness enthusiasts in action\r\n- Include people of different body types, ages, and fitness levels\r\n- Display authentic workout environments and community settings\r\n- Use culturally relevant sports and fitness activities for the region\r\n`,\r\n\r\n  beauty: `\r\n**BEAUTY DESIGN DNA:**\r\n- Use sophisticated, elegant color palettes (pastels, metallics)\r\n- Include high-quality beauty photography with perfect lighting\r\n- Apply clean, minimalist aesthetics with luxury touches\r\n- Use elegant, refined typography\r\n- Include aspirational and transformational imagery\r\n- Apply soft, flattering lighting effects\r\n- Use premium and luxurious visual elements\r\n- Show diverse models representing different skin tones, ages, and beauty standards\r\n- Include authentic beauty routines and self-care moments\r\n- Display culturally relevant beauty practices and aesthetics\r\n- Use inclusive representation that celebrates natural beauty diversity\r\n`,\r\n\r\n  tech: `\r\n**TECH DESIGN DNA:**\r\n- Use modern, digital color schemes (blues, purples, teals)\r\n- Include clean, minimalist design with geometric elements\r\n- Apply futuristic and innovative visual metaphors\r\n- Use modern, sans-serif typography\r\n- Include data visualization and tech-focused imagery\r\n- Apply gradient overlays and digital effects\r\n- Use professional and cutting-edge visual language\r\n- Show diverse tech professionals in collaborative work environments\r\n- Include people using technology in natural, authentic ways\r\n- Display modern office spaces, remote work setups, and innovation hubs\r\n- Use culturally relevant technology adoption and usage patterns\r\n`,\r\n\r\n  ecommerce: `\r\n**E-COMMERCE DESIGN DNA:**\r\n- Use conversion-focused color schemes (trust blues, urgency reds, success greens)\r\n- Include high-quality product photography with lifestyle context\r\n- Apply clean, scannable layouts with clear hierarchy\r\n- Use action-oriented typography and compelling CTAs\r\n- Include social proof and trust signals\r\n- Apply mobile-first responsive design principles\r\n- Use persuasive and benefit-focused visual language\r\n- Show diverse customers using products in real-life situations\r\n- Include authentic unboxing and product experience moments\r\n- Display culturally relevant usage scenarios and lifestyle contexts\r\n`,\r\n\r\n  healthcare: `\r\n**HEALTHCARE DESIGN DNA:**\r\n- Use calming, trustworthy color palettes (soft blues, greens, whites)\r\n- Include professional medical imagery with human warmth\r\n- Apply clean, accessible design with clear information hierarchy\r\n- Use readable, professional typography\r\n- Include caring and compassionate visual elements\r\n- Apply medical accuracy with approachable aesthetics\r\n- Use reassuring and professional visual language\r\n- Show diverse healthcare professionals and patients\r\n- Include authentic care moments and medical environments\r\n- Display culturally sensitive healthcare interactions and settings\r\n`,\r\n\r\n  education: `\r\n**EDUCATION DESIGN DNA:**\r\n- Use inspiring, growth-focused color schemes (blues, greens, warm oranges)\r\n- Include diverse learning environments and educational moments\r\n- Apply organized, structured layouts with clear learning paths\r\n- Use friendly, accessible typography\r\n- Include knowledge and achievement visual metaphors\r\n- Apply bright, optimistic design elements\r\n- Use encouraging and empowering visual language\r\n- Show students and educators from diverse backgrounds\r\n- Include authentic classroom and learning environments\r\n- Display culturally relevant educational practices and settings\r\n`,\r\n\r\n  default: `\r\n**UNIVERSAL DESIGN DNA:**\r\n- Use brand-appropriate color psychology\r\n- Include authentic, high-quality imagery\r\n- Apply clean, professional aesthetics\r\n- Use readable, accessible typography\r\n- Include relevant industry visual metaphors\r\n- Apply consistent brand visual language\r\n- Use trustworthy and professional design elements\r\n- Show diverse people in authentic, relevant contexts\r\n- Include culturally appropriate imagery and design elements\r\n- Display real human connections and authentic moments\r\n`\r\n};\r\n\r\nexport const QUALITY_ENHANCEMENT_INSTRUCTIONS = `\r\n**DESIGN QUALITY STANDARDS:**\r\n- Ensure all text is perfectly readable with sufficient contrast\r\n- Apply consistent spacing and alignment throughout\r\n- Use high-resolution imagery without pixelation or artifacts\r\n- Maintain visual balance and proper proportions\r\n- Ensure brand elements are prominently but naturally integrated\r\n- Apply professional color grading and visual polish\r\n- Create designs that work across different screen sizes\r\n- Ensure accessibility compliance for color contrast and readability\r\n\r\n**TECHNICAL EXCELLENCE:**\r\n- Generate crisp, high-resolution images suitable for social media\r\n- Apply proper aspect ratios for platform requirements\r\n- Ensure text overlay is perfectly positioned and readable\r\n- Use consistent visual style throughout the design\r\n- Apply professional lighting and shadow effects\r\n- Ensure logo integration feels natural and branded\r\n- Create designs that maintain quality when compressed for social media\r\n`;\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;AAEM,MAAM,6BAA6B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgC3C,CAAC;AAEM,MAAM,+BAA+B;IAC1C,WAAW,CAAC;;;;;;;;;;;AAWd,CAAC;IAEC,UAAU,CAAC;;;;;;;;;;;AAWb,CAAC;IAEC,SAAS,CAAC;;;;;;;;;;;AAWZ,CAAC;IAEC,UAAU,CAAC;;;;;;;;;;;AAWb,CAAC;AACD;AAEO,MAAM,2BAA2B;IACtC,YAAY,CAAC;;;;;;;;;;;;;AAaf,CAAC;IAEC,SAAS,CAAC;;;;;;;;;;;;;AAaZ,CAAC;IAEC,QAAQ,CAAC;;;;;;;;;;;;;AAaX,CAAC;IAEC,MAAM,CAAC;;;;;;;;;;;;;AAaT,CAAC;IAEC,WAAW,CAAC;;;;;;;;;;;;AAYd,CAAC;IAEC,YAAY,CAAC;;;;;;;;;;;;AAYf,CAAC;IAEC,WAAW,CAAC;;;;;;;;;;;;AAYd,CAAC;IAEC,SAAS,CAAC;;;;;;;;;;;;AAYZ,CAAC;AACD;AAEO,MAAM,mCAAmC,CAAC;;;;;;;;;;;;;;;;;;;AAmBjD,CAAC", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/genkit.ts"], "sourcesContent": ["import { genkit } from 'genkit';\r\nimport { googleAI } from '@genkit-ai/googleai';\r\n\r\n// Get API key from environment variables\r\nconst apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY;\r\n\r\nif (!apiKey) {\r\n  console.error(\"❌ No Google AI API key found. Please set GEMINI_API_KEY, GOOGLE_API_KEY, or GOOGLE_GENAI_API_KEY environment variable.\");\r\n}\r\n\r\nexport const ai = genkit({\r\n  plugins: [googleAI({ apiKey })],\r\n  model: 'googleai/gemini-2.5-flash', // Using Gemini 2.5 Flash\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEA,yCAAyC;AACzC,MAAM,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,oBAAoB;AAE3G,IAAI,CAAC,QAAQ;IACX,QAAQ,KAAK,CAAC;AAChB;AAEO,MAAM,KAAK,CAAA,GAAA,yIAAA,CAAA,SAAM,AAAD,EAAE;IACvB,SAAS;QAAC,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE;YAAE;QAAO;KAAG;IAC/B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/design-trends.ts"], "sourcesContent": ["/**\r\n * Design Trends Integration System\r\n * \r\n * Keeps design generation current with latest visual trends and best practices\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\n\r\n// Schema for design trends analysis\r\nexport const DesignTrendsSchema = z.object({\r\n  currentTrends: z.array(z.object({\r\n    name: z.string().describe('Name of the design trend'),\r\n    description: z.string().describe('Description of the trend'),\r\n    applicability: z.enum(['high', 'medium', 'low']).describe('How applicable this trend is to the business type'),\r\n    implementation: z.string().describe('How to implement this trend in the design'),\r\n    examples: z.array(z.string()).describe('Visual examples or descriptions of the trend')\r\n  })).describe('Current relevant design trends'),\r\n  colorTrends: z.object({\r\n    palette: z.array(z.string()).describe('Trending color palette in hex format'),\r\n    mood: z.string().describe('Overall mood of trending colors'),\r\n    application: z.string().describe('How to apply these colors effectively')\r\n  }),\r\n  typographyTrends: z.object({\r\n    styles: z.array(z.string()).describe('Trending typography styles'),\r\n    pairings: z.array(z.string()).describe('Popular font pairings'),\r\n    treatments: z.array(z.string()).describe('Special text treatments and effects')\r\n  }),\r\n  layoutTrends: z.object({\r\n    compositions: z.array(z.string()).describe('Trending layout compositions'),\r\n    spacing: z.string().describe('Current spacing and whitespace trends'),\r\n    hierarchy: z.string().describe('Visual hierarchy trends')\r\n  }),\r\n  platformSpecific: z.object({\r\n    instagram: z.array(z.string()).describe('Instagram-specific design trends'),\r\n    facebook: z.array(z.string()).describe('Facebook-specific design trends'),\r\n    twitter: z.array(z.string()).describe('Twitter/X-specific design trends'),\r\n    linkedin: z.array(z.string()).describe('LinkedIn-specific design trends')\r\n  })\r\n});\r\n\r\nexport type DesignTrends = z.infer<typeof DesignTrendsSchema>;\r\n\r\n// Design trends analysis prompt\r\nconst designTrendsPrompt = ai.definePrompt({\r\n  name: 'analyzeDesignTrends',\r\n  input: {\r\n    schema: z.object({\r\n      businessType: z.string(),\r\n      platform: z.string(),\r\n      targetAudience: z.string().optional(),\r\n      industry: z.string().optional()\r\n    })\r\n  },\r\n  output: {\r\n    schema: DesignTrendsSchema\r\n  },\r\n  prompt: `You are a leading design trend analyst with deep knowledge of current visual design trends, social media best practices, and industry-specific design patterns.\r\n\r\nAnalyze and provide current design trends relevant to:\r\n- Business Type: {{businessType}}\r\n- Platform: {{platform}}\r\n- Target Audience: {{targetAudience}}\r\n- Industry: {{industry}}\r\n\r\nFocus on trends that are:\r\n1. Currently popular and effective (2024-2025)\r\n2. Relevant to the specific business type and platform\r\n3. Proven to drive engagement and conversions\r\n4. Accessible and implementable in AI-generated designs\r\n\r\nProvide specific, actionable trend insights that can be directly applied to design generation.`\r\n});\r\n\r\n/**\r\n * Gets current design trends relevant to the business and platform\r\n */\r\nexport async function getCurrentDesignTrends(\r\n  businessType: string,\r\n  platform: string,\r\n  targetAudience?: string,\r\n  industry?: string\r\n): Promise<DesignTrends> {\r\n  try {\r\n    // For now, return fallback trends to avoid API issues\r\n    // This provides current, relevant trends while the system is being tested\r\n    return getFallbackTrends(businessType, platform);\r\n  } catch (error) {\r\n    console.error('Design trends analysis failed:', error);\r\n    // Return fallback trends\r\n    return getFallbackTrends(businessType, platform);\r\n  }\r\n}\r\n\r\n/**\r\n * Generates trend-aware design instructions\r\n */\r\nexport function generateTrendInstructions(trends: DesignTrends, platform: string): string {\r\n  const platformTrends = trends.platformSpecific[platform as keyof typeof trends.platformSpecific] || [];\r\n  const highApplicabilityTrends = trends.currentTrends.filter(t => t.applicability === 'high');\r\n\r\n  return `\r\n**CURRENT DESIGN TRENDS INTEGRATION:**\r\n\r\n**High-Priority Trends to Incorporate:**\r\n${highApplicabilityTrends.map(trend => `\r\n- **${trend.name}**: ${trend.description}\r\n  Implementation: ${trend.implementation}`).join('\\n')}\r\n\r\n**Color Trends:**\r\n- Trending Palette: ${trends.colorTrends.palette.join(', ')}\r\n- Mood: ${trends.colorTrends.mood}\r\n- Application: ${trends.colorTrends.application}\r\n\r\n**Typography Trends:**\r\n- Styles: ${trends.typographyTrends.styles.join(', ')}\r\n- Popular Pairings: ${trends.typographyTrends.pairings.join(', ')}\r\n- Special Treatments: ${trends.typographyTrends.treatments.join(', ')}\r\n\r\n**Layout Trends:**\r\n- Compositions: ${trends.layoutTrends.compositions.join(', ')}\r\n- Spacing: ${trends.layoutTrends.spacing}\r\n- Hierarchy: ${trends.layoutTrends.hierarchy}\r\n\r\n**Platform-Specific Trends (${platform}):**\r\n${platformTrends.map(trend => `- ${trend}`).join('\\n')}\r\n\r\n**TREND APPLICATION GUIDELINES:**\r\n- Incorporate 2-3 relevant trends maximum to avoid overwhelming the design\r\n- Ensure trends align with brand personality and business goals\r\n- Prioritize trends that enhance readability and user experience\r\n- Balance trendy elements with timeless design principles\r\n`;\r\n}\r\n\r\n/**\r\n * Fallback trends when API fails\r\n */\r\nfunction getFallbackTrends(businessType: string, platform: string): DesignTrends {\r\n  return {\r\n    currentTrends: [\r\n      {\r\n        name: \"Bold Typography\",\r\n        description: \"Large, impactful typography that commands attention\",\r\n        applicability: \"high\",\r\n        implementation: \"Use oversized headlines with strong contrast\",\r\n        examples: [\"Large sans-serif headers\", \"Bold statement text\", \"Typography as hero element\"]\r\n      },\r\n      {\r\n        name: \"Minimalist Design\",\r\n        description: \"Clean, uncluttered designs with plenty of white space\",\r\n        applicability: \"high\",\r\n        implementation: \"Focus on essential elements, generous spacing, simple color palette\",\r\n        examples: [\"Clean layouts\", \"Minimal color schemes\", \"Focused messaging\"]\r\n      },\r\n      {\r\n        name: \"Authentic Photography\",\r\n        description: \"Real, unposed photography over stock imagery\",\r\n        applicability: \"medium\",\r\n        implementation: \"Use candid, lifestyle photography that feels genuine\",\r\n        examples: [\"Behind-the-scenes shots\", \"Real customer photos\", \"Lifestyle imagery\"]\r\n      }\r\n    ],\r\n    colorTrends: {\r\n      palette: [\"#FF6B6B\", \"#4ECDC4\", \"#45B7D1\", \"#96CEB4\", \"#FFEAA7\"],\r\n      mood: \"Vibrant yet calming, optimistic and approachable\",\r\n      application: \"Use as accent colors against neutral backgrounds for maximum impact\"\r\n    },\r\n    typographyTrends: {\r\n      styles: [\"Bold sans-serif\", \"Modern serif\", \"Custom lettering\"],\r\n      pairings: [\"Bold header + clean body\", \"Serif headline + sans-serif body\"],\r\n      treatments: [\"Gradient text\", \"Outlined text\", \"Text with shadows\"]\r\n    },\r\n    layoutTrends: {\r\n      compositions: [\"Asymmetrical balance\", \"Grid-based layouts\", \"Centered focal points\"],\r\n      spacing: \"Generous white space with intentional breathing room\",\r\n      hierarchy: \"Clear size differentiation with strong contrast\"\r\n    },\r\n    platformSpecific: {\r\n      instagram: [\"Square and vertical formats\", \"Story-friendly designs\", \"Carousel-optimized layouts\"],\r\n      facebook: [\"Horizontal emphasis\", \"Video-first approach\", \"Community-focused messaging\"],\r\n      twitter: [\"High contrast for timeline\", \"Text-heavy designs\", \"Trending hashtag integration\"],\r\n      linkedin: [\"Professional aesthetics\", \"Data visualization\", \"Thought leadership focus\"]\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Caches trends to avoid excessive API calls\r\n * Reduced cache duration and added randomization to prevent repetitive designs\r\n */\r\nconst trendsCache = new Map<string, { trends: DesignTrends; timestamp: number; usageCount: number }>();\r\nconst CACHE_DURATION = 6 * 60 * 60 * 1000; // 6 hours (reduced from 24 hours)\r\nconst MAX_USAGE_COUNT = 5; // Force refresh after 5 uses to add variety\r\n\r\nexport async function getCachedDesignTrends(\r\n  businessType: string,\r\n  platform: string,\r\n  targetAudience?: string,\r\n  industry?: string\r\n): Promise<DesignTrends> {\r\n  // Add randomization to cache key to create more variety\r\n  const hourOfDay = new Date().getHours();\r\n  const randomSeed = Math.floor(hourOfDay / 2); // Changes every 2 hours\r\n  const cacheKey = `${businessType}-${platform}-${targetAudience}-${industry}-${randomSeed}`;\r\n  const cached = trendsCache.get(cacheKey);\r\n\r\n  // Check if cache is valid and not overused\r\n  if (cached &&\r\n    Date.now() - cached.timestamp < CACHE_DURATION &&\r\n    cached.usageCount < MAX_USAGE_COUNT) {\r\n    cached.usageCount++;\r\n    return cached.trends;\r\n  }\r\n\r\n  const trends = await getCurrentDesignTrends(businessType, platform, targetAudience, industry);\r\n  trendsCache.set(cacheKey, { trends, timestamp: Date.now(), usageCount: 1 });\r\n\r\n  return trends;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;AAED;AACA;;;AAGO,MAAM,qBAAqB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,eAAe,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC9B,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC1B,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACjC,eAAe,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;SAAM,EAAE,QAAQ,CAAC;QAC1D,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACpC,UAAU,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IACzC,IAAI,QAAQ,CAAC;IACb,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,SAAS,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACtC,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC1B,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACnC;IACA,kBAAkB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,QAAQ,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACrC,UAAU,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACvC,YAAY,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC3C;IACA,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QAC3C,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC;IACA,kBAAkB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,WAAW,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACxC,UAAU,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACvC,SAAS,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACtC,UAAU,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IACzC;AACF;AAIA,gCAAgC;AAChC,MAAM,qBAAqB,qHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IACzC,MAAM;IACN,OAAO;QACL,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM;YACtB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACnC,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC/B;IACF;IACA,QAAQ;QACN,QAAQ;IACV;IACA,QAAQ,CAAC;;;;;;;;;;;;;;8FAcmF,CAAC;AAC/F;AAKO,eAAe,uBACpB,YAAoB,EACpB,QAAgB,EAChB,cAAuB,EACvB,QAAiB;IAEjB,IAAI;QACF,sDAAsD;QACtD,0EAA0E;QAC1E,OAAO,kBAAkB,cAAc;IACzC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,yBAAyB;QACzB,OAAO,kBAAkB,cAAc;IACzC;AACF;AAKO,SAAS,0BAA0B,MAAoB,EAAE,QAAgB;IAC9E,MAAM,iBAAiB,OAAO,gBAAgB,CAAC,SAAiD,IAAI,EAAE;IACtG,MAAM,0BAA0B,OAAO,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;IAErF,OAAO,CAAC;;;;AAIV,EAAE,wBAAwB,GAAG,CAAC,CAAA,QAAS,CAAC;IACpC,EAAE,MAAM,IAAI,CAAC,IAAI,EAAE,MAAM,WAAW,CAAC;kBACvB,EAAE,MAAM,cAAc,EAAE,EAAE,IAAI,CAAC,MAAM;;;oBAGnC,EAAE,OAAO,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM;QACpD,EAAE,OAAO,WAAW,CAAC,IAAI,CAAC;eACnB,EAAE,OAAO,WAAW,CAAC,WAAW,CAAC;;;UAGtC,EAAE,OAAO,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM;oBAClC,EAAE,OAAO,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM;sBAC5C,EAAE,OAAO,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM;;;gBAGtD,EAAE,OAAO,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM;WACnD,EAAE,OAAO,YAAY,CAAC,OAAO,CAAC;aAC5B,EAAE,OAAO,YAAY,CAAC,SAAS,CAAC;;4BAEjB,EAAE,SAAS;AACvC,EAAE,eAAe,GAAG,CAAC,CAAA,QAAS,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM;;;;;;;AAOvD,CAAC;AACD;AAEA;;CAEC,GACD,SAAS,kBAAkB,YAAoB,EAAE,QAAgB;IAC/D,OAAO;QACL,eAAe;YACb;gBACE,MAAM;gBACN,aAAa;gBACb,eAAe;gBACf,gBAAgB;gBAChB,UAAU;oBAAC;oBAA4B;oBAAuB;iBAA6B;YAC7F;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,eAAe;gBACf,gBAAgB;gBAChB,UAAU;oBAAC;oBAAiB;oBAAyB;iBAAoB;YAC3E;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,eAAe;gBACf,gBAAgB;gBAChB,UAAU;oBAAC;oBAA2B;oBAAwB;iBAAoB;YACpF;SACD;QACD,aAAa;YACX,SAAS;gBAAC;gBAAW;gBAAW;gBAAW;gBAAW;aAAU;YAChE,MAAM;YACN,aAAa;QACf;QACA,kBAAkB;YAChB,QAAQ;gBAAC;gBAAmB;gBAAgB;aAAmB;YAC/D,UAAU;gBAAC;gBAA4B;aAAmC;YAC1E,YAAY;gBAAC;gBAAiB;gBAAiB;aAAoB;QACrE;QACA,cAAc;YACZ,cAAc;gBAAC;gBAAwB;gBAAsB;aAAwB;YACrF,SAAS;YACT,WAAW;QACb;QACA,kBAAkB;YAChB,WAAW;gBAAC;gBAA+B;gBAA0B;aAA6B;YAClG,UAAU;gBAAC;gBAAuB;gBAAwB;aAA8B;YACxF,SAAS;gBAAC;gBAA8B;gBAAsB;aAA+B;YAC7F,UAAU;gBAAC;gBAA2B;gBAAsB;aAA2B;QACzF;IACF;AACF;AAEA;;;CAGC,GACD,MAAM,cAAc,IAAI;AACxB,MAAM,iBAAiB,IAAI,KAAK,KAAK,MAAM,kCAAkC;AAC7E,MAAM,kBAAkB,GAAG,4CAA4C;AAEhE,eAAe,sBACpB,YAAoB,EACpB,QAAgB,EAChB,cAAuB,EACvB,QAAiB;IAEjB,wDAAwD;IACxD,MAAM,YAAY,IAAI,OAAO,QAAQ;IACrC,MAAM,aAAa,KAAK,KAAK,CAAC,YAAY,IAAI,wBAAwB;IACtE,MAAM,WAAW,GAAG,aAAa,CAAC,EAAE,SAAS,CAAC,EAAE,eAAe,CAAC,EAAE,SAAS,CAAC,EAAE,YAAY;IAC1F,MAAM,SAAS,YAAY,GAAG,CAAC;IAE/B,2CAA2C;IAC3C,IAAI,UACF,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,kBAChC,OAAO,UAAU,GAAG,iBAAiB;QACrC,OAAO,UAAU;QACjB,OAAO,OAAO,MAAM;IACtB;IAEA,MAAM,SAAS,MAAM,uBAAuB,cAAc,UAAU,gBAAgB;IACpF,YAAY,GAAG,CAAC,UAAU;QAAE;QAAQ,WAAW,KAAK,GAAG;QAAI,YAAY;IAAE;IAEzE,OAAO;AACT", "debugId": null}}, {"offset": {"line": 789, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/design-analytics.ts"], "sourcesContent": ["/**\r\n * Design Performance Analytics System\r\n * \r\n * Tracks design performance, learns from successful patterns, and optimizes future generations\r\n */\r\n\r\nimport { z } from 'zod';\r\n\r\n// Schema for design performance metrics\r\nexport const DesignPerformanceSchema = z.object({\r\n  designId: z.string(),\r\n  businessType: z.string(),\r\n  platform: z.string(),\r\n  visualStyle: z.string(),\r\n  generatedAt: z.date(),\r\n  metrics: z.object({\r\n    qualityScore: z.number().min(1).max(10),\r\n    engagementPrediction: z.number().min(1).max(10),\r\n    brandAlignmentScore: z.number().min(1).max(10),\r\n    technicalQuality: z.number().min(1).max(10),\r\n    trendRelevance: z.number().min(1).max(10)\r\n  }),\r\n  designElements: z.object({\r\n    colorPalette: z.array(z.string()),\r\n    typography: z.string(),\r\n    composition: z.string(),\r\n    trends: z.array(z.string()),\r\n    businessDNA: z.string()\r\n  }),\r\n  performance: z.object({\r\n    actualEngagement: z.number().optional(),\r\n    clickThroughRate: z.number().optional(),\r\n    conversionRate: z.number().optional(),\r\n    brandRecall: z.number().optional(),\r\n    userFeedback: z.number().min(1).max(5).optional()\r\n  }).optional(),\r\n  improvements: z.array(z.string()).optional(),\r\n  tags: z.array(z.string()).optional()\r\n});\r\n\r\nexport type DesignPerformance = z.infer<typeof DesignPerformanceSchema>;\r\n\r\n// In-memory storage for design analytics (in production, use a database)\r\nconst designAnalytics: Map<string, DesignPerformance> = new Map();\r\nconst performancePatterns: Map<string, any> = new Map();\r\n\r\n/**\r\n * Records design generation and initial metrics\r\n */\r\nexport function recordDesignGeneration(\r\n  designId: string,\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  qualityScore: number,\r\n  designElements: {\r\n    colorPalette: string[];\r\n    typography: string;\r\n    composition: string;\r\n    trends: string[];\r\n    businessDNA: string;\r\n  },\r\n  predictions: {\r\n    engagement: number;\r\n    brandAlignment: number;\r\n    technicalQuality: number;\r\n    trendRelevance: number;\r\n  }\r\n): void {\r\n  const record: DesignPerformance = {\r\n    designId,\r\n    businessType,\r\n    platform,\r\n    visualStyle,\r\n    generatedAt: new Date(),\r\n    metrics: {\r\n      qualityScore,\r\n      engagementPrediction: predictions.engagement,\r\n      brandAlignmentScore: predictions.brandAlignment,\r\n      technicalQuality: predictions.technicalQuality,\r\n      trendRelevance: predictions.trendRelevance\r\n    },\r\n    designElements,\r\n    tags: [businessType, platform, visualStyle]\r\n  };\r\n\r\n  designAnalytics.set(designId, record);\r\n  updatePerformancePatterns(record);\r\n}\r\n\r\n/**\r\n * Updates design performance with actual metrics\r\n */\r\nexport function updateDesignPerformance(\r\n  designId: string,\r\n  actualMetrics: {\r\n    engagement?: number;\r\n    clickThroughRate?: number;\r\n    conversionRate?: number;\r\n    brandRecall?: number;\r\n    userFeedback?: number;\r\n  }\r\n): void {\r\n  const record = designAnalytics.get(designId);\r\n  if (!record) return;\r\n\r\n  record.performance = {\r\n    ...record.performance,\r\n    ...actualMetrics\r\n  };\r\n\r\n  designAnalytics.set(designId, record);\r\n  updatePerformancePatterns(record);\r\n}\r\n\r\n/**\r\n * Analyzes performance patterns to improve future designs\r\n */\r\nfunction updatePerformancePatterns(record: DesignPerformance): void {\r\n  const patternKey = `${record.businessType}-${record.platform}-${record.visualStyle}`;\r\n  \r\n  if (!performancePatterns.has(patternKey)) {\r\n    performancePatterns.set(patternKey, {\r\n      count: 0,\r\n      avgQuality: 0,\r\n      avgEngagement: 0,\r\n      successfulElements: new Map(),\r\n      commonIssues: new Map(),\r\n      bestPractices: []\r\n    });\r\n  }\r\n\r\n  const pattern = performancePatterns.get(patternKey);\r\n  pattern.count += 1;\r\n  \r\n  // Update averages\r\n  pattern.avgQuality = (pattern.avgQuality * (pattern.count - 1) + record.metrics.qualityScore) / pattern.count;\r\n  pattern.avgEngagement = (pattern.avgEngagement * (pattern.count - 1) + record.metrics.engagementPrediction) / pattern.count;\r\n\r\n  // Track successful elements\r\n  if (record.metrics.qualityScore >= 8) {\r\n    record.designElements.trends.forEach(trend => {\r\n      const count = pattern.successfulElements.get(trend) || 0;\r\n      pattern.successfulElements.set(trend, count + 1);\r\n    });\r\n  }\r\n\r\n  // Track common issues\r\n  if (record.improvements) {\r\n    record.improvements.forEach(issue => {\r\n      const count = pattern.commonIssues.get(issue) || 0;\r\n      pattern.commonIssues.set(issue, count + 1);\r\n    });\r\n  }\r\n\r\n  performancePatterns.set(patternKey, pattern);\r\n}\r\n\r\n/**\r\n * Gets performance insights for a specific business/platform combination\r\n */\r\nexport function getPerformanceInsights(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle?: string\r\n): {\r\n  averageQuality: number;\r\n  averageEngagement: number;\r\n  topSuccessfulElements: string[];\r\n  commonIssues: string[];\r\n  recommendations: string[];\r\n  sampleSize: number;\r\n} {\r\n  const patternKey = visualStyle \r\n    ? `${businessType}-${platform}-${visualStyle}`\r\n    : `${businessType}-${platform}`;\r\n  \r\n  const pattern = performancePatterns.get(patternKey);\r\n  \r\n  if (!pattern) {\r\n    return {\r\n      averageQuality: 0,\r\n      averageEngagement: 0,\r\n      topSuccessfulElements: [],\r\n      commonIssues: [],\r\n      recommendations: ['Insufficient data for insights'],\r\n      sampleSize: 0\r\n    };\r\n  }\r\n\r\n  // Get top successful elements\r\n  const topElements = Array.from(pattern.successfulElements.entries())\r\n    .sort((a, b) => b[1] - a[1])\r\n    .slice(0, 5)\r\n    .map(([element]) => element);\r\n\r\n  // Get common issues\r\n  const topIssues = Array.from(pattern.commonIssues.entries())\r\n    .sort((a, b) => b[1] - a[1])\r\n    .slice(0, 3)\r\n    .map(([issue]) => issue);\r\n\r\n  // Generate recommendations\r\n  const recommendations = generateRecommendations(pattern, topElements, topIssues);\r\n\r\n  return {\r\n    averageQuality: Math.round(pattern.avgQuality * 10) / 10,\r\n    averageEngagement: Math.round(pattern.avgEngagement * 10) / 10,\r\n    topSuccessfulElements: topElements,\r\n    commonIssues: topIssues,\r\n    recommendations,\r\n    sampleSize: pattern.count\r\n  };\r\n}\r\n\r\n/**\r\n * Generates actionable recommendations based on performance data\r\n */\r\nfunction generateRecommendations(\r\n  pattern: any,\r\n  successfulElements: string[],\r\n  commonIssues: string[]\r\n): string[] {\r\n  const recommendations: string[] = [];\r\n\r\n  // Quality-based recommendations\r\n  if (pattern.avgQuality < 7) {\r\n    recommendations.push('Focus on improving overall design quality through better composition and typography');\r\n  }\r\n\r\n  // Engagement-based recommendations\r\n  if (pattern.avgEngagement < 7) {\r\n    recommendations.push('Incorporate more attention-grabbing elements and bold visual choices');\r\n  }\r\n\r\n  // Element-based recommendations\r\n  if (successfulElements.length > 0) {\r\n    recommendations.push(`Continue using successful elements: ${successfulElements.slice(0, 3).join(', ')}`);\r\n  }\r\n\r\n  // Issue-based recommendations\r\n  if (commonIssues.length > 0) {\r\n    recommendations.push(`Address common issues: ${commonIssues.slice(0, 2).join(', ')}`);\r\n  }\r\n\r\n  // Sample size recommendations\r\n  if (pattern.count < 10) {\r\n    recommendations.push('Generate more designs to improve insights accuracy');\r\n  }\r\n\r\n  return recommendations;\r\n}\r\n\r\n/**\r\n * Gets top performing designs for learning\r\n */\r\nexport function getTopPerformingDesigns(\r\n  businessType?: string,\r\n  platform?: string,\r\n  limit: number = 10\r\n): DesignPerformance[] {\r\n  let designs = Array.from(designAnalytics.values());\r\n\r\n  // Filter by business type and platform if specified\r\n  if (businessType) {\r\n    designs = designs.filter(d => d.businessType === businessType);\r\n  }\r\n  if (platform) {\r\n    designs = designs.filter(d => d.platform === platform);\r\n  }\r\n\r\n  // Sort by quality score and engagement prediction\r\n  designs.sort((a, b) => {\r\n    const scoreA = (a.metrics.qualityScore + a.metrics.engagementPrediction) / 2;\r\n    const scoreB = (b.metrics.qualityScore + b.metrics.engagementPrediction) / 2;\r\n    return scoreB - scoreA;\r\n  });\r\n\r\n  return designs.slice(0, limit);\r\n}\r\n\r\n/**\r\n * Generates performance-optimized design instructions\r\n */\r\nexport function generatePerformanceOptimizedInstructions(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string\r\n): string {\r\n  const insights = getPerformanceInsights(businessType, platform, visualStyle);\r\n  \r\n  if (insights.sampleSize === 0) {\r\n    return ''; // No data available\r\n  }\r\n\r\n  let instructions = `\\n**PERFORMANCE-OPTIMIZED DESIGN INSTRUCTIONS:**\\n`;\r\n  \r\n  if (insights.topSuccessfulElements.length > 0) {\r\n    instructions += `**Proven Successful Elements (${insights.sampleSize} designs analyzed):**\\n`;\r\n    insights.topSuccessfulElements.forEach(element => {\r\n      instructions += `- Incorporate: ${element}\\n`;\r\n    });\r\n  }\r\n\r\n  if (insights.commonIssues.length > 0) {\r\n    instructions += `\\n**Avoid Common Issues:**\\n`;\r\n    insights.commonIssues.forEach(issue => {\r\n      instructions += `- Prevent: ${issue}\\n`;\r\n    });\r\n  }\r\n\r\n  if (insights.recommendations.length > 0) {\r\n    instructions += `\\n**Performance Recommendations:**\\n`;\r\n    insights.recommendations.forEach(rec => {\r\n      instructions += `- ${rec}\\n`;\r\n    });\r\n  }\r\n\r\n  instructions += `\\n**Performance Benchmarks:**\\n`;\r\n  instructions += `- Target Quality Score: ${Math.max(insights.averageQuality + 0.5, 8)}/10\\n`;\r\n  instructions += `- Target Engagement: ${Math.max(insights.averageEngagement + 0.5, 8)}/10\\n`;\r\n\r\n  return instructions;\r\n}\r\n\r\n/**\r\n * Exports analytics data for external analysis\r\n */\r\nexport function exportAnalyticsData(): {\r\n  designs: DesignPerformance[];\r\n  patterns: Array<{ key: string; data: any }>;\r\n  summary: {\r\n    totalDesigns: number;\r\n    averageQuality: number;\r\n    topBusinessTypes: string[];\r\n    topPlatforms: string[];\r\n  };\r\n} {\r\n  const designs = Array.from(designAnalytics.values());\r\n  const patterns = Array.from(performancePatterns.entries()).map(([key, data]) => ({ key, data }));\r\n\r\n  // Calculate summary statistics\r\n  const totalDesigns = designs.length;\r\n  const averageQuality = designs.reduce((sum, d) => sum + d.metrics.qualityScore, 0) / totalDesigns;\r\n  \r\n  const businessTypeCounts = designs.reduce((acc, d) => {\r\n    acc[d.businessType] = (acc[d.businessType] || 0) + 1;\r\n    return acc;\r\n  }, {} as Record<string, number>);\r\n  \r\n  const platformCounts = designs.reduce((acc, d) => {\r\n    acc[d.platform] = (acc[d.platform] || 0) + 1;\r\n    return acc;\r\n  }, {} as Record<string, number>);\r\n\r\n  const topBusinessTypes = Object.entries(businessTypeCounts)\r\n    .sort((a, b) => b[1] - a[1])\r\n    .slice(0, 5)\r\n    .map(([type]) => type);\r\n\r\n  const topPlatforms = Object.entries(platformCounts)\r\n    .sort((a, b) => b[1] - a[1])\r\n    .slice(0, 5)\r\n    .map(([platform]) => platform);\r\n\r\n  return {\r\n    designs,\r\n    patterns,\r\n    summary: {\r\n      totalDesigns,\r\n      averageQuality: Math.round(averageQuality * 10) / 10,\r\n      topBusinessTypes,\r\n      topPlatforms\r\n    }\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;AAED;;AAGO,MAAM,0BAA0B,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;IAClB,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM;IACtB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;IAClB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;IACrB,aAAa,sIAAA,CAAA,IAAC,CAAC,IAAI;IACnB,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACpC,sBAAsB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAC5C,qBAAqB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAC3C,kBAAkB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACxC,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACxC;IACA,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACvB,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM;QAC9B,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM;QACpB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;QACrB,QAAQ,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM;QACxB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;IACvB;IACA,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,kBAAkB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC,kBAAkB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAChC,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;IACjD,GAAG,QAAQ;IACX,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IAC1C,MAAM,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;AACpC;AAIA,yEAAyE;AACzE,MAAM,kBAAkD,IAAI;AAC5D,MAAM,sBAAwC,IAAI;AAK3C,SAAS,uBACd,QAAgB,EAChB,YAAoB,EACpB,QAAgB,EAChB,WAAmB,EACnB,YAAoB,EACpB,cAMC,EACD,WAKC;IAED,MAAM,SAA4B;QAChC;QACA;QACA;QACA;QACA,aAAa,IAAI;QACjB,SAAS;YACP;YACA,sBAAsB,YAAY,UAAU;YAC5C,qBAAqB,YAAY,cAAc;YAC/C,kBAAkB,YAAY,gBAAgB;YAC9C,gBAAgB,YAAY,cAAc;QAC5C;QACA;QACA,MAAM;YAAC;YAAc;YAAU;SAAY;IAC7C;IAEA,gBAAgB,GAAG,CAAC,UAAU;IAC9B,0BAA0B;AAC5B;AAKO,SAAS,wBACd,QAAgB,EAChB,aAMC;IAED,MAAM,SAAS,gBAAgB,GAAG,CAAC;IACnC,IAAI,CAAC,QAAQ;IAEb,OAAO,WAAW,GAAG;QACnB,GAAG,OAAO,WAAW;QACrB,GAAG,aAAa;IAClB;IAEA,gBAAgB,GAAG,CAAC,UAAU;IAC9B,0BAA0B;AAC5B;AAEA;;CAEC,GACD,SAAS,0BAA0B,MAAyB;IAC1D,MAAM,aAAa,GAAG,OAAO,YAAY,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,CAAC,EAAE,OAAO,WAAW,EAAE;IAEpF,IAAI,CAAC,oBAAoB,GAAG,CAAC,aAAa;QACxC,oBAAoB,GAAG,CAAC,YAAY;YAClC,OAAO;YACP,YAAY;YACZ,eAAe;YACf,oBAAoB,IAAI;YACxB,cAAc,IAAI;YAClB,eAAe,EAAE;QACnB;IACF;IAEA,MAAM,UAAU,oBAAoB,GAAG,CAAC;IACxC,QAAQ,KAAK,IAAI;IAEjB,kBAAkB;IAClB,QAAQ,UAAU,GAAG,CAAC,QAAQ,UAAU,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,OAAO,OAAO,CAAC,YAAY,IAAI,QAAQ,KAAK;IAC7G,QAAQ,aAAa,GAAG,CAAC,QAAQ,aAAa,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,OAAO,OAAO,CAAC,oBAAoB,IAAI,QAAQ,KAAK;IAE3H,4BAA4B;IAC5B,IAAI,OAAO,OAAO,CAAC,YAAY,IAAI,GAAG;QACpC,OAAO,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YACnC,MAAM,QAAQ,QAAQ,kBAAkB,CAAC,GAAG,CAAC,UAAU;YACvD,QAAQ,kBAAkB,CAAC,GAAG,CAAC,OAAO,QAAQ;QAChD;IACF;IAEA,sBAAsB;IACtB,IAAI,OAAO,YAAY,EAAE;QACvB,OAAO,YAAY,CAAC,OAAO,CAAC,CAAA;YAC1B,MAAM,QAAQ,QAAQ,YAAY,CAAC,GAAG,CAAC,UAAU;YACjD,QAAQ,YAAY,CAAC,GAAG,CAAC,OAAO,QAAQ;QAC1C;IACF;IAEA,oBAAoB,GAAG,CAAC,YAAY;AACtC;AAKO,SAAS,uBACd,YAAoB,EACpB,QAAgB,EAChB,WAAoB;IASpB,MAAM,aAAa,cACf,GAAG,aAAa,CAAC,EAAE,SAAS,CAAC,EAAE,aAAa,GAC5C,GAAG,aAAa,CAAC,EAAE,UAAU;IAEjC,MAAM,UAAU,oBAAoB,GAAG,CAAC;IAExC,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,gBAAgB;YAChB,mBAAmB;YACnB,uBAAuB,EAAE;YACzB,cAAc,EAAE;YAChB,iBAAiB;gBAAC;aAAiC;YACnD,YAAY;QACd;IACF;IAEA,8BAA8B;IAC9B,MAAM,cAAc,MAAM,IAAI,CAAC,QAAQ,kBAAkB,CAAC,OAAO,IAC9D,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAK;IAEtB,oBAAoB;IACpB,MAAM,YAAY,MAAM,IAAI,CAAC,QAAQ,YAAY,CAAC,OAAO,IACtD,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,MAAM,GAAK;IAEpB,2BAA2B;IAC3B,MAAM,kBAAkB,wBAAwB,SAAS,aAAa;IAEtE,OAAO;QACL,gBAAgB,KAAK,KAAK,CAAC,QAAQ,UAAU,GAAG,MAAM;QACtD,mBAAmB,KAAK,KAAK,CAAC,QAAQ,aAAa,GAAG,MAAM;QAC5D,uBAAuB;QACvB,cAAc;QACd;QACA,YAAY,QAAQ,KAAK;IAC3B;AACF;AAEA;;CAEC,GACD,SAAS,wBACP,OAAY,EACZ,kBAA4B,EAC5B,YAAsB;IAEtB,MAAM,kBAA4B,EAAE;IAEpC,gCAAgC;IAChC,IAAI,QAAQ,UAAU,GAAG,GAAG;QAC1B,gBAAgB,IAAI,CAAC;IACvB;IAEA,mCAAmC;IACnC,IAAI,QAAQ,aAAa,GAAG,GAAG;QAC7B,gBAAgB,IAAI,CAAC;IACvB;IAEA,gCAAgC;IAChC,IAAI,mBAAmB,MAAM,GAAG,GAAG;QACjC,gBAAgB,IAAI,CAAC,CAAC,oCAAoC,EAAE,mBAAmB,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;IACzG;IAEA,8BAA8B;IAC9B,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,gBAAgB,IAAI,CAAC,CAAC,uBAAuB,EAAE,aAAa,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;IACtF;IAEA,8BAA8B;IAC9B,IAAI,QAAQ,KAAK,GAAG,IAAI;QACtB,gBAAgB,IAAI,CAAC;IACvB;IAEA,OAAO;AACT;AAKO,SAAS,wBACd,YAAqB,EACrB,QAAiB,EACjB,QAAgB,EAAE;IAElB,IAAI,UAAU,MAAM,IAAI,CAAC,gBAAgB,MAAM;IAE/C,oDAAoD;IACpD,IAAI,cAAc;QAChB,UAAU,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK;IACnD;IACA,IAAI,UAAU;QACZ,UAAU,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAC/C;IAEA,kDAAkD;IAClD,QAAQ,IAAI,CAAC,CAAC,GAAG;QACf,MAAM,SAAS,CAAC,EAAE,OAAO,CAAC,YAAY,GAAG,EAAE,OAAO,CAAC,oBAAoB,IAAI;QAC3E,MAAM,SAAS,CAAC,EAAE,OAAO,CAAC,YAAY,GAAG,EAAE,OAAO,CAAC,oBAAoB,IAAI;QAC3E,OAAO,SAAS;IAClB;IAEA,OAAO,QAAQ,KAAK,CAAC,GAAG;AAC1B;AAKO,SAAS,yCACd,YAAoB,EACpB,QAAgB,EAChB,WAAmB;IAEnB,MAAM,WAAW,uBAAuB,cAAc,UAAU;IAEhE,IAAI,SAAS,UAAU,KAAK,GAAG;QAC7B,OAAO,IAAI,oBAAoB;IACjC;IAEA,IAAI,eAAe,CAAC,kDAAkD,CAAC;IAEvE,IAAI,SAAS,qBAAqB,CAAC,MAAM,GAAG,GAAG;QAC7C,gBAAgB,CAAC,8BAA8B,EAAE,SAAS,UAAU,CAAC,uBAAuB,CAAC;QAC7F,SAAS,qBAAqB,CAAC,OAAO,CAAC,CAAA;YACrC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,CAAC;QAC/C;IACF;IAEA,IAAI,SAAS,YAAY,CAAC,MAAM,GAAG,GAAG;QACpC,gBAAgB,CAAC,4BAA4B,CAAC;QAC9C,SAAS,YAAY,CAAC,OAAO,CAAC,CAAA;YAC5B,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC;QACzC;IACF;IAEA,IAAI,SAAS,eAAe,CAAC,MAAM,GAAG,GAAG;QACvC,gBAAgB,CAAC,oCAAoC,CAAC;QACtD,SAAS,eAAe,CAAC,OAAO,CAAC,CAAA;YAC/B,gBAAgB,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC;QAC9B;IACF;IAEA,gBAAgB,CAAC,+BAA+B,CAAC;IACjD,gBAAgB,CAAC,wBAAwB,EAAE,KAAK,GAAG,CAAC,SAAS,cAAc,GAAG,KAAK,GAAG,KAAK,CAAC;IAC5F,gBAAgB,CAAC,qBAAqB,EAAE,KAAK,GAAG,CAAC,SAAS,iBAAiB,GAAG,KAAK,GAAG,KAAK,CAAC;IAE5F,OAAO;AACT;AAKO,SAAS;IAUd,MAAM,UAAU,MAAM,IAAI,CAAC,gBAAgB,MAAM;IACjD,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,GAAK,CAAC;YAAE;YAAK;QAAK,CAAC;IAE9F,+BAA+B;IAC/B,MAAM,eAAe,QAAQ,MAAM;IACnC,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,OAAO,CAAC,YAAY,EAAE,KAAK;IAErF,MAAM,qBAAqB,QAAQ,MAAM,CAAC,CAAC,KAAK;QAC9C,GAAG,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI;QACnD,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAC,KAAK;QAC1C,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;QAC3C,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,mBAAmB,OAAO,OAAO,CAAC,oBACrC,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,KAAK,GAAK;IAEnB,MAAM,eAAe,OAAO,OAAO,CAAC,gBACjC,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,SAAS,GAAK;IAEvB,OAAO;QACL;QACA;QACA,SAAS;YACP;YACA,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,MAAM;YAClD;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/services/weather.ts"], "sourcesContent": ["// src/services/weather.ts\r\nimport fetch from 'node-fetch';\r\n\r\nconst API_KEY = process.env.OPENWEATHERMAP_API_KEY;\r\nconst BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';\r\n\r\n/**\r\n * Fetches the current weather for a given location.\r\n * @param location A string in the format \"City, ST\" or \"City, Country\".\r\n * @returns A human-readable weather description string, or null if an error occurs.\r\n */\r\nexport async function getWeather(location: string): Promise<string | null> {\r\n  if (!API_KEY || API_KEY === 'YOUR_OPENWEATHERMAP_API_KEY' || API_KEY.length < 20) {\r\n    console.log('OpenWeatherMap API key is not configured or appears invalid.');\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${BASE_URL}?q=${location}&appid=${API_KEY}&units=imperial`);\r\n    if (!response.ok) {\r\n      console.error('Weather API Error:', `Status: ${response.status}`);\r\n      // Return null to allow the flow to continue without weather data\r\n      return `Could not retrieve weather information due to an API error (Status: ${response.status})`;\r\n    }\r\n    \r\n    const data: any = await response.json();\r\n\r\n    if (data && data.weather && data.main) {\r\n      const description = data.weather[0].description;\r\n      const temp = Math.round(data.main.temp);\r\n      return `${description} with a temperature of ${temp}°F`;\r\n    }\r\n    return null;\r\n  } catch (error) {\r\n    console.error('Error fetching weather data:', error);\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,0BAA0B;;;;AAC1B;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,sBAAsB;AAClD,MAAM,WAAW;AAOV,eAAe,WAAW,QAAgB;IAC/C,IAAI,CAAC,WAAW,YAAY,iCAAiC,QAAQ,MAAM,GAAG,IAAI;QAChF,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,+JAAA,CAAA,UAAK,AAAD,EAAE,GAAG,SAAS,GAAG,EAAE,SAAS,OAAO,EAAE,QAAQ,eAAe,CAAC;QACxF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,sBAAsB,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE;YAChE,iEAAiE;YACjE,OAAO,CAAC,oEAAoE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAClG;QAEA,MAAM,OAAY,MAAM,SAAS,IAAI;QAErC,IAAI,QAAQ,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;YACrC,MAAM,cAAc,KAAK,OAAO,CAAC,EAAE,CAAC,WAAW;YAC/C,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI;YACtC,OAAO,GAAG,YAAY,uBAAuB,EAAE,KAAK,EAAE,CAAC;QACzD;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/services/events.ts"], "sourcesContent": ["// src/services/events.ts\r\nimport fetch from 'node-fetch';\r\nimport { format, add } from 'date-fns';\r\n\r\nconst API_KEY = process.env.EVENTBRITE_PRIVATE_TOKEN;\r\nconst BASE_URL = 'https://www.eventbriteapi.com/v3/events/search/';\r\n\r\n/**\r\n * Fetches local events for a given location and date using Eventbrite.\r\n * @param location A string in the format \"City, ST\" or \"City\".\r\n * @param date The date for which to find events.\r\n * @returns A string summarizing local events, or null if an error occurs.\r\n */\r\nexport async function getEvents(location: string, date: Date): Promise<string | null> {\r\n  if (!API_KEY || API_KEY === 'YOUR_EVENTBRITE_PRIVATE_TOKEN' || API_KEY.length < 10) {\r\n    console.log('Eventbrite API key is not configured or appears invalid.');\r\n    return null;\r\n  }\r\n\r\n  // Eventbrite is more flexible with location strings.\r\n  const city = location.split(',')[0].trim();\r\n  \r\n  // Search for events starting from today up to one week from now to get more results\r\n  const startDate = format(date, \"yyyy-MM-dd'T'HH:mm:ss'Z'\");\r\n  const endDate = format(add(date, { days: 7 }), \"yyyy-MM-dd'T'HH:mm:ss'Z'\");\r\n\r\n  try {\r\n    const url = `${BASE_URL}?location.address=${city}&start_date.range_start=${startDate}&start_date.range_end=${endDate}&sort_by=date`;\r\n    \r\n    const response = await fetch(url, {\r\n        headers: {\r\n            'Authorization': `Bearer ${API_KEY}`,\r\n            'Accept': 'application/json',\r\n        }\r\n    });\r\n    \r\n    if (!response.ok) {\r\n      const errorBody = await response.text();\r\n      console.error('Eventbrite API Error:', `Status: ${response.status}`, errorBody);\r\n      // Return null to allow the flow to continue without event data\r\n      return `Could not retrieve local event information due to an API error (Status: ${response.status}).`;\r\n    }\r\n\r\n    const data: any = await response.json();\r\n\r\n    if (data.events && data.events.length > 0) {\r\n      const eventNames = data.events.slice(0, 5).map((event: any) => event.name.text);\r\n      return `local events happening soon include: ${eventNames.join(', ')}`;\r\n    } else {\r\n      return 'no major local events found on Eventbrite for the upcoming week';\r\n    }\r\n  } catch (error) {\r\n    console.error('Error fetching event data:', error);\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;AACzB;AACA;AAAA;;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,wBAAwB;AACpD,MAAM,WAAW;AAQV,eAAe,UAAU,QAAgB,EAAE,IAAU;IAC1D,IAAI,CAAC,WAAW,YAAY,mCAAmC,QAAQ,MAAM,GAAG,IAAI;QAClF,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,qDAAqD;IACrD,MAAM,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IAExC,oFAAoF;IACpF,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IAC/B,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,MAAG,AAAD,EAAE,MAAM;QAAE,MAAM;IAAE,IAAI;IAE/C,IAAI;QACF,MAAM,MAAM,GAAG,SAAS,kBAAkB,EAAE,KAAK,wBAAwB,EAAE,UAAU,sBAAsB,EAAE,QAAQ,aAAa,CAAC;QAEnI,MAAM,WAAW,MAAM,CAAA,GAAA,+JAAA,CAAA,UAAK,AAAD,EAAE,KAAK;YAC9B,SAAS;gBACL,iBAAiB,CAAC,OAAO,EAAE,SAAS;gBACpC,UAAU;YACd;QACJ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,yBAAyB,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE,EAAE;YACrE,+DAA+D;YAC/D,OAAO,CAAC,wEAAwE,EAAE,SAAS,MAAM,CAAC,EAAE,CAAC;QACvG;QAEA,MAAM,OAAY,MAAM,SAAS,IAAI;QAErC,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,GAAG;YACzC,MAAM,aAAa,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAe,MAAM,IAAI,CAAC,IAAI;YAC9E,OAAO,CAAC,qCAAqC,EAAE,WAAW,IAAI,CAAC,OAAO;QACxE,OAAO;YACL,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/tools/local-data.ts"], "sourcesContent": ["'use server';\r\n\r\n/**\r\n * @fileOverview Defines Genkit tools for fetching local weather and event data.\r\n * This allows the AI to dynamically decide when to pull in local information\r\n * to make social media posts more relevant and timely.\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\nimport { getWeather } from '@/services/weather';\r\nimport { getEvents } from '@/services/events';\r\n\r\nexport const getWeatherTool = ai.defineTool(\r\n  {\r\n    name: 'getWeather',\r\n    description: 'Gets the current weather for a specific location. Use this to make posts more relevant to the current conditions.',\r\n    inputSchema: z.object({\r\n      location: z.string().describe('The city and state, e.g., \"San Francisco, CA\"'),\r\n    }),\r\n    outputSchema: z.string(),\r\n  },\r\n  async (input) => {\r\n    const weather = await getWeather(input.location);\r\n    return weather || 'Could not retrieve weather information.';\r\n  }\r\n);\r\n\r\nexport const getEventsTool = ai.defineTool(\r\n  {\r\n    name: 'getEvents',\r\n    description: 'Finds local events happening on or after the current date for a specific location. Use this to create timely posts about local happenings.',\r\n    inputSchema: z.object({\r\n        location: z.string().describe('The city and state, e.g., \"San Francisco, CA\"'),\r\n    }),\r\n    outputSchema: z.string(),\r\n  },\r\n  async (input) => {\r\n    // Tools will always be called with the current date\r\n    const events = await getEvents(input.location, new Date());\r\n    return events || 'Could not retrieve local event information.';\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;;;;AAEA;;;;CAIC,GAED;AACA;AACA;AACA;;;;;;;;AAEO,MAAM,iBAAiB,qHAAA,CAAA,KAAE,CAAC,UAAU,CACzC;IACE,MAAM;IACN,aAAa;IACb,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAChC;IACA,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM;AACxB,GACA,OAAO;IACL,MAAM,UAAU,MAAM,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD,EAAE,MAAM,QAAQ;IAC/C,OAAO,WAAW;AACpB;AAGK,MAAM,gBAAgB,qHAAA,CAAA,KAAE,CAAC,UAAU,CACxC;IACE,MAAM;IACN,aAAa;IACb,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAClB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAClC;IACA,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM;AACxB,GACA,OAAO;IACL,oDAAoD;IACpD,MAAM,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,YAAS,AAAD,EAAE,MAAM,QAAQ,EAAE,IAAI;IACnD,OAAO,UAAU;AACnB;;;IA5BW;IAeA;;AAfA,iPAAA;AAeA,iPAAA", "debugId": null}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/tools/enhanced-local-data.ts"], "sourcesContent": ["/**\r\n * Enhanced Local Data Tools - Events and Weather Integration\r\n * \r\n * This module provides real-time local events and weather data\r\n * for contextually aware content generation.\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\n\r\nexport interface LocalEvent {\r\n  name: string;\r\n  description?: string;\r\n  start_date: string;\r\n  end_date?: string;\r\n  venue?: string;\r\n  category: string;\r\n  url?: string;\r\n  is_free: boolean;\r\n  relevance_score: number; // 1-10 based on business type\r\n}\r\n\r\nexport interface WeatherContext {\r\n  temperature: number;\r\n  condition: string;\r\n  description: string;\r\n  humidity: number;\r\n  feels_like: number;\r\n  location: string;\r\n  content_opportunities: string[];\r\n  business_impact: string;\r\n}\r\n\r\n/**\r\n * Enhanced Eventbrite Events Tool\r\n */\r\nexport const getEnhancedEventsTool = ai.defineTool({\r\n  name: 'getEnhancedEvents',\r\n  description: 'Fetch local events from Eventbrite API that are relevant to the business type and location',\r\n  input: z.object({\r\n    location: z.string().describe('Location for events (city, country or coordinates)'),\r\n    businessType: z.string().describe('Business type to filter relevant events'),\r\n    radius: z.string().optional().default('25km').describe('Search radius for events'),\r\n    timeframe: z.string().optional().default('this_week').describe('Time period: today, this_week, this_month')\r\n  }),\r\n  output: z.array(z.object({\r\n    name: z.string(),\r\n    description: z.string().optional(),\r\n    start_date: z.string(),\r\n    venue: z.string().optional(),\r\n    category: z.string(),\r\n    url: z.string().optional(),\r\n    is_free: z.boolean(),\r\n    relevance_score: z.number()\r\n  })),\r\n}, async (input) => {\r\n  try {\r\n    if (!process.env.EVENTBRITE_API_KEY) {\r\n      console.log('Eventbrite API key not configured, using fallback events');\r\n      return getEventsFallback(input.location, input.businessType);\r\n    }\r\n\r\n    console.log(`🎪 Fetching events from Eventbrite for ${input.location}...`);\r\n\r\n    // Convert location to coordinates if needed\r\n    const locationQuery = await geocodeLocation(input.location);\r\n    \r\n    // Build Eventbrite API request\r\n    const params = new URLSearchParams({\r\n      'location.address': input.location,\r\n      'location.within': input.radius,\r\n      'start_date.range_start': getDateRange(input.timeframe).start,\r\n      'start_date.range_end': getDateRange(input.timeframe).end,\r\n      'sort_by': 'relevance',\r\n      'page_size': '20',\r\n      'expand': 'venue,category'\r\n    });\r\n\r\n    const response = await fetch(\r\n      `https://www.eventbriteapi.com/v3/events/search/?${params}`,\r\n      {\r\n        headers: {\r\n          'Authorization': `Bearer ${process.env.EVENTBRITE_API_KEY}`,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      console.log(`Eventbrite API error: ${response.status} ${response.statusText}`);\r\n      throw new Error(`Eventbrite API error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log(`✅ Eventbrite returned ${data.events?.length || 0} events`);\r\n\r\n    // Process and filter events by business relevance\r\n    const relevantEvents = processEventbriteEvents(data.events || [], input.businessType);\r\n    \r\n    return relevantEvents.slice(0, 10);\r\n\r\n  } catch (error) {\r\n    console.error('Error fetching Eventbrite events:', error);\r\n    return getEventsFallback(input.location, input.businessType);\r\n  }\r\n});\r\n\r\n/**\r\n * Enhanced OpenWeather Tool\r\n */\r\nexport const getEnhancedWeatherTool = ai.defineTool({\r\n  name: 'getEnhancedWeather',\r\n  description: 'Fetch current weather and forecast with business context and content opportunities',\r\n  input: z.object({\r\n    location: z.string().describe('Location for weather (city, country)'),\r\n    businessType: z.string().describe('Business type to provide relevant weather context'),\r\n    includeForecast: z.boolean().optional().default(false).describe('Include 5-day forecast')\r\n  }),\r\n  output: z.object({\r\n    temperature: z.number(),\r\n    condition: z.string(),\r\n    description: z.string(),\r\n    humidity: z.number(),\r\n    feels_like: z.number(),\r\n    location: z.string(),\r\n    content_opportunities: z.array(z.string()),\r\n    business_impact: z.string(),\r\n    forecast: z.array(z.object({\r\n      date: z.string(),\r\n      temperature: z.number(),\r\n      condition: z.string(),\r\n      business_opportunity: z.string()\r\n    })).optional()\r\n  }),\r\n}, async (input) => {\r\n  try {\r\n    if (!process.env.OPENWEATHER_API_KEY) {\r\n      console.log('OpenWeather API key not configured, using fallback weather');\r\n      return getWeatherFallback(input.location, input.businessType);\r\n    }\r\n\r\n    console.log(`🌤️ Fetching weather from OpenWeather for ${input.location}...`);\r\n\r\n    // Current weather\r\n    const currentParams = new URLSearchParams({\r\n      q: input.location,\r\n      appid: process.env.OPENWEATHER_API_KEY!,\r\n      units: 'metric'\r\n    });\r\n\r\n    const currentResponse = await fetch(\r\n      `https://api.openweathermap.org/data/2.5/weather?${currentParams}`\r\n    );\r\n\r\n    if (!currentResponse.ok) {\r\n      console.log(`OpenWeather API error: ${currentResponse.status} ${currentResponse.statusText}`);\r\n      throw new Error(`OpenWeather API error: ${currentResponse.status}`);\r\n    }\r\n\r\n    const currentData = await currentResponse.json();\r\n    console.log(`✅ OpenWeather returned current weather for ${currentData.name}`);\r\n\r\n    // Process weather data with business context\r\n    const weatherContext = processWeatherData(currentData, input.businessType);\r\n\r\n    // Get forecast if requested\r\n    if (input.includeForecast) {\r\n      const forecastParams = new URLSearchParams({\r\n        q: input.location,\r\n        appid: process.env.OPENWEATHER_API_KEY!,\r\n        units: 'metric'\r\n      });\r\n\r\n      const forecastResponse = await fetch(\r\n        `https://api.openweathermap.org/data/2.5/forecast?${forecastParams}`\r\n      );\r\n\r\n      if (forecastResponse.ok) {\r\n        const forecastData = await forecastResponse.json();\r\n        weatherContext.forecast = processForecastData(forecastData, input.businessType);\r\n      }\r\n    }\r\n\r\n    return weatherContext;\r\n\r\n  } catch (error) {\r\n    console.error('Error fetching weather:', error);\r\n    return getWeatherFallback(input.location, input.businessType);\r\n  }\r\n});\r\n\r\n/**\r\n * Helper functions\r\n */\r\nasync function geocodeLocation(location: string): Promise<{lat: number, lon: number} | null> {\r\n  try {\r\n    if (!process.env.OPENWEATHER_API_KEY) return null;\r\n\r\n    const params = new URLSearchParams({\r\n      q: location,\r\n      limit: '1',\r\n      appid: process.env.OPENWEATHER_API_KEY!\r\n    });\r\n\r\n    const response = await fetch(\r\n      `https://api.openweathermap.org/geo/1.0/direct?${params}`\r\n    );\r\n\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      if (data.length > 0) {\r\n        return { lat: data[0].lat, lon: data[0].lon };\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('Error geocoding location:', error);\r\n  }\r\n  return null;\r\n}\r\n\r\nfunction getDateRange(timeframe: string): {start: string, end: string} {\r\n  const now = new Date();\r\n  const start = new Date(now);\r\n  let end = new Date(now);\r\n\r\n  switch (timeframe) {\r\n    case 'today':\r\n      end.setDate(end.getDate() + 1);\r\n      break;\r\n    case 'this_week':\r\n      end.setDate(end.getDate() + 7);\r\n      break;\r\n    case 'this_month':\r\n      end.setMonth(end.getMonth() + 1);\r\n      break;\r\n    default:\r\n      end.setDate(end.getDate() + 7);\r\n  }\r\n\r\n  return {\r\n    start: start.toISOString(),\r\n    end: end.toISOString()\r\n  };\r\n}\r\n\r\nfunction processEventbriteEvents(events: any[], businessType: string): LocalEvent[] {\r\n  return events.map(event => {\r\n    const relevanceScore = calculateEventRelevance(event, businessType);\r\n    \r\n    return {\r\n      name: event.name?.text || 'Unnamed Event',\r\n      description: event.description?.text?.substring(0, 200) || '',\r\n      start_date: event.start?.local || event.start?.utc || '',\r\n      end_date: event.end?.local || event.end?.utc,\r\n      venue: event.venue?.name || 'Online Event',\r\n      category: event.category?.name || 'General',\r\n      url: event.url,\r\n      is_free: event.is_free || false,\r\n      relevance_score: relevanceScore\r\n    };\r\n  }).filter(event => event.relevance_score >= 5)\r\n    .sort((a, b) => b.relevance_score - a.relevance_score);\r\n}\r\n\r\nfunction calculateEventRelevance(event: any, businessType: string): number {\r\n  let score = 5; // Base score\r\n\r\n  const eventName = (event.name?.text || '').toLowerCase();\r\n  const eventDescription = (event.description?.text || '').toLowerCase();\r\n  const eventCategory = (event.category?.name || '').toLowerCase();\r\n\r\n  // Business type relevance\r\n  const businessKeywords = getBusinessKeywords(businessType);\r\n  for (const keyword of businessKeywords) {\r\n    if (eventName.includes(keyword) || eventDescription.includes(keyword) || eventCategory.includes(keyword)) {\r\n      score += 2;\r\n    }\r\n  }\r\n\r\n  // Event category bonus\r\n  if (eventCategory.includes('business') || eventCategory.includes('networking')) {\r\n    score += 1;\r\n  }\r\n\r\n  // Free events get slight bonus for broader appeal\r\n  if (event.is_free) {\r\n    score += 1;\r\n  }\r\n\r\n  return Math.min(10, score);\r\n}\r\n\r\nfunction getBusinessKeywords(businessType: string): string[] {\r\n  const keywordMap: Record<string, string[]> = {\r\n    'financial technology software': ['fintech', 'finance', 'banking', 'payment', 'blockchain', 'cryptocurrency', 'startup', 'tech'],\r\n    'restaurant': ['food', 'culinary', 'cooking', 'dining', 'chef', 'restaurant', 'hospitality'],\r\n    'fitness': ['fitness', 'health', 'wellness', 'gym', 'workout', 'nutrition', 'sports'],\r\n    'technology': ['tech', 'software', 'programming', 'ai', 'digital', 'innovation', 'startup'],\r\n    'beauty': ['beauty', 'cosmetics', 'skincare', 'wellness', 'spa', 'fashion'],\r\n    'retail': ['retail', 'shopping', 'ecommerce', 'business', 'sales', 'marketing']\r\n  };\r\n\r\n  return keywordMap[businessType.toLowerCase()] || ['business', 'networking', 'professional'];\r\n}\r\n\r\nfunction processWeatherData(weatherData: any, businessType: string): WeatherContext {\r\n  const temperature = Math.round(weatherData.main.temp);\r\n  const condition = weatherData.weather[0].main;\r\n  const description = weatherData.weather[0].description;\r\n  \r\n  return {\r\n    temperature,\r\n    condition,\r\n    description,\r\n    humidity: weatherData.main.humidity,\r\n    feels_like: Math.round(weatherData.main.feels_like),\r\n    location: weatherData.name,\r\n    content_opportunities: generateWeatherContentOpportunities(condition, temperature, businessType),\r\n    business_impact: generateBusinessWeatherImpact(condition, temperature, businessType)\r\n  };\r\n}\r\n\r\nfunction processForecastData(forecastData: any, businessType: string): any[] {\r\n  const dailyForecasts = forecastData.list.filter((_: any, index: number) => index % 8 === 0).slice(0, 5);\r\n  \r\n  return dailyForecasts.map((forecast: any) => ({\r\n    date: new Date(forecast.dt * 1000).toLocaleDateString(),\r\n    temperature: Math.round(forecast.main.temp),\r\n    condition: forecast.weather[0].main,\r\n    business_opportunity: generateBusinessWeatherImpact(forecast.weather[0].main, forecast.main.temp, businessType)\r\n  }));\r\n}\r\n\r\nfunction generateWeatherContentOpportunities(condition: string, temperature: number, businessType: string): string[] {\r\n  const opportunities: string[] = [];\r\n\r\n  // Temperature-based opportunities\r\n  if (temperature > 25) {\r\n    opportunities.push('Hot weather content angle', 'Summer promotion opportunity', 'Cooling solutions messaging');\r\n  } else if (temperature < 10) {\r\n    opportunities.push('Cold weather content angle', 'Winter comfort messaging', 'Warm-up solutions');\r\n  }\r\n\r\n  // Condition-based opportunities\r\n  switch (condition.toLowerCase()) {\r\n    case 'rain':\r\n      opportunities.push('Rainy day indoor activities', 'Weather protection messaging', 'Cozy atmosphere content');\r\n      break;\r\n    case 'sunny':\r\n    case 'clear':\r\n      opportunities.push('Beautiful weather celebration', 'Outdoor activity promotion', 'Sunshine positivity');\r\n      break;\r\n    case 'clouds':\r\n      opportunities.push('Perfect weather for activities', 'Comfortable conditions messaging');\r\n      break;\r\n  }\r\n\r\n  // Business-specific weather opportunities\r\n  const businessOpportunities = getBusinessWeatherOpportunities(businessType, condition, temperature);\r\n  opportunities.push(...businessOpportunities);\r\n\r\n  return opportunities;\r\n}\r\n\r\nfunction generateBusinessWeatherImpact(condition: string, temperature: number, businessType: string): string {\r\n  const businessImpacts: Record<string, Record<string, string>> = {\r\n    'restaurant': {\r\n      'sunny': 'Perfect weather for outdoor dining and patio service',\r\n      'rain': 'Great opportunity to promote cozy indoor dining experience',\r\n      'hot': 'Ideal time to highlight refreshing drinks and cool dishes',\r\n      'cold': 'Perfect weather for warm comfort food and hot beverages'\r\n    },\r\n    'fitness': {\r\n      'sunny': 'Excellent conditions for outdoor workouts and activities',\r\n      'rain': 'Great time to promote indoor fitness programs',\r\n      'hot': 'Important to emphasize hydration and cooling strategies',\r\n      'cold': 'Perfect for promoting warm-up routines and indoor training'\r\n    },\r\n    'retail': {\r\n      'sunny': 'Great shopping weather, people are out and about',\r\n      'rain': 'Perfect time for online shopping promotions',\r\n      'hot': 'Opportunity to promote summer collections and cooling products',\r\n      'cold': 'Ideal for promoting warm clothing and comfort items'\r\n    }\r\n  };\r\n\r\n  const businessKey = businessType.toLowerCase();\r\n  const impacts = businessImpacts[businessKey] || businessImpacts['retail'];\r\n\r\n  if (temperature > 25) return impacts['hot'] || 'Weather creates opportunities for seasonal promotions';\r\n  if (temperature < 10) return impacts['cold'] || 'Weather creates opportunities for comfort-focused messaging';\r\n  \r\n  return impacts[condition.toLowerCase()] || impacts['sunny'] || 'Current weather conditions are favorable for business';\r\n}\r\n\r\nfunction getBusinessWeatherOpportunities(businessType: string, condition: string, temperature: number): string[] {\r\n  // Business-specific weather content opportunities\r\n  const opportunities: string[] = [];\r\n\r\n  if (businessType.toLowerCase().includes('restaurant')) {\r\n    if (condition === 'sunny') opportunities.push('Outdoor dining promotion', 'Fresh seasonal menu highlight');\r\n    if (condition === 'rain') opportunities.push('Cozy indoor atmosphere', 'Comfort food specials');\r\n  }\r\n\r\n  if (businessType.toLowerCase().includes('fitness')) {\r\n    if (condition === 'sunny') opportunities.push('Outdoor workout motivation', 'Vitamin D benefits');\r\n    if (temperature > 25) opportunities.push('Hydration importance', 'Summer fitness tips');\r\n  }\r\n\r\n  return opportunities;\r\n}\r\n\r\n// Fallback functions\r\nfunction getEventsFallback(location: string, businessType: string): LocalEvent[] {\r\n  return [\r\n    {\r\n      name: `${businessType} networking event in ${location}`,\r\n      description: `Local networking opportunity for ${businessType} professionals`,\r\n      start_date: new Date(Date.now() + 86400000 * 3).toISOString(), // 3 days from now\r\n      venue: `${location} Business Center`,\r\n      category: 'Business & Professional',\r\n      is_free: true,\r\n      relevance_score: 8\r\n    }\r\n  ];\r\n}\r\n\r\nfunction getWeatherFallback(location: string, businessType: string): WeatherContext {\r\n  return {\r\n    temperature: 22,\r\n    condition: 'Clear',\r\n    description: 'clear sky',\r\n    humidity: 60,\r\n    feels_like: 24,\r\n    location: location,\r\n    content_opportunities: ['Pleasant weather content opportunity', 'Comfortable conditions messaging'],\r\n    business_impact: 'Current weather conditions are favorable for business activities'\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAED;AACA;;;AA4BO,MAAM,wBAAwB,qHAAA,CAAA,KAAE,CAAC,UAAU,CAAC;IACjD,MAAM;IACN,aAAa;IACb,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACd,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAClC,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,QAAQ,QAAQ,CAAC;QACvD,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,aAAa,QAAQ,CAAC;IACjE;IACA,QAAQ,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACvB,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM;QACd,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAChC,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM;QACpB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,KAAK,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxB,SAAS,sIAAA,CAAA,IAAC,CAAC,OAAO;QAClB,iBAAiB,sIAAA,CAAA,IAAC,CAAC,MAAM;IAC3B;AACF,GAAG,OAAO;IACR,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YACnC,QAAQ,GAAG,CAAC;YACZ,OAAO,kBAAkB,MAAM,QAAQ,EAAE,MAAM,YAAY;QAC7D;QAEA,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,MAAM,QAAQ,CAAC,GAAG,CAAC;QAEzE,4CAA4C;QAC5C,MAAM,gBAAgB,MAAM,gBAAgB,MAAM,QAAQ;QAE1D,+BAA+B;QAC/B,MAAM,SAAS,IAAI,gBAAgB;YACjC,oBAAoB,MAAM,QAAQ;YAClC,mBAAmB,MAAM,MAAM;YAC/B,0BAA0B,aAAa,MAAM,SAAS,EAAE,KAAK;YAC7D,wBAAwB,aAAa,MAAM,SAAS,EAAE,GAAG;YACzD,WAAW;YACX,aAAa;YACb,UAAU;QACZ;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,gDAAgD,EAAE,QAAQ,EAC3D;YACE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,kBAAkB,EAAE;gBAC3D,gBAAgB;YAClB;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAC7E,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,MAAM,EAAE;QAC5D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,KAAK,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC;QAEtE,kDAAkD;QAClD,MAAM,iBAAiB,wBAAwB,KAAK,MAAM,IAAI,EAAE,EAAE,MAAM,YAAY;QAEpF,OAAO,eAAe,KAAK,CAAC,GAAG;IAEjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,kBAAkB,MAAM,QAAQ,EAAE,MAAM,YAAY;IAC7D;AACF;AAKO,MAAM,yBAAyB,qHAAA,CAAA,KAAE,CAAC,UAAU,CAAC;IAClD,MAAM;IACN,aAAa;IACb,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACd,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAClC,iBAAiB,sIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC,OAAO,QAAQ,CAAC;IAClE;IACA,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACf,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;QACrB,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM;QACnB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;QACrB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM;QACpB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,uBAAuB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM;QACvC,iBAAiB,sIAAA,CAAA,IAAC,CAAC,MAAM;QACzB,UAAU,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACzB,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM;YACd,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM;YACnB,sBAAsB,sIAAA,CAAA,IAAC,CAAC,MAAM;QAChC,IAAI,QAAQ;IACd;AACF,GAAG,OAAO;IACR,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,CAAC,mBAAmB,EAAE;YACpC,QAAQ,GAAG,CAAC;YACZ,OAAO,mBAAmB,MAAM,QAAQ,EAAE,MAAM,YAAY;QAC9D;QAEA,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,MAAM,QAAQ,CAAC,GAAG,CAAC;QAE5E,kBAAkB;QAClB,MAAM,gBAAgB,IAAI,gBAAgB;YACxC,GAAG,MAAM,QAAQ;YACjB,OAAO,QAAQ,GAAG,CAAC,mBAAmB;YACtC,OAAO;QACT;QAEA,MAAM,kBAAkB,MAAM,MAC5B,CAAC,gDAAgD,EAAE,eAAe;QAGpE,IAAI,CAAC,gBAAgB,EAAE,EAAE;YACvB,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,gBAAgB,MAAM,CAAC,CAAC,EAAE,gBAAgB,UAAU,EAAE;YAC5F,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,gBAAgB,MAAM,EAAE;QACpE;QAEA,MAAM,cAAc,MAAM,gBAAgB,IAAI;QAC9C,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,YAAY,IAAI,EAAE;QAE5E,6CAA6C;QAC7C,MAAM,iBAAiB,mBAAmB,aAAa,MAAM,YAAY;QAEzE,4BAA4B;QAC5B,IAAI,MAAM,eAAe,EAAE;YACzB,MAAM,iBAAiB,IAAI,gBAAgB;gBACzC,GAAG,MAAM,QAAQ;gBACjB,OAAO,QAAQ,GAAG,CAAC,mBAAmB;gBACtC,OAAO;YACT;YAEA,MAAM,mBAAmB,MAAM,MAC7B,CAAC,iDAAiD,EAAE,gBAAgB;YAGtE,IAAI,iBAAiB,EAAE,EAAE;gBACvB,MAAM,eAAe,MAAM,iBAAiB,IAAI;gBAChD,eAAe,QAAQ,GAAG,oBAAoB,cAAc,MAAM,YAAY;YAChF;QACF;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,mBAAmB,MAAM,QAAQ,EAAE,MAAM,YAAY;IAC9D;AACF;AAEA;;CAEC,GACD,eAAe,gBAAgB,QAAgB;IAC7C,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,CAAC,mBAAmB,EAAE,OAAO;QAE7C,MAAM,SAAS,IAAI,gBAAgB;YACjC,GAAG;YACH,OAAO;YACP,OAAO,QAAQ,GAAG,CAAC,mBAAmB;QACxC;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,8CAA8C,EAAE,QAAQ;QAG3D,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,MAAM,GAAG,GAAG;gBACnB,OAAO;oBAAE,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG;oBAAE,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG;gBAAC;YAC9C;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;IACA,OAAO;AACT;AAEA,SAAS,aAAa,SAAiB;IACrC,MAAM,MAAM,IAAI;IAChB,MAAM,QAAQ,IAAI,KAAK;IACvB,IAAI,MAAM,IAAI,KAAK;IAEnB,OAAQ;QACN,KAAK;YACH,IAAI,OAAO,CAAC,IAAI,OAAO,KAAK;YAC5B;QACF,KAAK;YACH,IAAI,OAAO,CAAC,IAAI,OAAO,KAAK;YAC5B;QACF,KAAK;YACH,IAAI,QAAQ,CAAC,IAAI,QAAQ,KAAK;YAC9B;QACF;YACE,IAAI,OAAO,CAAC,IAAI,OAAO,KAAK;IAChC;IAEA,OAAO;QACL,OAAO,MAAM,WAAW;QACxB,KAAK,IAAI,WAAW;IACtB;AACF;AAEA,SAAS,wBAAwB,MAAa,EAAE,YAAoB;IAClE,OAAO,OAAO,GAAG,CAAC,CAAA;QAChB,MAAM,iBAAiB,wBAAwB,OAAO;QAEtD,OAAO;YACL,MAAM,MAAM,IAAI,EAAE,QAAQ;YAC1B,aAAa,MAAM,WAAW,EAAE,MAAM,UAAU,GAAG,QAAQ;YAC3D,YAAY,MAAM,KAAK,EAAE,SAAS,MAAM,KAAK,EAAE,OAAO;YACtD,UAAU,MAAM,GAAG,EAAE,SAAS,MAAM,GAAG,EAAE;YACzC,OAAO,MAAM,KAAK,EAAE,QAAQ;YAC5B,UAAU,MAAM,QAAQ,EAAE,QAAQ;YAClC,KAAK,MAAM,GAAG;YACd,SAAS,MAAM,OAAO,IAAI;YAC1B,iBAAiB;QACnB;IACF,GAAG,MAAM,CAAC,CAAA,QAAS,MAAM,eAAe,IAAI,GACzC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,eAAe,GAAG,EAAE,eAAe;AACzD;AAEA,SAAS,wBAAwB,KAAU,EAAE,YAAoB;IAC/D,IAAI,QAAQ,GAAG,aAAa;IAE5B,MAAM,YAAY,CAAC,MAAM,IAAI,EAAE,QAAQ,EAAE,EAAE,WAAW;IACtD,MAAM,mBAAmB,CAAC,MAAM,WAAW,EAAE,QAAQ,EAAE,EAAE,WAAW;IACpE,MAAM,gBAAgB,CAAC,MAAM,QAAQ,EAAE,QAAQ,EAAE,EAAE,WAAW;IAE9D,0BAA0B;IAC1B,MAAM,mBAAmB,oBAAoB;IAC7C,KAAK,MAAM,WAAW,iBAAkB;QACtC,IAAI,UAAU,QAAQ,CAAC,YAAY,iBAAiB,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,UAAU;YACxG,SAAS;QACX;IACF;IAEA,uBAAuB;IACvB,IAAI,cAAc,QAAQ,CAAC,eAAe,cAAc,QAAQ,CAAC,eAAe;QAC9E,SAAS;IACX;IAEA,kDAAkD;IAClD,IAAI,MAAM,OAAO,EAAE;QACjB,SAAS;IACX;IAEA,OAAO,KAAK,GAAG,CAAC,IAAI;AACtB;AAEA,SAAS,oBAAoB,YAAoB;IAC/C,MAAM,aAAuC;QAC3C,iCAAiC;YAAC;YAAW;YAAW;YAAW;YAAW;YAAc;YAAkB;YAAW;SAAO;QAChI,cAAc;YAAC;YAAQ;YAAY;YAAW;YAAU;YAAQ;YAAc;SAAc;QAC5F,WAAW;YAAC;YAAW;YAAU;YAAY;YAAO;YAAW;YAAa;SAAS;QACrF,cAAc;YAAC;YAAQ;YAAY;YAAe;YAAM;YAAW;YAAc;SAAU;QAC3F,UAAU;YAAC;YAAU;YAAa;YAAY;YAAY;YAAO;SAAU;QAC3E,UAAU;YAAC;YAAU;YAAY;YAAa;YAAY;YAAS;SAAY;IACjF;IAEA,OAAO,UAAU,CAAC,aAAa,WAAW,GAAG,IAAI;QAAC;QAAY;QAAc;KAAe;AAC7F;AAEA,SAAS,mBAAmB,WAAgB,EAAE,YAAoB;IAChE,MAAM,cAAc,KAAK,KAAK,CAAC,YAAY,IAAI,CAAC,IAAI;IACpD,MAAM,YAAY,YAAY,OAAO,CAAC,EAAE,CAAC,IAAI;IAC7C,MAAM,cAAc,YAAY,OAAO,CAAC,EAAE,CAAC,WAAW;IAEtD,OAAO;QACL;QACA;QACA;QACA,UAAU,YAAY,IAAI,CAAC,QAAQ;QACnC,YAAY,KAAK,KAAK,CAAC,YAAY,IAAI,CAAC,UAAU;QAClD,UAAU,YAAY,IAAI;QAC1B,uBAAuB,oCAAoC,WAAW,aAAa;QACnF,iBAAiB,8BAA8B,WAAW,aAAa;IACzE;AACF;AAEA,SAAS,oBAAoB,YAAiB,EAAE,YAAoB;IAClE,MAAM,iBAAiB,aAAa,IAAI,CAAC,MAAM,CAAC,CAAC,GAAQ,QAAkB,QAAQ,MAAM,GAAG,KAAK,CAAC,GAAG;IAErG,OAAO,eAAe,GAAG,CAAC,CAAC,WAAkB,CAAC;YAC5C,MAAM,IAAI,KAAK,SAAS,EAAE,GAAG,MAAM,kBAAkB;YACrD,aAAa,KAAK,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI;YAC1C,WAAW,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI;YACnC,sBAAsB,8BAA8B,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE;QACpG,CAAC;AACH;AAEA,SAAS,oCAAoC,SAAiB,EAAE,WAAmB,EAAE,YAAoB;IACvG,MAAM,gBAA0B,EAAE;IAElC,kCAAkC;IAClC,IAAI,cAAc,IAAI;QACpB,cAAc,IAAI,CAAC,6BAA6B,gCAAgC;IAClF,OAAO,IAAI,cAAc,IAAI;QAC3B,cAAc,IAAI,CAAC,8BAA8B,4BAA4B;IAC/E;IAEA,gCAAgC;IAChC,OAAQ,UAAU,WAAW;QAC3B,KAAK;YACH,cAAc,IAAI,CAAC,+BAA+B,gCAAgC;YAClF;QACF,KAAK;QACL,KAAK;YACH,cAAc,IAAI,CAAC,iCAAiC,8BAA8B;YAClF;QACF,KAAK;YACH,cAAc,IAAI,CAAC,kCAAkC;YACrD;IACJ;IAEA,0CAA0C;IAC1C,MAAM,wBAAwB,gCAAgC,cAAc,WAAW;IACvF,cAAc,IAAI,IAAI;IAEtB,OAAO;AACT;AAEA,SAAS,8BAA8B,SAAiB,EAAE,WAAmB,EAAE,YAAoB;IACjG,MAAM,kBAA0D;QAC9D,cAAc;YACZ,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;QACA,WAAW;YACT,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;QACA,UAAU;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,aAAa,WAAW;IAC5C,MAAM,UAAU,eAAe,CAAC,YAAY,IAAI,eAAe,CAAC,SAAS;IAEzE,IAAI,cAAc,IAAI,OAAO,OAAO,CAAC,MAAM,IAAI;IAC/C,IAAI,cAAc,IAAI,OAAO,OAAO,CAAC,OAAO,IAAI;IAEhD,OAAO,OAAO,CAAC,UAAU,WAAW,GAAG,IAAI,OAAO,CAAC,QAAQ,IAAI;AACjE;AAEA,SAAS,gCAAgC,YAAoB,EAAE,SAAiB,EAAE,WAAmB;IACnG,kDAAkD;IAClD,MAAM,gBAA0B,EAAE;IAElC,IAAI,aAAa,WAAW,GAAG,QAAQ,CAAC,eAAe;QACrD,IAAI,cAAc,SAAS,cAAc,IAAI,CAAC,4BAA4B;QAC1E,IAAI,cAAc,QAAQ,cAAc,IAAI,CAAC,0BAA0B;IACzE;IAEA,IAAI,aAAa,WAAW,GAAG,QAAQ,CAAC,YAAY;QAClD,IAAI,cAAc,SAAS,cAAc,IAAI,CAAC,8BAA8B;QAC5E,IAAI,cAAc,IAAI,cAAc,IAAI,CAAC,wBAAwB;IACnE;IAEA,OAAO;AACT;AAEA,qBAAqB;AACrB,SAAS,kBAAkB,QAAgB,EAAE,YAAoB;IAC/D,OAAO;QACL;YACE,MAAM,GAAG,aAAa,qBAAqB,EAAE,UAAU;YACvD,aAAa,CAAC,iCAAiC,EAAE,aAAa,cAAc,CAAC;YAC7E,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,GAAG,WAAW;YAC3D,OAAO,GAAG,SAAS,gBAAgB,CAAC;YACpC,UAAU;YACV,SAAS;YACT,iBAAiB;QACnB;KACD;AACH;AAEA,SAAS,mBAAmB,QAAgB,EAAE,YAAoB;IAChE,OAAO;QACL,aAAa;QACb,WAAW;QACX,aAAa;QACb,UAAU;QACV,YAAY;QACZ,UAAU;QACV,uBAAuB;YAAC;YAAwC;SAAmC;QACnG,iBAAiB;IACnB;AACF", "debugId": null}}, {"offset": {"line": 1706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/prompts/advanced-ai-prompt.ts"], "sourcesContent": ["/**\r\n * Advanced AI Content Generation Prompt\r\n * \r\n * This prompt integrates trending topics, competitor analysis, cultural optimization,\r\n * human-like content generation, and traffic-driving strategies.\r\n */\r\n\r\nexport const ADVANCED_AI_PROMPT = `You are an elite social media strategist, cultural anthropologist, and viral content creator with deep expertise in the {{{businessType}}} industry.\r\n\r\nYour mission is to create content that:\r\n🎯 Captures trending conversations and cultural moments\r\n🚀 Drives maximum traffic and business results\r\n🤝 Feels authentically human and culturally sensitive\r\n💡 Differentiates from competitors strategically\r\n📈 Optimizes for platform-specific viral potential\r\n🌤️ Integrates current weather and local events naturally\r\n🎪 Leverages local happenings for timely relevance\r\n🌍 Uses ENGLISH ONLY for all content generation\r\n\r\nBUSINESS INTELLIGENCE:\r\n- Industry: {{{businessType}}}\r\n- Location: {{{location}}}\r\n- Brand Voice: {{{writingTone}}}\r\n- Content Themes: {{{contentThemes}}}\r\n- Day: {{{dayOfWeek}}}\r\n- Date: {{{currentDate}}}\r\n{{#if platform}}- Primary Platform: {{{platform}}}{{/if}}\r\n{{#if services}}- Services/Products: {{{services}}}{{/if}}\r\n{{#if targetAudience}}- Target Audience: {{{targetAudience}}}{{/if}}\r\n{{#if keyFeatures}}- Key Features: {{{keyFeatures}}}{{/if}}\r\n{{#if competitiveAdvantages}}- Competitive Edge: {{{competitiveAdvantages}}}{{/if}}\r\n{{#if contentVariation}}- Content Approach: {{{contentVariation}}} (MANDATORY: Use this specific approach for content generation){{/if}}\r\n\r\nTRENDING TOPICS INTEGRATION:\r\nResearch and incorporate current trending topics relevant to:\r\n- {{{businessType}}} industry developments\r\n- {{{location}}} local events and cultural moments\r\n- Platform-specific trending hashtags and conversations\r\n- Seasonal relevance and timely opportunities\r\n- News events that connect to your business value\r\n\r\nCOMPETITOR DIFFERENTIATION STRATEGY:\r\nAnalyze and differentiate from typical competitor content by:\r\n- Avoiding generic industry messaging\r\n- Finding unique angles on common topics\r\n- Highlighting authentic personal/business stories\r\n- Focusing on underserved audience needs\r\n- Creating content gaps competitors miss\r\n- Using authentic local cultural connections\r\n\r\nCONTENT DIVERSITY ENFORCEMENT:\r\nCRITICAL: Each post must be completely different from previous generations:\r\n- Use different opening hooks (question, statement, story, statistic, quote)\r\n- Vary content structure (problem-solution, story-lesson, tip-benefit, behind-scenes)\r\n- Alternate between different emotional tones (inspiring, educational, entertaining, personal)\r\n- Change content length and paragraph structure significantly\r\n- Use different call-to-action styles (direct, subtle, question-based, action-oriented)\r\n- Vary hashtag themes and combinations\r\n- Never repeat the same content pattern or messaging approach\r\n\r\n{{#if contentVariation}}\r\nMANDATORY CONTENT VARIATION APPROACH - {{{contentVariation}}}:\r\n\r\nUse the following approach based on the content variation specified:\r\n- For \"trending_hook\": Start with a trending topic or viral conversation, connect the trend to your business naturally, use current social media language and references, include trending hashtags and phrases\r\n- For \"story_driven\": Begin with a compelling personal or customer story, use narrative structure with beginning, middle, end, include emotional elements and relatable characters, end with a meaningful lesson or takeaway\r\n- For \"educational_tip\": Lead with valuable, actionable advice, use numbered lists or step-by-step format, position your business as the expert solution, include \"did you know\" or \"pro tip\" elements\r\n- For \"behind_scenes\": Show the human side of your business, include process, preparation, or team moments, use authentic, unpolished language, create connection through transparency\r\n- For \"question_engagement\": Start with a thought-provoking question, encourage audience participation and responses, use polls, \"this or that,\" or opinion requests, build community through conversation\r\n- For \"statistic_driven\": Lead with surprising or compelling statistics, use data to support your business value, include industry insights and research, position your business as data-informed\r\n- For \"personal_insight\": Share personal experiences or observations, use first-person perspective and authentic voice, include lessons learned or mistakes made, connect personal growth to business value\r\n- For \"industry_contrarian\": Challenge common industry assumptions, present alternative viewpoints respectfully, use \"unpopular opinion\" or \"hot take\" framing, support contrarian views with evidence\r\n- For \"local_cultural\": Reference local events, landmarks, or culture, use location-specific language and references, connect to community values and traditions, show deep local understanding\r\n- For \"seasonal_relevance\": Connect to current season, weather, or holidays, use timely references and seasonal language, align business offerings with seasonal needs\r\n- For \"problem_solution\": Identify a specific customer pain point, agitate the problem to create urgency, present your business as the clear solution, use before/after or transformation language\r\n- For \"inspiration_motivation\": Use uplifting, motivational language, include inspirational quotes or mantras, focus on transformation and possibility, connect inspiration to business outcomes\r\n\r\nApply the specific approach for the {{{contentVariation}}} variation throughout your content generation.\r\n{{/if}}\r\n\r\nCULTURAL & LOCATION OPTIMIZATION:\r\nFor {{{location}}}, incorporate:\r\n- Local cultural nuances and values\r\n- Regional language preferences and expressions\r\n- Community customs and social norms\r\n- Seasonal and cultural calendar awareness\r\n- Local landmarks, events, and references\r\n- Respectful acknowledgment of cultural diversity\r\n\r\nINTELLIGENT CONTEXT USAGE:\r\n{{#if contextInstructions}}\r\nCONTEXT INSTRUCTIONS FOR THIS SPECIFIC POST:\r\n{{{contextInstructions}}}\r\n\r\nFollow these instructions precisely - they are based on expert analysis of what information is relevant for this specific business type and location.\r\n{{/if}}\r\n\r\nWEATHER & EVENTS INTEGRATION:\r\n{{#if selectedWeather}}\r\n- Current weather: {{{selectedWeather.temperature}}}°C, {{{selectedWeather.condition}}}\r\n- Business impact: {{{selectedWeather.business_impact}}}\r\n- Content opportunities: {{{selectedWeather.content_opportunities}}}\r\n{{/if}}\r\n\r\n{{#if selectedEvents}}\r\n- Relevant local events:\r\n{{#each selectedEvents}}\r\n  * {{{this.name}}} ({{{this.category}}}) - {{{this.start_date}}}\r\n{{/each}}\r\n{{/if}}\r\n\r\nUse this information ONLY if the context instructions indicate it's relevant for this business type.\r\n\r\nHUMAN-LIKE AUTHENTICITY MARKERS:\r\nMake content feel genuinely human by:\r\n- Using conversational, imperfect language\r\n- Including personal experiences and observations\r\n- Showing vulnerability and learning moments\r\n- Using specific details over generic statements\r\n- Adding natural speech patterns and contractions\r\n- Including time-specific references (today, this morning)\r\n- Expressing genuine emotions and reactions\r\n\r\nTRAFFIC-DRIVING OPTIMIZATION:\r\nMaximize engagement and traffic through:\r\n- Curiosity gaps that demand attention\r\n- Shareability factors that encourage spreading\r\n- Conversion triggers that drive action\r\n- Social proof elements that build trust\r\n- Interactive elements that boost engagement\r\n- Viral hooks that capture trending conversations\r\n\r\nADVANCED COPYWRITING FRAMEWORKS:\r\n1. **AIDA Framework**: Attention → Interest → Desire → Action\r\n2. **PAS Framework**: Problem → Agitation → Solution  \r\n3. **Storytelling Arc**: Setup → Conflict → Resolution → Lesson\r\n4. **Social Proof Stack**: Testimonial → Statistics → Authority → Community\r\n5. **Curiosity Loop**: Hook → Tension → Payoff → Next Hook\r\n\r\nPSYCHOLOGICAL TRIGGERS FOR MAXIMUM ENGAGEMENT:\r\n✅ **Urgency & Scarcity**: Time-sensitive opportunities\r\n✅ **Social Proof**: Community validation and testimonials\r\n✅ **FOMO**: Exclusive access and insider information\r\n✅ **Curiosity Gaps**: Intriguing questions and reveals\r\n✅ **Emotional Resonance**: Joy, surprise, inspiration, empathy\r\n✅ **Authority**: Expert insights and industry knowledge\r\n✅ **Reciprocity**: Valuable tips and free insights\r\n✅ **Tribal Identity**: Community belonging and shared values\r\n\r\nCONTENT GENERATION REQUIREMENTS:\r\n\r\nGenerate a comprehensive social media post with these components:\r\n\r\n1. **CAPTION (content)**:\r\n   - Start with a trending topic hook or cultural moment\r\n   - Use authentic, conversational human language\r\n   - Include competitor differentiation naturally\r\n   - Apply psychological triggers strategically\r\n   - Incorporate local cultural references appropriately\r\n   - End with traffic-driving call-to-action\r\n   - Length optimized for platform and engagement\r\n   - Feel like it was written by a real person, not AI\r\n\r\n2. **CATCHY WORDS (catchyWords)**:\r\n   - Create relevant, business-focused catchy words (max 5 words)\r\n   - MUST be directly related to the specific business services/products\r\n   - Use clear, professional language that matches the business type\r\n   - Focus on the business value proposition or key service\r\n   - Avoid generic phrases like \"Banking Made Easy\" or random financial terms\r\n   - Examples: For a restaurant: \"Fresh Daily Specials\", For a gym: \"Transform Your Body\", For a salon: \"Expert Hair Care\"\r\n   - Required for ALL posts - this is the main visual text\r\n   - Optimize for visual impact and business relevance\r\n\r\n3. **SUBHEADLINE (subheadline)** - OPTIONAL:\r\n   - Add only when it would make the post more effective\r\n   - Maximum 14 words\r\n   - Use your marketing expertise to decide when needed\r\n   - Should complement the catchy words and enhance the message\r\n   - Examples: When explaining a complex service, highlighting a special offer, or providing context\r\n   - Skip if the catchy words and caption are sufficient\r\n\r\n4. **CALL TO ACTION (callToAction)** - OPTIONAL:\r\n   - Add only when it would drive better engagement or conversions\r\n   - Use your marketing expertise to decide when needed\r\n   - Should be specific and actionable\r\n   - Examples: \"Book Now\", \"Call Today\", \"Visit Us\", \"Learn More\", \"Get Started\"\r\n   - Skip if the post is more about awareness or engagement rather than direct action\r\n\r\n5. **HASHTAGS**:\r\n   - Mix trending hashtags with niche industry tags\r\n   - Include location-specific and cultural hashtags\r\n   - Balance high-competition and low-competition tags\r\n   - Ensure cultural sensitivity and appropriateness\r\n   - Optimize quantity for platform (Instagram: 20-30, LinkedIn: 3-5, etc.)\r\n\r\n6. **CONTENT VARIANTS (contentVariants)**:\r\n   Generate 2-3 alternative approaches:\r\n\r\n   **Variant 1 - Trending Topic Angle**:\r\n   - Hook into current trending conversation\r\n   - Connect trend to business value naturally\r\n   - Use viral content patterns\r\n   - Include shareability factors\r\n\r\n   **Variant 2 - Cultural Connection Angle**:\r\n   - Start with local cultural reference\r\n   - Show deep community understanding\r\n   - Use location-specific language naturally\r\n   - Build authentic local connections\r\n\r\n   **Variant 3 - Competitor Differentiation Angle**:\r\n   - Address common industry pain points differently\r\n   - Highlight unique business approach\r\n   - Use contrarian but respectful positioning\r\n   - Show authentic expertise and experience\r\n\r\n   For each variant, provide:\r\n   - The alternative caption content\r\n   - The strategic approach used\r\n   - Why this variant will drive traffic and engagement\r\n   - Cultural sensitivity considerations\r\n\r\nQUALITY STANDARDS:\r\n- Every word serves engagement or conversion purpose\r\n- Content feels authentically human, never robotic\r\n- Cultural references are respectful and accurate\r\n- Trending topics are naturally integrated, not forced\r\n- Competitor differentiation is subtle but clear\r\n- Traffic-driving elements are seamlessly woven in\r\n- Platform optimization is invisible but effective\r\n- Local cultural nuances are appropriately honored\r\n\r\nTRAFFIC & CONVERSION OPTIMIZATION:\r\n- Include clear value proposition for audience\r\n- Create multiple engagement touchpoints\r\n- Use psychological triggers ethically\r\n- Provide shareable insights or entertainment\r\n- Include conversion pathway (comment, DM, visit, etc.)\r\n- Optimize for algorithm preferences\r\n- Encourage community building and return visits\r\n\r\nWEBSITE REFERENCE GUIDELINES:\r\n{{#if websiteUrl}}\r\n- Website available for CTAs: {{{websiteUrl}}} (use clean format without https:// or www.)\r\n- Only include website when CTA specifically calls for it (e.g., \"check us out online\", \"visit our site\")\r\n- Don't force website into every post - use contextually when it makes sense\r\n- Examples: \"Visit us online\", \"Check our website\", \"Learn more at [clean-url]\"\r\n{{else}}\r\n- No website URL provided - focus on other CTAs (DM, call, visit location)\r\n{{/if}}\r\n\r\nLANGUAGE REQUIREMENTS:\r\n🌍 TEXT CLARITY: Generate clear, readable text\r\n{{#if useLocalLanguage}}\r\n- You may use local language text when 100% certain of spelling, meaning, and cultural appropriateness\r\n- Mix local language with English naturally (1-2 local words maximum per text element)\r\n- Only use commonly known local words that add cultural connection to {{{location}}}\r\n- When uncertain about local language accuracy, use English instead\r\n- Better to use clear English than incorrect or garbled local language\r\n{{else}}\r\n- USE ONLY ENGLISH for all text content (captions, hashtags, call-to-actions)\r\n- Do not use any local language words or phrases\r\n- Keep all text elements in clear, professional English\r\n- Focus on universal messaging that works across all markets\r\n{{/if}}\r\n- Do NOT use corrupted, gibberish, or unreadable character sequences\r\n- Do NOT use random symbols or malformed text\r\n- Ensure all text is properly formatted and legible\r\n- Avoid character encoding issues or text corruption\r\n- All text must be clear and professional\r\n- Prevent any garbled or nonsensical character combinations\r\n\r\nYour response MUST be a valid JSON object that conforms to the output schema.\r\nFocus on creating content that real humans will love, share, and act upon.`;\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAEM,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0EA0QuC,CAAC", "debugId": null}}, {"offset": {"line": 1987, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/hashtag-strategy.ts"], "sourcesContent": ["/**\r\n * Advanced Hashtag Strategy Utilities\r\n * \r\n * This module provides sophisticated hashtag generation and optimization\r\n * strategies for different platforms and business types.\r\n */\r\n\r\nexport interface HashtagStrategy {\r\n  trending: string[];\r\n  niche: string[];\r\n  branded: string[];\r\n  location: string[];\r\n  community: string[];\r\n  topicSpecific: string[];\r\n  designBased: string[];\r\n}\r\n\r\nexport interface HashtagAnalysis {\r\n  hashtag: string;\r\n  category: 'trending' | 'niche' | 'branded' | 'location' | 'community';\r\n  competitionLevel: 'high' | 'medium' | 'low';\r\n  estimatedReach: 'high' | 'medium' | 'low';\r\n  relevanceScore: number; // 1-10\r\n}\r\n\r\n/**\r\n * Generates exactly 10 most relevant hashtags based on design and post topic\r\n */\r\nexport function generateHashtagStrategy(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string,\r\n  services?: string,\r\n  targetAudience?: string,\r\n  postTopic?: string,\r\n  designStyle?: string\r\n): HashtagStrategy {\r\n\r\n  // Generate all possible hashtags\r\n  const allHashtags = {\r\n    trending: generateTrendingHashtags(businessType, platform),\r\n    niche: generateNicheHashtags(businessType, services),\r\n    branded: generateBrandedHashtags(businessType),\r\n    location: generateLocationHashtags(location),\r\n    community: generateCommunityHashtags(businessType, targetAudience),\r\n    topicSpecific: generateTopicSpecificHashtags(postTopic, designStyle),\r\n    designBased: generateDesignBasedHashtags(designStyle, businessType)\r\n  };\r\n\r\n  // Select exactly 10 most relevant hashtags\r\n  return selectTop10Hashtags(allHashtags, platform, businessType, postTopic);\r\n}\r\n\r\n/**\r\n * Optimizes hashtag counts based on platform best practices\r\n */\r\nfunction optimizeHashtagsForPlatform(strategy: HashtagStrategy, platform: string): HashtagStrategy {\r\n  const platformLimits: Record<string, { trending: number; niche: number; location: number; community: number; branded: number }> = {\r\n    'instagram': { trending: 5, niche: 8, location: 4, community: 5, branded: 3 },\r\n    'linkedin': { trending: 2, niche: 2, location: 1, community: 1, branded: 1 },\r\n    'twitter': { trending: 2, niche: 1, location: 0, community: 0, branded: 0 },\r\n    'facebook': { trending: 2, niche: 3, location: 2, community: 2, branded: 1 }\r\n  };\r\n\r\n  const limits = platformLimits[platform.toLowerCase()] || { trending: 3, niche: 5, location: 3, community: 3, branded: 2 };\r\n\r\n  return {\r\n    trending: strategy.trending.slice(0, limits.trending),\r\n    niche: strategy.niche.slice(0, limits.niche),\r\n    location: strategy.location.slice(0, limits.location),\r\n    community: strategy.community.slice(0, limits.community),\r\n    branded: strategy.branded.slice(0, limits.branded),\r\n    topicSpecific: strategy.topicSpecific || [],\r\n    designBased: strategy.designBased || []\r\n  };\r\n}\r\n\r\n/**\r\n * Generates trending hashtags based on business type and platform\r\n */\r\nfunction generateTrendingHashtags(businessType: string, platform: string): string[] {\r\n  const businessTypeMap: Record<string, string[]> = {\r\n    'restaurant': ['#foodie', '#delicious', '#foodstagram', '#yummy', '#tasty'],\r\n    'fitness': ['#fitness', '#workout', '#health', '#motivation', '#fitlife'],\r\n    'beauty': ['#beauty', '#skincare', '#makeup', '#selfcare', '#glowup'],\r\n    'retail': ['#shopping', '#style', '#fashion', '#deals', '#newcollection'],\r\n    'technology': ['#tech', '#innovation', '#digital', '#future', '#startup'],\r\n    'healthcare': ['#health', '#wellness', '#care', '#medical', '#healthy'],\r\n    'education': ['#education', '#learning', '#knowledge', '#skills', '#growth'],\r\n    'real_estate': ['#realestate', '#home', '#property', '#investment', '#dreamhome'],\r\n    'automotive': ['#cars', '#automotive', '#driving', '#vehicle', '#auto'],\r\n    'travel': ['#travel', '#adventure', '#explore', '#wanderlust', '#vacation']\r\n  };\r\n\r\n  const platformTrending: Record<string, string[]> = {\r\n    'instagram': ['#instagood', '#photooftheday', '#love', '#beautiful', '#happy', '#instadaily', '#follow', '#like4like'],\r\n    'linkedin': ['#professional', '#business', '#career', '#networking', '#success', '#leadership', '#innovation', '#growth'],\r\n    'twitter': ['#trending', '#viral', '#breaking', '#news', '#update', '#thread', '#twitterchat', '#follow'],\r\n    'facebook': ['#community', '#local', '#family', '#friends', '#share', '#like', '#comment', '#engage']\r\n  };\r\n\r\n  const businessHashtags = businessTypeMap[businessType.toLowerCase()] || ['#business', '#service', '#quality', '#professional', '#local'];\r\n  const platformHashtags = platformTrending[platform.toLowerCase()] || ['#social', '#content', '#engagement'];\r\n\r\n  // Shuffle to ensure variety in each generation\r\n  const shuffledBusiness = businessHashtags.sort(() => 0.5 - Math.random());\r\n  const shuffledPlatform = platformHashtags.sort(() => 0.5 - Math.random());\r\n\r\n  return [...shuffledBusiness.slice(0, 3), ...shuffledPlatform.slice(0, 2)];\r\n}\r\n\r\n/**\r\n * Generates niche-specific hashtags\r\n */\r\nfunction generateNicheHashtags(businessType: string, services?: string): string[] {\r\n  const nicheMap: Record<string, string[]> = {\r\n    'food production': ['#foodproduction', '#nutrition', '#healthyfood', '#manufacturing', '#qualitycontrol', '#foodsafety', '#sustainable', '#organic', '#processing', '#ingredients', '#nutritious', '#wholesome'],\r\n    'restaurant': ['#localfood', '#chefspecial', '#freshingredients', '#culinaryart', '#foodculture', '#diningexperience', '#artisanfood', '#farmtotable'],\r\n    'fitness': ['#personaltrainer', '#strengthtraining', '#cardio', '#nutrition', '#bodybuilding', '#crossfit', '#yoga', '#pilates'],\r\n    'beauty': ['#beautytips', '#skincareroutine', '#makeuptutorial', '#beautyproducts', '#antiaging', '#naturalbeauty', '#beautysalon', '#spa'],\r\n    'retail': ['#boutique', '#handmade', '#unique', '#quality', '#craftsmanship', '#designer', '#exclusive', '#limited'],\r\n    'technology': ['#software', '#AI', '#machinelearning', '#cybersecurity', '#cloudcomputing', '#blockchain', '#IoT', '#automation'],\r\n    'healthcare': ['#preventivecare', '#patientcare', '#medicaladvice', '#healthtips', '#wellness', '#mentalhealth', '#nutrition', '#exercise'],\r\n    'education': ['#onlinelearning', '#skillbuilding', '#certification', '#training', '#development', '#mentorship', '#coaching', '#academy'],\r\n    'real_estate': ['#propertyinvestment', '#homebuying', '#realtorlife', '#propertymanagement', '#commercialrealestate', '#luxury', '#firsttimehomebuyer'],\r\n    'automotive': ['#carcare', '#automotive', '#mechanic', '#carrepair', '#maintenance', '#performance', '#luxury', '#electric'],\r\n    'travel': ['#localtourism', '#hiddengems', '#culturalexperience', '#adventure', '#ecotourism', '#luxurytravel', '#backpacking', '#roadtrip']\r\n  };\r\n\r\n  const baseNiche = nicheMap[businessType.toLowerCase()] || ['#specialized', '#expert', '#professional', '#quality', '#service', '#local', '#trusted', '#experienced'];\r\n\r\n  // Shuffle the base niche hashtags to ensure variety\r\n  const shuffledNiche = baseNiche.sort(() => 0.5 - Math.random());\r\n\r\n  // Add service-specific hashtags if services are provided\r\n  if (services) {\r\n    const serviceWords = services.toLowerCase().split(/[,\\s]+/).filter(word => word.length > 3);\r\n    const serviceHashtags = serviceWords.slice(0, 3).map(word => `#${word.replace(/[^a-z0-9]/g, '')}`);\r\n    return [...shuffledNiche.slice(0, 5), ...serviceHashtags];\r\n  }\r\n\r\n  return shuffledNiche.slice(0, 8);\r\n}\r\n\r\n/**\r\n * Generates branded hashtags\r\n */\r\nfunction generateBrandedHashtags(businessType: string): string[] {\r\n  const brandedSuffixes = ['experience', 'quality', 'service', 'difference', 'way', 'style', 'approach'];\r\n  const businessPrefix = businessType.toLowerCase().replace(/[^a-z]/g, '');\r\n\r\n  return [\r\n    `#${businessPrefix}${brandedSuffixes[0]}`,\r\n    `#${businessPrefix}${brandedSuffixes[1]}`,\r\n    `#choose${businessPrefix}`\r\n  ];\r\n}\r\n\r\n/**\r\n * Generates location-based hashtags\r\n */\r\nfunction generateLocationHashtags(location: string): string[] {\r\n  const locationParts = location.split(',').map(part => part.trim());\r\n  const hashtags: string[] = [];\r\n\r\n  locationParts.forEach(part => {\r\n    const cleanLocation = part.replace(/[^a-zA-Z0-9\\s]/g, '').replace(/\\s+/g, '').toLowerCase();\r\n    if (cleanLocation.length > 2) {\r\n      hashtags.push(`#${cleanLocation}`);\r\n      hashtags.push(`#local${cleanLocation}`);\r\n      hashtags.push(`#${cleanLocation}business`);\r\n    }\r\n  });\r\n\r\n  // Add generic location hashtags\r\n  hashtags.push('#local', '#community', '#neighborhood');\r\n\r\n  return hashtags.slice(0, 5);\r\n}\r\n\r\n/**\r\n * Generates community and engagement hashtags\r\n */\r\nfunction generateCommunityHashtags(businessType: string, targetAudience?: string): string[] {\r\n  const communityMap: Record<string, string[]> = {\r\n    'restaurant': ['#foodlovers', '#foodies', '#localfoodie', '#foodcommunity'],\r\n    'fitness': ['#fitnesscommunity', '#healthylifestyle', '#fitnessjourney', '#workoutbuddy'],\r\n    'beauty': ['#beautycommunity', '#selfcare', '#beautylovers', '#skincarecommunity'],\r\n    'retail': ['#shoplocal', '#supportlocal', '#shoppingcommunity', '#stylelovers'],\r\n    'technology': ['#techcommunity', '#developers', '#innovation', '#digitaltransformation'],\r\n    'healthcare': ['#healthcommunity', '#wellness', '#patientcare', '#healthylife'],\r\n    'education': ['#learningcommunity', '#students', '#educators', '#knowledge'],\r\n    'real_estate': ['#homeowners', '#investors', '#realestatecommunity', '#propertylovers'],\r\n    'automotive': ['#carenthusiasts', '#automotive', '#carlovers', '#drivingcommunity'],\r\n    'travel': ['#travelers', '#explorers', '#adventurers', '#wanderers']\r\n  };\r\n\r\n  const baseCommunity = communityMap[businessType.toLowerCase()] || ['#community', '#customers', '#supporters', '#family'];\r\n\r\n  // Add audience-specific hashtags if provided\r\n  if (targetAudience) {\r\n    const audienceWords = targetAudience.toLowerCase().split(/[,\\s]+/).filter(word => word.length > 3);\r\n    const audienceHashtags = audienceWords.slice(0, 2).map(word => `#${word.replace(/[^a-z0-9]/g, '')}`);\r\n    return [...baseCommunity.slice(0, 3), ...audienceHashtags];\r\n  }\r\n\r\n  return baseCommunity.slice(0, 4);\r\n}\r\n\r\n/**\r\n * Analyzes hashtag effectiveness\r\n */\r\nexport function analyzeHashtags(hashtags: string[]): HashtagAnalysis[] {\r\n  return hashtags.map(hashtag => ({\r\n    hashtag,\r\n    category: categorizeHashtag(hashtag),\r\n    competitionLevel: estimateCompetition(hashtag),\r\n    estimatedReach: estimateReach(hashtag),\r\n    relevanceScore: Math.floor(Math.random() * 3) + 8 // Simplified scoring\r\n  }));\r\n}\r\n\r\nfunction categorizeHashtag(hashtag: string): HashtagAnalysis['category'] {\r\n  const trending = ['#instagood', '#photooftheday', '#love', '#beautiful', '#happy', '#fitness', '#food'];\r\n  const location = hashtag.includes('local') || hashtag.includes('community');\r\n  const branded = hashtag.includes('experience') || hashtag.includes('quality');\r\n\r\n  if (trending.some(t => hashtag.includes(t.slice(1)))) return 'trending';\r\n  if (location) return 'location';\r\n  if (branded) return 'branded';\r\n  return 'niche';\r\n}\r\n\r\nfunction estimateCompetition(hashtag: string): 'high' | 'medium' | 'low' {\r\n  const highCompetition = ['#love', '#instagood', '#photooftheday', '#beautiful', '#happy'];\r\n  const lowCompetition = hashtag.length > 15 || hashtag.includes('local');\r\n\r\n  if (highCompetition.includes(hashtag)) return 'high';\r\n  if (lowCompetition) return 'low';\r\n  return 'medium';\r\n}\r\n\r\nfunction estimateReach(hashtag: string): 'high' | 'medium' | 'low' {\r\n  const highReach = ['#love', '#instagood', '#photooftheday', '#beautiful', '#happy'];\r\n  const lowReach = hashtag.length > 15 || hashtag.includes('local');\r\n\r\n  if (highReach.includes(hashtag)) return 'high';\r\n  if (lowReach) return 'low';\r\n  return 'medium';\r\n}\r\n\r\n/**\r\n * Generates hashtags based on specific post topic and content\r\n */\r\nfunction generateTopicSpecificHashtags(postTopic?: string, designStyle?: string): string[] {\r\n  if (!postTopic) return [];\r\n\r\n  const postContent = postTopic.toLowerCase();\r\n  const hashtags: string[] = [];\r\n\r\n  // Content-based hashtag detection\r\n  const contentKeywords = {\r\n    // Food & Nutrition\r\n    'food': ['#food', '#nutrition', '#healthy', '#delicious', '#fresh'],\r\n    'cookie': ['#cookies', '#snacks', '#treats', '#baked', '#homemade'],\r\n    'nutritious': ['#nutritious', '#healthy', '#wellness', '#goodforyou', '#natural'],\r\n    'malnutrition': ['#nutrition', '#health', '#wellness', '#community', '#impact'],\r\n\r\n    // Business actions\r\n    'sale': ['#sale', '#discount', '#offer', '#deal', '#savings'],\r\n    'new': ['#new', '#launch', '#fresh', '#latest', '#innovation'],\r\n    'quality': ['#quality', '#premium', '#excellence', '#trusted', '#reliable'],\r\n    'service': ['#service', '#professional', '#expert', '#specialized', '#care'],\r\n\r\n    // Emotions & Values\r\n    'future': ['#future', '#tomorrow', '#progress', '#growth', '#vision'],\r\n    'fighting': ['#fighting', '#mission', '#purpose', '#impact', '#change'],\r\n    'learn': ['#learn', '#education', '#knowledge', '#discover', '#grow'],\r\n    'experience': ['#experience', '#expertise', '#skilled', '#proven', '#established'],\r\n\r\n    // Industry specific\r\n    'production': ['#production', '#manufacturing', '#quality', '#process', '#industry'],\r\n    'technology': ['#technology', '#innovation', '#digital', '#modern', '#advanced'],\r\n    'consulting': ['#consulting', '#advisory', '#expertise', '#solutions', '#strategy'],\r\n    'retail': ['#retail', '#shopping', '#products', '#customer', '#store']\r\n  };\r\n\r\n  // Extract relevant hashtags based on content\r\n  Object.entries(contentKeywords).forEach(([keyword, tags]) => {\r\n    if (postContent.includes(keyword)) {\r\n      hashtags.push(...tags.slice(0, 2)); // Take first 2 from each matching category\r\n    }\r\n  });\r\n\r\n  // Add random variation to avoid repetition\r\n  const randomVariations = [\r\n    '#amazing', '#incredible', '#outstanding', '#exceptional', '#remarkable',\r\n    '#innovative', '#creative', '#unique', '#special', '#exclusive',\r\n    '#authentic', '#genuine', '#original', '#custom', '#personalized',\r\n    '#sustainable', '#eco', '#green', '#responsible', '#ethical'\r\n  ];\r\n\r\n  // Add 1-2 random variations for uniqueness\r\n  const shuffled = randomVariations.sort(() => 0.5 - Math.random());\r\n  hashtags.push(...shuffled.slice(0, 2));\r\n\r\n  return [...new Set(hashtags)]; // Remove duplicates\r\n}\r\n\r\n/**\r\n * Generates hashtags based on design style and visual elements\r\n */\r\nfunction generateDesignBasedHashtags(designStyle?: string, businessType?: string): string[] {\r\n  if (!designStyle) return [];\r\n\r\n  const designMap: Record<string, string[]> = {\r\n    'modern': ['#modern', '#clean', '#minimal', '#sleek', '#contemporary'],\r\n    'vintage': ['#vintage', '#retro', '#classic', '#timeless', '#nostalgic'],\r\n    'bold': ['#bold', '#vibrant', '#striking', '#powerful', '#dynamic'],\r\n    'elegant': ['#elegant', '#sophisticated', '#luxury', '#premium', '#refined'],\r\n    'playful': ['#playful', '#fun', '#creative', '#colorful', '#energetic'],\r\n    'minimalist': ['#minimalist', '#simple', '#clean', '#pure', '#essential'],\r\n    'professional': ['#professional', '#corporate', '#business', '#formal', '#polished'],\r\n    'artistic': ['#artistic', '#creative', '#design', '#art', '#visual'],\r\n    'trendy': ['#trendy', '#stylish', '#fashionable', '#current', '#hip'],\r\n    'warm': ['#warm', '#cozy', '#inviting', '#friendly', '#welcoming']\r\n  };\r\n\r\n  // Find matching design style\r\n  const style = Object.keys(designMap).find(key =>\r\n    designStyle.toLowerCase().includes(key)\r\n  );\r\n\r\n  return style ? designMap[style] : [];\r\n}\r\n\r\n/**\r\n * Selects exactly 10 most relevant hashtags from all categories\r\n */\r\nfunction selectTop10Hashtags(\r\n  allHashtags: HashtagStrategy & { topicSpecific: string[]; designBased: string[] },\r\n  platform: string,\r\n  businessType: string,\r\n  postTopic?: string\r\n): HashtagStrategy {\r\n  // Use timestamp for randomization seed to ensure different results each time\r\n  const randomSeed = Date.now() % 1000;\r\n\r\n  // Priority weights for different categories\r\n  const categoryWeights = {\r\n    topicSpecific: 3.0,    // Highest priority - directly related to post content\r\n    designBased: 2.5,      // High priority - matches visual style\r\n    niche: 2.0,           // High priority - business-specific\r\n    branded: 1.8,         // Good priority - brand building\r\n    trending: 1.5,        // Medium priority - reach\r\n    location: 1.3,        // Medium priority - local relevance\r\n    community: 1.0        // Lower priority - general engagement\r\n  };\r\n\r\n  // Collect all hashtags with scores and add randomization\r\n  const scoredHashtags: Array<{ hashtag: string; score: number; category: string }> = [];\r\n\r\n  Object.entries(allHashtags).forEach(([category, hashtags]) => {\r\n    const weight = categoryWeights[category as keyof typeof categoryWeights] || 1.0;\r\n    hashtags.forEach((hashtag, index) => {\r\n      // Avoid duplicates\r\n      if (!scoredHashtags.some(item => item.hashtag === hashtag)) {\r\n        // Add slight randomization to score to ensure variety\r\n        const randomBonus = (randomSeed + index) % 100 / 1000;\r\n        scoredHashtags.push({\r\n          hashtag,\r\n          score: weight + randomBonus,\r\n          category\r\n        });\r\n      }\r\n    });\r\n  });\r\n\r\n  // Sort by score (highest first) and take top 10\r\n  const top10 = scoredHashtags\r\n    .sort((a, b) => b.score - a.score)\r\n    .slice(0, 10)\r\n    .map(item => item.hashtag);\r\n\r\n  // Return in the expected format (distribute across categories for compatibility)\r\n  return {\r\n    trending: top10.slice(0, 3),\r\n    niche: top10.slice(3, 6),\r\n    branded: top10.slice(6, 8),\r\n    location: top10.slice(8, 9),\r\n    community: top10.slice(9, 10),\r\n    topicSpecific: [],\r\n    designBased: []\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAuBM,SAAS,wBACd,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,QAAiB,EACjB,cAAuB,EACvB,SAAkB,EAClB,WAAoB;IAGpB,iCAAiC;IACjC,MAAM,cAAc;QAClB,UAAU,yBAAyB,cAAc;QACjD,OAAO,sBAAsB,cAAc;QAC3C,SAAS,wBAAwB;QACjC,UAAU,yBAAyB;QACnC,WAAW,0BAA0B,cAAc;QACnD,eAAe,8BAA8B,WAAW;QACxD,aAAa,4BAA4B,aAAa;IACxD;IAEA,2CAA2C;IAC3C,OAAO,oBAAoB,aAAa,UAAU,cAAc;AAClE;AAEA;;CAEC,GACD,SAAS,4BAA4B,QAAyB,EAAE,QAAgB;IAC9E,MAAM,iBAA4H;QAChI,aAAa;YAAE,UAAU;YAAG,OAAO;YAAG,UAAU;YAAG,WAAW;YAAG,SAAS;QAAE;QAC5E,YAAY;YAAE,UAAU;YAAG,OAAO;YAAG,UAAU;YAAG,WAAW;YAAG,SAAS;QAAE;QAC3E,WAAW;YAAE,UAAU;YAAG,OAAO;YAAG,UAAU;YAAG,WAAW;YAAG,SAAS;QAAE;QAC1E,YAAY;YAAE,UAAU;YAAG,OAAO;YAAG,UAAU;YAAG,WAAW;YAAG,SAAS;QAAE;IAC7E;IAEA,MAAM,SAAS,cAAc,CAAC,SAAS,WAAW,GAAG,IAAI;QAAE,UAAU;QAAG,OAAO;QAAG,UAAU;QAAG,WAAW;QAAG,SAAS;IAAE;IAExH,OAAO;QACL,UAAU,SAAS,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,QAAQ;QACpD,OAAO,SAAS,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,KAAK;QAC3C,UAAU,SAAS,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,QAAQ;QACpD,WAAW,SAAS,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO,SAAS;QACvD,SAAS,SAAS,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,OAAO;QACjD,eAAe,SAAS,aAAa,IAAI,EAAE;QAC3C,aAAa,SAAS,WAAW,IAAI,EAAE;IACzC;AACF;AAEA;;CAEC,GACD,SAAS,yBAAyB,YAAoB,EAAE,QAAgB;IACtE,MAAM,kBAA4C;QAChD,cAAc;YAAC;YAAW;YAAc;YAAgB;YAAU;SAAS;QAC3E,WAAW;YAAC;YAAY;YAAY;YAAW;YAAe;SAAW;QACzE,UAAU;YAAC;YAAW;YAAa;YAAW;YAAa;SAAU;QACrE,UAAU;YAAC;YAAa;YAAU;YAAY;YAAU;SAAiB;QACzE,cAAc;YAAC;YAAS;YAAe;YAAY;YAAW;SAAW;QACzE,cAAc;YAAC;YAAW;YAAa;YAAS;YAAY;SAAW;QACvE,aAAa;YAAC;YAAc;YAAa;YAAc;YAAW;SAAU;QAC5E,eAAe;YAAC;YAAe;YAAS;YAAa;YAAe;SAAa;QACjF,cAAc;YAAC;YAAS;YAAe;YAAY;YAAY;SAAQ;QACvE,UAAU;YAAC;YAAW;YAAc;YAAY;YAAe;SAAY;IAC7E;IAEA,MAAM,mBAA6C;QACjD,aAAa;YAAC;YAAc;YAAkB;YAAS;YAAc;YAAU;YAAe;YAAW;SAAa;QACtH,YAAY;YAAC;YAAiB;YAAa;YAAW;YAAe;YAAY;YAAe;YAAe;SAAU;QACzH,WAAW;YAAC;YAAa;YAAU;YAAa;YAAS;YAAW;YAAW;YAAgB;SAAU;QACzG,YAAY;YAAC;YAAc;YAAU;YAAW;YAAY;YAAU;YAAS;YAAY;SAAU;IACvG;IAEA,MAAM,mBAAmB,eAAe,CAAC,aAAa,WAAW,GAAG,IAAI;QAAC;QAAa;QAAY;QAAY;QAAiB;KAAS;IACxI,MAAM,mBAAmB,gBAAgB,CAAC,SAAS,WAAW,GAAG,IAAI;QAAC;QAAW;QAAY;KAAc;IAE3G,+CAA+C;IAC/C,MAAM,mBAAmB,iBAAiB,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IACtE,MAAM,mBAAmB,iBAAiB,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IAEtE,OAAO;WAAI,iBAAiB,KAAK,CAAC,GAAG;WAAO,iBAAiB,KAAK,CAAC,GAAG;KAAG;AAC3E;AAEA;;CAEC,GACD,SAAS,sBAAsB,YAAoB,EAAE,QAAiB;IACpE,MAAM,WAAqC;QACzC,mBAAmB;YAAC;YAAmB;YAAc;YAAgB;YAAkB;YAAmB;YAAe;YAAgB;YAAY;YAAe;YAAgB;YAAe;SAAa;QAChN,cAAc;YAAC;YAAc;YAAgB;YAAqB;YAAgB;YAAgB;YAAqB;YAAgB;SAAe;QACtJ,WAAW;YAAC;YAAoB;YAAqB;YAAW;YAAc;YAAiB;YAAa;YAAS;SAAW;QAChI,UAAU;YAAC;YAAe;YAAoB;YAAmB;YAAmB;YAAc;YAAkB;YAAgB;SAAO;QAC3I,UAAU;YAAC;YAAa;YAAa;YAAW;YAAY;YAAkB;YAAa;YAAc;SAAW;QACpH,cAAc;YAAC;YAAa;YAAO;YAAoB;YAAkB;YAAmB;YAAe;YAAQ;SAAc;QACjI,cAAc;YAAC;YAAmB;YAAgB;YAAkB;YAAe;YAAa;YAAiB;YAAc;SAAY;QAC3I,aAAa;YAAC;YAAmB;YAAkB;YAAkB;YAAa;YAAgB;YAAe;YAAa;SAAW;QACzI,eAAe;YAAC;YAAuB;YAAe;YAAgB;YAAuB;YAAyB;YAAW;SAAsB;QACvJ,cAAc;YAAC;YAAY;YAAe;YAAa;YAAc;YAAgB;YAAgB;YAAW;SAAY;QAC5H,UAAU;YAAC;YAAiB;YAAe;YAAuB;YAAc;YAAe;YAAiB;YAAgB;SAAY;IAC9I;IAEA,MAAM,YAAY,QAAQ,CAAC,aAAa,WAAW,GAAG,IAAI;QAAC;QAAgB;QAAW;QAAiB;QAAY;QAAY;QAAU;QAAY;KAAe;IAEpK,oDAAoD;IACpD,MAAM,gBAAgB,UAAU,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IAE5D,yDAAyD;IACzD,IAAI,UAAU;QACZ,MAAM,eAAe,SAAS,WAAW,GAAG,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;QACzF,MAAM,kBAAkB,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,cAAc,KAAK;QACjG,OAAO;eAAI,cAAc,KAAK,CAAC,GAAG;eAAO;SAAgB;IAC3D;IAEA,OAAO,cAAc,KAAK,CAAC,GAAG;AAChC;AAEA;;CAEC,GACD,SAAS,wBAAwB,YAAoB;IACnD,MAAM,kBAAkB;QAAC;QAAc;QAAW;QAAW;QAAc;QAAO;QAAS;KAAW;IACtG,MAAM,iBAAiB,aAAa,WAAW,GAAG,OAAO,CAAC,WAAW;IAErE,OAAO;QACL,CAAC,CAAC,EAAE,iBAAiB,eAAe,CAAC,EAAE,EAAE;QACzC,CAAC,CAAC,EAAE,iBAAiB,eAAe,CAAC,EAAE,EAAE;QACzC,CAAC,OAAO,EAAE,gBAAgB;KAC3B;AACH;AAEA;;CAEC,GACD,SAAS,yBAAyB,QAAgB;IAChD,MAAM,gBAAgB,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;IAC/D,MAAM,WAAqB,EAAE;IAE7B,cAAc,OAAO,CAAC,CAAA;QACpB,MAAM,gBAAgB,KAAK,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,QAAQ,IAAI,WAAW;QACzF,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,eAAe;YACjC,SAAS,IAAI,CAAC,CAAC,MAAM,EAAE,eAAe;YACtC,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,QAAQ,CAAC;QAC3C;IACF;IAEA,gCAAgC;IAChC,SAAS,IAAI,CAAC,UAAU,cAAc;IAEtC,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAEA;;CAEC,GACD,SAAS,0BAA0B,YAAoB,EAAE,cAAuB;IAC9E,MAAM,eAAyC;QAC7C,cAAc;YAAC;YAAe;YAAY;YAAgB;SAAiB;QAC3E,WAAW;YAAC;YAAqB;YAAqB;YAAmB;SAAgB;QACzF,UAAU;YAAC;YAAoB;YAAa;YAAiB;SAAqB;QAClF,UAAU;YAAC;YAAc;YAAiB;YAAsB;SAAe;QAC/E,cAAc;YAAC;YAAkB;YAAe;YAAe;SAAyB;QACxF,cAAc;YAAC;YAAoB;YAAa;YAAgB;SAAe;QAC/E,aAAa;YAAC;YAAsB;YAAa;YAAc;SAAa;QAC5E,eAAe;YAAC;YAAe;YAAc;YAAwB;SAAkB;QACvF,cAAc;YAAC;YAAmB;YAAe;YAAc;SAAoB;QACnF,UAAU;YAAC;YAAc;YAAc;YAAgB;SAAa;IACtE;IAEA,MAAM,gBAAgB,YAAY,CAAC,aAAa,WAAW,GAAG,IAAI;QAAC;QAAc;QAAc;QAAe;KAAU;IAExH,6CAA6C;IAC7C,IAAI,gBAAgB;QAClB,MAAM,gBAAgB,eAAe,WAAW,GAAG,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;QAChG,MAAM,mBAAmB,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,cAAc,KAAK;QACnG,OAAO;eAAI,cAAc,KAAK,CAAC,GAAG;eAAO;SAAiB;IAC5D;IAEA,OAAO,cAAc,KAAK,CAAC,GAAG;AAChC;AAKO,SAAS,gBAAgB,QAAkB;IAChD,OAAO,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;YAC9B;YACA,UAAU,kBAAkB;YAC5B,kBAAkB,oBAAoB;YACtC,gBAAgB,cAAc;YAC9B,gBAAgB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,EAAE,qBAAqB;QACzE,CAAC;AACH;AAEA,SAAS,kBAAkB,OAAe;IACxC,MAAM,WAAW;QAAC;QAAc;QAAkB;QAAS;QAAc;QAAU;QAAY;KAAQ;IACvG,MAAM,WAAW,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC;IAC/D,MAAM,UAAU,QAAQ,QAAQ,CAAC,iBAAiB,QAAQ,QAAQ,CAAC;IAEnE,IAAI,SAAS,IAAI,CAAC,CAAA,IAAK,QAAQ,QAAQ,CAAC,EAAE,KAAK,CAAC,MAAM,OAAO;IAC7D,IAAI,UAAU,OAAO;IACrB,IAAI,SAAS,OAAO;IACpB,OAAO;AACT;AAEA,SAAS,oBAAoB,OAAe;IAC1C,MAAM,kBAAkB;QAAC;QAAS;QAAc;QAAkB;QAAc;KAAS;IACzF,MAAM,iBAAiB,QAAQ,MAAM,GAAG,MAAM,QAAQ,QAAQ,CAAC;IAE/D,IAAI,gBAAgB,QAAQ,CAAC,UAAU,OAAO;IAC9C,IAAI,gBAAgB,OAAO;IAC3B,OAAO;AACT;AAEA,SAAS,cAAc,OAAe;IACpC,MAAM,YAAY;QAAC;QAAS;QAAc;QAAkB;QAAc;KAAS;IACnF,MAAM,WAAW,QAAQ,MAAM,GAAG,MAAM,QAAQ,QAAQ,CAAC;IAEzD,IAAI,UAAU,QAAQ,CAAC,UAAU,OAAO;IACxC,IAAI,UAAU,OAAO;IACrB,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,8BAA8B,SAAkB,EAAE,WAAoB;IAC7E,IAAI,CAAC,WAAW,OAAO,EAAE;IAEzB,MAAM,cAAc,UAAU,WAAW;IACzC,MAAM,WAAqB,EAAE;IAE7B,kCAAkC;IAClC,MAAM,kBAAkB;QACtB,mBAAmB;QACnB,QAAQ;YAAC;YAAS;YAAc;YAAY;YAAc;SAAS;QACnE,UAAU;YAAC;YAAY;YAAW;YAAW;YAAU;SAAY;QACnE,cAAc;YAAC;YAAe;YAAY;YAAa;YAAe;SAAW;QACjF,gBAAgB;YAAC;YAAc;YAAW;YAAa;YAAc;SAAU;QAE/E,mBAAmB;QACnB,QAAQ;YAAC;YAAS;YAAa;YAAU;YAAS;SAAW;QAC7D,OAAO;YAAC;YAAQ;YAAW;YAAU;YAAW;SAAc;QAC9D,WAAW;YAAC;YAAY;YAAY;YAAe;YAAY;SAAY;QAC3E,WAAW;YAAC;YAAY;YAAiB;YAAW;YAAgB;SAAQ;QAE5E,oBAAoB;QACpB,UAAU;YAAC;YAAW;YAAa;YAAa;YAAW;SAAU;QACrE,YAAY;YAAC;YAAa;YAAY;YAAY;YAAW;SAAU;QACvE,SAAS;YAAC;YAAU;YAAc;YAAc;YAAa;SAAQ;QACrE,cAAc;YAAC;YAAe;YAAc;YAAY;YAAW;SAAe;QAElF,oBAAoB;QACpB,cAAc;YAAC;YAAe;YAAkB;YAAY;YAAY;SAAY;QACpF,cAAc;YAAC;YAAe;YAAe;YAAY;YAAW;SAAY;QAChF,cAAc;YAAC;YAAe;YAAa;YAAc;YAAc;SAAY;QACnF,UAAU;YAAC;YAAW;YAAa;YAAa;YAAa;SAAS;IACxE;IAEA,6CAA6C;IAC7C,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,SAAS,KAAK;QACtD,IAAI,YAAY,QAAQ,CAAC,UAAU;YACjC,SAAS,IAAI,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,2CAA2C;QACjF;IACF;IAEA,2CAA2C;IAC3C,MAAM,mBAAmB;QACvB;QAAY;QAAe;QAAgB;QAAgB;QAC3D;QAAe;QAAa;QAAW;QAAY;QACnD;QAAc;QAAY;QAAa;QAAW;QAClD;QAAgB;QAAQ;QAAU;QAAgB;KACnD;IAED,2CAA2C;IAC3C,MAAM,WAAW,iBAAiB,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IAC9D,SAAS,IAAI,IAAI,SAAS,KAAK,CAAC,GAAG;IAEnC,OAAO;WAAI,IAAI,IAAI;KAAU,EAAE,oBAAoB;AACrD;AAEA;;CAEC,GACD,SAAS,4BAA4B,WAAoB,EAAE,YAAqB;IAC9E,IAAI,CAAC,aAAa,OAAO,EAAE;IAE3B,MAAM,YAAsC;QAC1C,UAAU;YAAC;YAAW;YAAU;YAAY;YAAU;SAAgB;QACtE,WAAW;YAAC;YAAY;YAAU;YAAY;YAAa;SAAa;QACxE,QAAQ;YAAC;YAAS;YAAY;YAAa;YAAa;SAAW;QACnE,WAAW;YAAC;YAAY;YAAkB;YAAW;YAAY;SAAW;QAC5E,WAAW;YAAC;YAAY;YAAQ;YAAa;YAAa;SAAa;QACvE,cAAc;YAAC;YAAe;YAAW;YAAU;YAAS;SAAa;QACzE,gBAAgB;YAAC;YAAiB;YAAc;YAAa;YAAW;SAAY;QACpF,YAAY;YAAC;YAAa;YAAa;YAAW;YAAQ;SAAU;QACpE,UAAU;YAAC;YAAW;YAAY;YAAgB;YAAY;SAAO;QACrE,QAAQ;YAAC;YAAS;YAAS;YAAa;YAAa;SAAa;IACpE;IAEA,6BAA6B;IAC7B,MAAM,QAAQ,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,CAAA,MACxC,YAAY,WAAW,GAAG,QAAQ,CAAC;IAGrC,OAAO,QAAQ,SAAS,CAAC,MAAM,GAAG,EAAE;AACtC;AAEA;;CAEC,GACD,SAAS,oBACP,WAAiF,EACjF,QAAgB,EAChB,YAAoB,EACpB,SAAkB;IAElB,6EAA6E;IAC7E,MAAM,aAAa,KAAK,GAAG,KAAK;IAEhC,4CAA4C;IAC5C,MAAM,kBAAkB;QACtB,eAAe;QACf,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW,IAAW,sCAAsC;IAC9D;IAEA,yDAAyD;IACzD,MAAM,iBAA8E,EAAE;IAEtF,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,UAAU,SAAS;QACvD,MAAM,SAAS,eAAe,CAAC,SAAyC,IAAI;QAC5E,SAAS,OAAO,CAAC,CAAC,SAAS;YACzB,mBAAmB;YACnB,IAAI,CAAC,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK,UAAU;gBAC1D,sDAAsD;gBACtD,MAAM,cAAc,CAAC,aAAa,KAAK,IAAI,MAAM;gBACjD,eAAe,IAAI,CAAC;oBAClB;oBACA,OAAO,SAAS;oBAChB;gBACF;YACF;QACF;IACF;IAEA,gDAAgD;IAChD,MAAM,QAAQ,eACX,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO;IAE3B,iFAAiF;IACjF,OAAO;QACL,UAAU,MAAM,KAAK,CAAC,GAAG;QACzB,OAAO,MAAM,KAAK,CAAC,GAAG;QACtB,SAAS,MAAM,KAAK,CAAC,GAAG;QACxB,UAAU,MAAM,KAAK,CAAC,GAAG;QACzB,WAAW,MAAM,KAAK,CAAC,GAAG;QAC1B,eAAe,EAAE;QACjB,aAAa,EAAE;IACjB;AACF", "debugId": null}}, {"offset": {"line": 2800, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/real-time-trends-integration.ts"], "sourcesContent": ["/**\r\n * Real-Time Trends Integration System\r\n * \r\n * This module integrates multiple real-time trending topic sources\r\n * and provides a unified interface for getting current trends.\r\n */\r\n\r\nexport interface TrendingTopicSource {\r\n  name: string;\r\n  enabled: boolean;\r\n  apiKey?: string;\r\n  baseUrl?: string;\r\n  rateLimitPerHour: number;\r\n}\r\n\r\nexport interface LocalContext {\r\n  weather?: {\r\n    temperature: number;\r\n    condition: string;\r\n    business_impact: string;\r\n    content_opportunities: string[];\r\n  };\r\n  events?: Array<{\r\n    name: string;\r\n    category: string;\r\n    relevance_score: number;\r\n    start_date: string;\r\n  }>;\r\n}\r\n\r\nexport interface RealTimeTrendingConfig {\r\n  sources: {\r\n    googleTrends: TrendingTopicSource;\r\n    twitterApi: TrendingTopicSource;\r\n    newsApi: TrendingTopicSource;\r\n    redditApi: TrendingTopicSource;\r\n    youtubeApi: TrendingTopicSource;\r\n    eventbriteApi: TrendingTopicSource;\r\n    openWeatherApi: TrendingTopicSource;\r\n  };\r\n  fallbackToStatic: boolean;\r\n  cacheTimeMinutes: number;\r\n}\r\n\r\n/**\r\n * Configuration for real-time trending topics\r\n * Add your API keys to environment variables\r\n */\r\nexport const TRENDING_CONFIG: RealTimeTrendingConfig = {\r\n  sources: {\r\n    googleTrends: {\r\n      name: 'Google Trends RSS',\r\n      enabled: process.env.GOOGLE_TRENDS_RSS_ENABLED === 'true',\r\n      apiKey: undefined, // RSS doesn't need API key\r\n      baseUrl: 'https://trends.google.com/trends/trendingsearches/daily/rss',\r\n      rateLimitPerHour: 1000 // RSS has higher limits\r\n    },\r\n    twitterApi: {\r\n      name: 'Twitter API v1.1',\r\n      enabled: false, // Temporarily disabled due to endpoint issues\r\n      apiKey: process.env.TWITTER_BEARER_TOKEN,\r\n      baseUrl: 'https://api.twitter.com/1.1',\r\n      rateLimitPerHour: 300\r\n    },\r\n    newsApi: {\r\n      name: 'News API',\r\n      enabled: false, // Temporarily disabled due to API key issues\r\n      apiKey: process.env.NEWS_API_KEY,\r\n      baseUrl: 'https://newsapi.org/v2',\r\n      rateLimitPerHour: 1000\r\n    },\r\n    redditApi: {\r\n      name: 'Reddit RSS',\r\n      enabled: process.env.REDDIT_RSS_ENABLED === 'true',\r\n      apiKey: undefined, // RSS doesn't need API key\r\n      baseUrl: 'https://www.reddit.com',\r\n      rateLimitPerHour: 1000 // RSS has higher limits\r\n    },\r\n    youtubeApi: {\r\n      name: 'YouTube Data API',\r\n      enabled: !!process.env.YOUTUBE_API_KEY,\r\n      apiKey: process.env.YOUTUBE_API_KEY,\r\n      baseUrl: 'https://www.googleapis.com/youtube/v3',\r\n      rateLimitPerHour: 10000\r\n    },\r\n    eventbriteApi: {\r\n      name: 'Eventbrite API',\r\n      enabled: !!process.env.EVENTBRITE_API_KEY,\r\n      apiKey: process.env.EVENTBRITE_API_KEY,\r\n      baseUrl: 'https://www.eventbriteapi.com/v3',\r\n      rateLimitPerHour: 1000\r\n    },\r\n    openWeatherApi: {\r\n      name: 'OpenWeather API',\r\n      enabled: !!process.env.OPENWEATHER_API_KEY,\r\n      apiKey: process.env.OPENWEATHER_API_KEY,\r\n      baseUrl: 'https://api.openweathermap.org/data/2.5',\r\n      rateLimitPerHour: 1000\r\n    }\r\n  },\r\n  fallbackToStatic: true,\r\n  cacheTimeMinutes: 30\r\n};\r\n\r\n/**\r\n * Google Trends Integration via RSS\r\n */\r\nexport async function fetchGoogleTrends(\r\n  location: string,\r\n  category?: string\r\n): Promise<any[]> {\r\n  if (!TRENDING_CONFIG.sources.googleTrends.enabled) {\r\n    console.log('Google Trends RSS not enabled, using fallback');\r\n    return getGoogleTrendsFallback(location, category);\r\n  }\r\n\r\n  try {\r\n    // Import RSS integration\r\n    const { fetchGoogleTrendsRSS } = await import('./rss-feeds-integration');\r\n\r\n    // Use RSS feeds for Google Trends\r\n    const geoCode = getGoogleTrendsGeoCode(location);\r\n    const trends = await fetchGoogleTrendsRSS(geoCode, category);\r\n\r\n    // Convert to expected format\r\n    return trends.map(trend => ({\r\n      topic: trend.topic,\r\n      relevanceScore: trend.relevanceScore,\r\n      category: trend.category,\r\n      timeframe: trend.timeframe,\r\n      engagement_potential: trend.engagement_potential,\r\n      source: 'google_trends_rss'\r\n    }));\r\n\r\n  } catch (error) {\r\n    console.error('Error fetching Google Trends RSS:', error);\r\n    return getGoogleTrendsFallback(location, category);\r\n  }\r\n}\r\n\r\n/**\r\n * Twitter/X Trends Integration\r\n */\r\nexport async function fetchTwitterTrends(\r\n  location: string,\r\n  businessType?: string\r\n): Promise<any[]> {\r\n  if (!TRENDING_CONFIG.sources.twitterApi.enabled) {\r\n    console.log('Twitter API not configured, using fallback');\r\n    return getTwitterTrendsFallback(location, businessType);\r\n  }\r\n\r\n  try {\r\n    const woeid = getTwitterWOEID(location);\r\n\r\n    // Use Twitter API v2 trending topics endpoint\r\n    const response = await fetch(\r\n      `${TRENDING_CONFIG.sources.twitterApi.baseUrl}/trends/place.json?id=${woeid}`,\r\n      {\r\n        headers: {\r\n          'Authorization': `Bearer ${TRENDING_CONFIG.sources.twitterApi.apiKey}`,\r\n          'Content-Type': 'application/json',\r\n          'User-Agent': 'TrendingTopicsBot/2.0'\r\n        }\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      console.log(`Twitter API response: ${response.status} ${response.statusText}`);\r\n      throw new Error(`Twitter API error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log(`✅ Twitter API returned ${data.length || 0} trend locations`);\r\n\r\n    // Process Twitter trends data\r\n    return processTwitterTrendsData(data, businessType);\r\n\r\n  } catch (error) {\r\n    console.error('Error fetching Twitter trends:', error);\r\n    return getTwitterTrendsFallback(location, businessType);\r\n  }\r\n}\r\n\r\n/**\r\n * News API Integration\r\n */\r\nexport async function fetchCurrentNews(\r\n  location: string,\r\n  businessType: string,\r\n  category?: string\r\n): Promise<any[]> {\r\n  if (!TRENDING_CONFIG.sources.newsApi.enabled) {\r\n    console.log('News API not configured, using fallback');\r\n    return getNewsFallback(location, businessType, category);\r\n  }\r\n\r\n  try {\r\n    const params = new URLSearchParams({\r\n      country: getNewsApiCountryCode(location),\r\n      category: category || 'business',\r\n      pageSize: '10',\r\n      apiKey: TRENDING_CONFIG.sources.newsApi.apiKey!\r\n    });\r\n\r\n    console.log(`🔍 Fetching news from News API for ${location}...`);\r\n    const response = await fetch(`${TRENDING_CONFIG.sources.newsApi.baseUrl}/top-headlines?${params}`);\r\n\r\n    if (!response.ok) {\r\n      console.log(`News API response: ${response.status} ${response.statusText}`);\r\n      const errorText = await response.text();\r\n      console.log('News API error details:', errorText);\r\n      throw new Error(`News API error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log(`✅ News API returned ${data.articles?.length || 0} articles`);\r\n\r\n    // Process news data\r\n    return processNewsData(data, businessType);\r\n\r\n  } catch (error) {\r\n    console.error('Error fetching news:', error);\r\n    return getNewsFallback(location, businessType, category);\r\n  }\r\n}\r\n\r\n/**\r\n * Reddit Trends Integration via RSS\r\n */\r\nexport async function fetchRedditTrends(\r\n  businessType: string,\r\n  platform: string\r\n): Promise<any[]> {\r\n  if (!TRENDING_CONFIG.sources.redditApi.enabled) {\r\n    console.log('Reddit RSS not enabled, using fallback');\r\n    return getRedditTrendsFallback(businessType, platform);\r\n  }\r\n\r\n  try {\r\n    // Import RSS integration\r\n    const { fetchRedditRSS } = await import('./rss-feeds-integration');\r\n\r\n    // Use RSS feeds for Reddit trends\r\n    const trends = await fetchRedditRSS(businessType);\r\n\r\n    // Convert to expected format\r\n    return trends.map(trend => ({\r\n      topic: trend.topic,\r\n      relevanceScore: trend.relevanceScore,\r\n      category: trend.category,\r\n      timeframe: trend.timeframe,\r\n      engagement_potential: trend.engagement_potential,\r\n      source: 'reddit_rss'\r\n    }));\r\n\r\n  } catch (error) {\r\n    console.error('Error fetching Reddit RSS:', error);\r\n    return getRedditTrendsFallback(businessType, platform);\r\n  }\r\n}\r\n\r\n/**\r\n * Helper functions for processing API data\r\n */\r\nfunction processGoogleTrendsData(data: any, location: string, category?: string) {\r\n  // Process Google Trends API response\r\n  return [\r\n    {\r\n      topic: `Trending in ${location}`,\r\n      source: 'google_trends',\r\n      relevanceScore: 9,\r\n      category: category || 'general',\r\n      timeframe: 'now',\r\n      engagement_potential: 'high'\r\n    }\r\n  ];\r\n}\r\n\r\nfunction processTwitterTrendsData(data: any, businessType?: string) {\r\n  // Process Twitter API response\r\n  if (data && data[0] && data[0].trends) {\r\n    return data[0].trends.slice(0, 10).map((trend: any) => ({\r\n      topic: trend.name,\r\n      source: 'twitter',\r\n      relevanceScore: trend.tweet_volume ? Math.min(10, Math.log10(trend.tweet_volume)) : 5,\r\n      category: 'social',\r\n      timeframe: 'now',\r\n      engagement_potential: trend.tweet_volume > 10000 ? 'high' : 'medium'\r\n    }));\r\n  }\r\n  return [];\r\n}\r\n\r\nfunction processNewsData(data: any, businessType: string) {\r\n  // Process News API response\r\n  if (data && data.articles) {\r\n    return data.articles.slice(0, 8).map((article: any) => ({\r\n      topic: article.title,\r\n      source: 'news',\r\n      relevanceScore: 8,\r\n      category: 'news',\r\n      timeframe: 'today',\r\n      engagement_potential: 'high',\r\n      business_angle: `How this relates to ${businessType} industry`\r\n    }));\r\n  }\r\n  return [];\r\n}\r\n\r\nfunction processRedditData(data: any, subreddit: string) {\r\n  // Process Reddit API response\r\n  if (data && data.data && data.data.children) {\r\n    return data.data.children.slice(0, 5).map((post: any) => ({\r\n      topic: post.data.title,\r\n      source: 'reddit',\r\n      relevanceScore: Math.min(10, post.data.score / 100),\r\n      category: 'community',\r\n      timeframe: 'today',\r\n      engagement_potential: post.data.score > 1000 ? 'high' : 'medium',\r\n      subreddit: subreddit\r\n    }));\r\n  }\r\n  return [];\r\n}\r\n\r\n/**\r\n * Helper functions for API parameters\r\n */\r\nfunction getGoogleTrendsGeoCode(location: string): string {\r\n  const geoMap: Record<string, string> = {\r\n    'kenya': 'KE',\r\n    'united states': 'US',\r\n    'nairobi': 'KE',\r\n    'new york': 'US-NY',\r\n    'london': 'GB-ENG'\r\n  };\r\n  return geoMap[location.toLowerCase()] || 'US';\r\n}\r\n\r\nfunction getTwitterWOEID(location: string): string {\r\n  const woeidMap: Record<string, string> = {\r\n    'kenya': '23424863',\r\n    'united states': '23424977',\r\n    'nairobi': '1528488',\r\n    'new york': '2459115',\r\n    'london': '44418'\r\n  };\r\n  return woeidMap[location.toLowerCase()] || '1'; // Worldwide\r\n}\r\n\r\nfunction getNewsApiCountryCode(location: string): string {\r\n  const countryMap: Record<string, string> = {\r\n    'kenya': 'ke',\r\n    'united states': 'us',\r\n    'nairobi': 'ke',\r\n    'new york': 'us',\r\n    'london': 'gb'\r\n  };\r\n  return countryMap[location.toLowerCase()] || 'us';\r\n}\r\n\r\nfunction getRelevantSubreddits(businessType: string): string[] {\r\n  const subredditMap: Record<string, string[]> = {\r\n    'financial technology software': ['fintech', 'personalfinance', 'investing', 'entrepreneur'],\r\n    'restaurant': ['food', 'recipes', 'restaurantowners', 'smallbusiness'],\r\n    'fitness': ['fitness', 'bodybuilding', 'nutrition', 'personaltrainer'],\r\n    'technology': ['technology', 'programming', 'startups', 'artificial']\r\n  };\r\n  return subredditMap[businessType.toLowerCase()] || ['business', 'entrepreneur'];\r\n}\r\n\r\n/**\r\n * Fallback functions when APIs are not available\r\n */\r\nfunction getGoogleTrendsFallback(location: string, category?: string) {\r\n  return [\r\n    {\r\n      topic: `Local business trends in ${location}`,\r\n      source: 'fallback',\r\n      relevanceScore: 7,\r\n      category: category || 'business',\r\n      timeframe: 'week',\r\n      engagement_potential: 'medium'\r\n    }\r\n  ];\r\n}\r\n\r\nfunction getTwitterTrendsFallback(location: string, businessType?: string) {\r\n  return [\r\n    {\r\n      topic: '#MondayMotivation',\r\n      source: 'fallback',\r\n      relevanceScore: 6,\r\n      category: 'social',\r\n      timeframe: 'today',\r\n      engagement_potential: 'medium'\r\n    }\r\n  ];\r\n}\r\n\r\nfunction getNewsFallback(location: string, businessType: string, category?: string) {\r\n  return [\r\n    {\r\n      topic: `${businessType} industry updates`,\r\n      source: 'fallback',\r\n      relevanceScore: 6,\r\n      category: 'news',\r\n      timeframe: 'today',\r\n      engagement_potential: 'medium'\r\n    }\r\n  ];\r\n}\r\n\r\nfunction getRedditTrendsFallback(businessType: string, platform: string) {\r\n  return [\r\n    {\r\n      topic: `${businessType} community discussions`,\r\n      source: 'fallback',\r\n      relevanceScore: 5,\r\n      category: 'community',\r\n      timeframe: 'today',\r\n      engagement_potential: 'medium'\r\n    }\r\n  ];\r\n}\r\n\r\n/**\r\n * Fetch comprehensive local context (weather + events)\r\n */\r\nexport async function fetchLocalContext(\r\n  location: string,\r\n  businessType: string\r\n): Promise<LocalContext> {\r\n  const context: LocalContext = {};\r\n\r\n  try {\r\n    // Fetch weather context\r\n    if (TRENDING_CONFIG.sources.openWeatherApi.enabled) {\r\n      console.log(`🌤️ Fetching weather context for ${location}...`);\r\n\r\n      const params = new URLSearchParams({\r\n        q: location,\r\n        appid: TRENDING_CONFIG.sources.openWeatherApi.apiKey!,\r\n        units: 'metric'\r\n      });\r\n\r\n      const response = await fetch(\r\n        `${TRENDING_CONFIG.sources.openWeatherApi.baseUrl}/weather?${params}`\r\n      );\r\n\r\n      if (response.ok) {\r\n        const weatherData = await response.json();\r\n        context.weather = {\r\n          temperature: Math.round(weatherData.main.temp),\r\n          condition: weatherData.weather[0].main,\r\n          business_impact: generateBusinessWeatherImpact(weatherData.weather[0].main, weatherData.main.temp, businessType),\r\n          content_opportunities: generateWeatherContentOpportunities(weatherData.weather[0].main, weatherData.main.temp, businessType)\r\n        };\r\n        console.log(`✅ Weather: ${context.weather.temperature}°C, ${context.weather.condition}`);\r\n      }\r\n    }\r\n\r\n    // Fetch events context\r\n    if (TRENDING_CONFIG.sources.eventbriteApi.enabled) {\r\n      console.log(`🎪 Fetching events context for ${location}...`);\r\n\r\n      const params = new URLSearchParams({\r\n        'location.address': location,\r\n        'location.within': '25km',\r\n        'start_date.range_start': new Date().toISOString(),\r\n        'start_date.range_end': new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n        'sort_by': 'relevance',\r\n        'page_size': '10'\r\n      });\r\n\r\n      const response = await fetch(\r\n        `${TRENDING_CONFIG.sources.eventbriteApi.baseUrl}/events/search/?${params}`,\r\n        {\r\n          headers: {\r\n            'Authorization': `Bearer ${TRENDING_CONFIG.sources.eventbriteApi.apiKey}`,\r\n            'Content-Type': 'application/json'\r\n          }\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        const eventsData = await response.json();\r\n        context.events = (eventsData.events || []).slice(0, 5).map((event: any) => ({\r\n          name: event.name?.text || 'Event',\r\n          category: event.category?.name || 'General',\r\n          relevance_score: calculateEventRelevance(event, businessType),\r\n          start_date: event.start?.local || event.start?.utc\r\n        }));\r\n        console.log(`✅ Found ${context.events.length} relevant events`);\r\n      }\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error('Error fetching local context:', error);\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\n// Helper functions for weather and events\r\nfunction generateBusinessWeatherImpact(condition: string, temperature: number, businessType: string): string {\r\n  const businessImpacts: Record<string, Record<string, string>> = {\r\n    'restaurant': {\r\n      'sunny': 'Perfect weather for outdoor dining and patio service',\r\n      'rain': 'Great opportunity to promote cozy indoor dining experience',\r\n      'hot': 'Ideal time to highlight refreshing drinks and cool dishes',\r\n      'cold': 'Perfect weather for warm comfort food and hot beverages'\r\n    },\r\n    'fitness': {\r\n      'sunny': 'Excellent conditions for outdoor workouts and activities',\r\n      'rain': 'Great time to promote indoor fitness programs',\r\n      'hot': 'Important to emphasize hydration and cooling strategies',\r\n      'cold': 'Perfect for promoting warm-up routines and indoor training'\r\n    },\r\n    'financial technology software': {\r\n      'sunny': 'Great weather for outdoor meetings and client visits',\r\n      'rain': 'Perfect time for indoor productivity and digital solutions',\r\n      'hot': 'Ideal for promoting mobile solutions and remote services',\r\n      'cold': 'Good time for cozy indoor planning and financial reviews'\r\n    }\r\n  };\r\n\r\n  const businessKey = businessType.toLowerCase();\r\n  const impacts = businessImpacts[businessKey] || businessImpacts['restaurant'];\r\n\r\n  if (temperature > 25) return impacts['hot'] || 'Weather creates opportunities for seasonal promotions';\r\n  if (temperature < 10) return impacts['cold'] || 'Weather creates opportunities for comfort-focused messaging';\r\n\r\n  return impacts[condition.toLowerCase()] || impacts['sunny'] || 'Current weather conditions are favorable for business';\r\n}\r\n\r\nfunction generateWeatherContentOpportunities(condition: string, temperature: number, businessType: string): string[] {\r\n  const opportunities: string[] = [];\r\n\r\n  // Temperature-based opportunities\r\n  if (temperature > 25) {\r\n    opportunities.push('Hot weather content angle', 'Summer promotion opportunity', 'Cooling solutions messaging');\r\n  } else if (temperature < 10) {\r\n    opportunities.push('Cold weather content angle', 'Winter comfort messaging', 'Warm-up solutions');\r\n  }\r\n\r\n  // Condition-based opportunities\r\n  switch (condition.toLowerCase()) {\r\n    case 'rain':\r\n      opportunities.push('Rainy day indoor activities', 'Weather protection messaging', 'Cozy atmosphere content');\r\n      break;\r\n    case 'sunny':\r\n    case 'clear':\r\n      opportunities.push('Beautiful weather celebration', 'Outdoor activity promotion', 'Sunshine positivity');\r\n      break;\r\n    case 'clouds':\r\n      opportunities.push('Perfect weather for activities', 'Comfortable conditions messaging');\r\n      break;\r\n  }\r\n\r\n  return opportunities;\r\n}\r\n\r\nfunction calculateEventRelevance(event: any, businessType: string): number {\r\n  let score = 5; // Base score\r\n\r\n  const eventName = (event.name?.text || '').toLowerCase();\r\n  const eventCategory = (event.category?.name || '').toLowerCase();\r\n\r\n  // Business type relevance\r\n  const businessKeywords = getBusinessKeywords(businessType);\r\n  for (const keyword of businessKeywords) {\r\n    if (eventName.includes(keyword) || eventCategory.includes(keyword)) {\r\n      score += 2;\r\n    }\r\n  }\r\n\r\n  // Event category bonus\r\n  if (eventCategory.includes('business') || eventCategory.includes('networking')) {\r\n    score += 1;\r\n  }\r\n\r\n  return Math.min(10, score);\r\n}\r\n\r\nfunction getBusinessKeywords(businessType: string): string[] {\r\n  const keywordMap: Record<string, string[]> = {\r\n    'financial technology software': ['fintech', 'finance', 'banking', 'payment', 'blockchain', 'startup', 'tech'],\r\n    'restaurant': ['food', 'culinary', 'cooking', 'dining', 'chef', 'restaurant'],\r\n    'fitness': ['fitness', 'health', 'wellness', 'gym', 'workout', 'nutrition'],\r\n    'technology': ['tech', 'software', 'programming', 'ai', 'digital', 'innovation']\r\n  };\r\n\r\n  return keywordMap[businessType.toLowerCase()] || ['business', 'networking', 'professional'];\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;AA2CM,MAAM,kBAA0C;IACrD,SAAS;QACP,cAAc;YACZ,MAAM;YACN,SAAS,QAAQ,GAAG,CAAC,yBAAyB,KAAK;YACnD,QAAQ;YACR,SAAS;YACT,kBAAkB,KAAK,wBAAwB;QACjD;QACA,YAAY;YACV,MAAM;YACN,SAAS;YACT,QAAQ,QAAQ,GAAG,CAAC,oBAAoB;YACxC,SAAS;YACT,kBAAkB;QACpB;QACA,SAAS;YACP,MAAM;YACN,SAAS;YACT,QAAQ,QAAQ,GAAG,CAAC,YAAY;YAChC,SAAS;YACT,kBAAkB;QACpB;QACA,WAAW;YACT,MAAM;YACN,SAAS,QAAQ,GAAG,CAAC,kBAAkB,KAAK;YAC5C,QAAQ;YACR,SAAS;YACT,kBAAkB,KAAK,wBAAwB;QACjD;QACA,YAAY;YACV,MAAM;YACN,SAAS,CAAC,CAAC,QAAQ,GAAG,CAAC,eAAe;YACtC,QAAQ,QAAQ,GAAG,CAAC,eAAe;YACnC,SAAS;YACT,kBAAkB;QACpB;QACA,eAAe;YACb,MAAM;YACN,SAAS,CAAC,CAAC,QAAQ,GAAG,CAAC,kBAAkB;YACzC,QAAQ,QAAQ,GAAG,CAAC,kBAAkB;YACtC,SAAS;YACT,kBAAkB;QACpB;QACA,gBAAgB;YACd,MAAM;YACN,SAAS,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;YAC1C,QAAQ,QAAQ,GAAG,CAAC,mBAAmB;YACvC,SAAS;YACT,kBAAkB;QACpB;IACF;IACA,kBAAkB;IAClB,kBAAkB;AACpB;AAKO,eAAe,kBACpB,QAAgB,EAChB,QAAiB;IAEjB,IAAI,CAAC,gBAAgB,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE;QACjD,QAAQ,GAAG,CAAC;QACZ,OAAO,wBAAwB,UAAU;IAC3C;IAEA,IAAI;QACF,yBAAyB;QACzB,MAAM,EAAE,oBAAoB,EAAE,GAAG;QAEjC,kCAAkC;QAClC,MAAM,UAAU,uBAAuB;QACvC,MAAM,SAAS,MAAM,qBAAqB,SAAS;QAEnD,6BAA6B;QAC7B,OAAO,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;gBAC1B,OAAO,MAAM,KAAK;gBAClB,gBAAgB,MAAM,cAAc;gBACpC,UAAU,MAAM,QAAQ;gBACxB,WAAW,MAAM,SAAS;gBAC1B,sBAAsB,MAAM,oBAAoB;gBAChD,QAAQ;YACV,CAAC;IAEH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,wBAAwB,UAAU;IAC3C;AACF;AAKO,eAAe,mBACpB,QAAgB,EAChB,YAAqB;IAErB,IAAI,CAAC,gBAAgB,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE;QAC/C,QAAQ,GAAG,CAAC;QACZ,OAAO,yBAAyB,UAAU;IAC5C;IAEA,IAAI;QACF,MAAM,QAAQ,gBAAgB;QAE9B,8CAA8C;QAC9C,MAAM,WAAW,MAAM,MACrB,GAAG,gBAAgB,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,sBAAsB,EAAE,OAAO,EAC7E;YACE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,gBAAgB,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE;gBACtE,gBAAgB;gBAChB,cAAc;YAChB;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAC7E,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,SAAS,MAAM,EAAE;QACzD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,KAAK,MAAM,IAAI,EAAE,gBAAgB,CAAC;QAExE,8BAA8B;QAC9B,OAAO,yBAAyB,MAAM;IAExC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,yBAAyB,UAAU;IAC5C;AACF;AAKO,eAAe,iBACpB,QAAgB,EAChB,YAAoB,EACpB,QAAiB;IAEjB,IAAI,CAAC,gBAAgB,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;QAC5C,QAAQ,GAAG,CAAC;QACZ,OAAO,gBAAgB,UAAU,cAAc;IACjD;IAEA,IAAI;QACF,MAAM,SAAS,IAAI,gBAAgB;YACjC,SAAS,sBAAsB;YAC/B,UAAU,YAAY;YACtB,UAAU;YACV,QAAQ,gBAAgB,OAAO,CAAC,OAAO,CAAC,MAAM;QAChD;QAEA,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,SAAS,GAAG,CAAC;QAC/D,MAAM,WAAW,MAAM,MAAM,GAAG,gBAAgB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE,QAAQ;QAEjG,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAC1E,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,GAAG,CAAC,2BAA2B;YACvC,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,SAAS,MAAM,EAAE;QACtD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,KAAK,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;QAExE,oBAAoB;QACpB,OAAO,gBAAgB,MAAM;IAE/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gBAAgB,UAAU,cAAc;IACjD;AACF;AAKO,eAAe,kBACpB,YAAoB,EACpB,QAAgB;IAEhB,IAAI,CAAC,gBAAgB,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;QAC9C,QAAQ,GAAG,CAAC;QACZ,OAAO,wBAAwB,cAAc;IAC/C;IAEA,IAAI;QACF,yBAAyB;QACzB,MAAM,EAAE,cAAc,EAAE,GAAG;QAE3B,kCAAkC;QAClC,MAAM,SAAS,MAAM,eAAe;QAEpC,6BAA6B;QAC7B,OAAO,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;gBAC1B,OAAO,MAAM,KAAK;gBAClB,gBAAgB,MAAM,cAAc;gBACpC,UAAU,MAAM,QAAQ;gBACxB,WAAW,MAAM,SAAS;gBAC1B,sBAAsB,MAAM,oBAAoB;gBAChD,QAAQ;YACV,CAAC;IAEH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,wBAAwB,cAAc;IAC/C;AACF;AAEA;;CAEC,GACD,SAAS,wBAAwB,IAAS,EAAE,QAAgB,EAAE,QAAiB;IAC7E,qCAAqC;IACrC,OAAO;QACL;YACE,OAAO,CAAC,YAAY,EAAE,UAAU;YAChC,QAAQ;YACR,gBAAgB;YAChB,UAAU,YAAY;YACtB,WAAW;YACX,sBAAsB;QACxB;KACD;AACH;AAEA,SAAS,yBAAyB,IAAS,EAAE,YAAqB;IAChE,+BAA+B;IAC/B,IAAI,QAAQ,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;QACrC,OAAO,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,QAAe,CAAC;gBACtD,OAAO,MAAM,IAAI;gBACjB,QAAQ;gBACR,gBAAgB,MAAM,YAAY,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,YAAY,KAAK;gBACpF,UAAU;gBACV,WAAW;gBACX,sBAAsB,MAAM,YAAY,GAAG,QAAQ,SAAS;YAC9D,CAAC;IACH;IACA,OAAO,EAAE;AACX;AAEA,SAAS,gBAAgB,IAAS,EAAE,YAAoB;IACtD,4BAA4B;IAC5B,IAAI,QAAQ,KAAK,QAAQ,EAAE;QACzB,OAAO,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAiB,CAAC;gBACtD,OAAO,QAAQ,KAAK;gBACpB,QAAQ;gBACR,gBAAgB;gBAChB,UAAU;gBACV,WAAW;gBACX,sBAAsB;gBACtB,gBAAgB,CAAC,oBAAoB,EAAE,aAAa,SAAS,CAAC;YAChE,CAAC;IACH;IACA,OAAO,EAAE;AACX;AAEA,SAAS,kBAAkB,IAAS,EAAE,SAAiB;IACrD,8BAA8B;IAC9B,IAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,QAAQ,EAAE;QAC3C,OAAO,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAc,CAAC;gBACxD,OAAO,KAAK,IAAI,CAAC,KAAK;gBACtB,QAAQ;gBACR,gBAAgB,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG;gBAC/C,UAAU;gBACV,WAAW;gBACX,sBAAsB,KAAK,IAAI,CAAC,KAAK,GAAG,OAAO,SAAS;gBACxD,WAAW;YACb,CAAC;IACH;IACA,OAAO,EAAE;AACX;AAEA;;CAEC,GACD,SAAS,uBAAuB,QAAgB;IAC9C,MAAM,SAAiC;QACrC,SAAS;QACT,iBAAiB;QACjB,WAAW;QACX,YAAY;QACZ,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,SAAS,WAAW,GAAG,IAAI;AAC3C;AAEA,SAAS,gBAAgB,QAAgB;IACvC,MAAM,WAAmC;QACvC,SAAS;QACT,iBAAiB;QACjB,WAAW;QACX,YAAY;QACZ,UAAU;IACZ;IACA,OAAO,QAAQ,CAAC,SAAS,WAAW,GAAG,IAAI,KAAK,YAAY;AAC9D;AAEA,SAAS,sBAAsB,QAAgB;IAC7C,MAAM,aAAqC;QACzC,SAAS;QACT,iBAAiB;QACjB,WAAW;QACX,YAAY;QACZ,UAAU;IACZ;IACA,OAAO,UAAU,CAAC,SAAS,WAAW,GAAG,IAAI;AAC/C;AAEA,SAAS,sBAAsB,YAAoB;IACjD,MAAM,eAAyC;QAC7C,iCAAiC;YAAC;YAAW;YAAmB;YAAa;SAAe;QAC5F,cAAc;YAAC;YAAQ;YAAW;YAAoB;SAAgB;QACtE,WAAW;YAAC;YAAW;YAAgB;YAAa;SAAkB;QACtE,cAAc;YAAC;YAAc;YAAe;YAAY;SAAa;IACvE;IACA,OAAO,YAAY,CAAC,aAAa,WAAW,GAAG,IAAI;QAAC;QAAY;KAAe;AACjF;AAEA;;CAEC,GACD,SAAS,wBAAwB,QAAgB,EAAE,QAAiB;IAClE,OAAO;QACL;YACE,OAAO,CAAC,yBAAyB,EAAE,UAAU;YAC7C,QAAQ;YACR,gBAAgB;YAChB,UAAU,YAAY;YACtB,WAAW;YACX,sBAAsB;QACxB;KACD;AACH;AAEA,SAAS,yBAAyB,QAAgB,EAAE,YAAqB;IACvE,OAAO;QACL;YACE,OAAO;YACP,QAAQ;YACR,gBAAgB;YAChB,UAAU;YACV,WAAW;YACX,sBAAsB;QACxB;KACD;AACH;AAEA,SAAS,gBAAgB,QAAgB,EAAE,YAAoB,EAAE,QAAiB;IAChF,OAAO;QACL;YACE,OAAO,GAAG,aAAa,iBAAiB,CAAC;YACzC,QAAQ;YACR,gBAAgB;YAChB,UAAU;YACV,WAAW;YACX,sBAAsB;QACxB;KACD;AACH;AAEA,SAAS,wBAAwB,YAAoB,EAAE,QAAgB;IACrE,OAAO;QACL;YACE,OAAO,GAAG,aAAa,sBAAsB,CAAC;YAC9C,QAAQ;YACR,gBAAgB;YAChB,UAAU;YACV,WAAW;YACX,sBAAsB;QACxB;KACD;AACH;AAKO,eAAe,kBACpB,QAAgB,EAChB,YAAoB;IAEpB,MAAM,UAAwB,CAAC;IAE/B,IAAI;QACF,wBAAwB;QACxB,IAAI,gBAAgB,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE;YAClD,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,SAAS,GAAG,CAAC;YAE7D,MAAM,SAAS,IAAI,gBAAgB;gBACjC,GAAG;gBACH,OAAO,gBAAgB,OAAO,CAAC,cAAc,CAAC,MAAM;gBACpD,OAAO;YACT;YAEA,MAAM,WAAW,MAAM,MACrB,GAAG,gBAAgB,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ;YAGvE,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,cAAc,MAAM,SAAS,IAAI;gBACvC,QAAQ,OAAO,GAAG;oBAChB,aAAa,KAAK,KAAK,CAAC,YAAY,IAAI,CAAC,IAAI;oBAC7C,WAAW,YAAY,OAAO,CAAC,EAAE,CAAC,IAAI;oBACtC,iBAAiB,8BAA8B,YAAY,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,YAAY,IAAI,CAAC,IAAI,EAAE;oBACnG,uBAAuB,oCAAoC,YAAY,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,YAAY,IAAI,CAAC,IAAI,EAAE;gBACjH;gBACA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,OAAO,CAAC,SAAS,EAAE;YACzF;QACF;QAEA,uBAAuB;QACvB,IAAI,gBAAgB,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE;YACjD,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,SAAS,GAAG,CAAC;YAE3D,MAAM,SAAS,IAAI,gBAAgB;gBACjC,oBAAoB;gBACpB,mBAAmB;gBACnB,0BAA0B,IAAI,OAAO,WAAW;gBAChD,wBAAwB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBAClF,WAAW;gBACX,aAAa;YACf;YAEA,MAAM,WAAW,MAAM,MACrB,GAAG,gBAAgB,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,EAC3E;gBACE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,gBAAgB,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE;oBACzE,gBAAgB;gBAClB;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,aAAa,MAAM,SAAS,IAAI;gBACtC,QAAQ,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAe,CAAC;wBAC1E,MAAM,MAAM,IAAI,EAAE,QAAQ;wBAC1B,UAAU,MAAM,QAAQ,EAAE,QAAQ;wBAClC,iBAAiB,wBAAwB,OAAO;wBAChD,YAAY,MAAM,KAAK,EAAE,SAAS,MAAM,KAAK,EAAE;oBACjD,CAAC;gBACD,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAChE;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;IACjD;IAEA,OAAO;AACT;AAEA,0CAA0C;AAC1C,SAAS,8BAA8B,SAAiB,EAAE,WAAmB,EAAE,YAAoB;IACjG,MAAM,kBAA0D;QAC9D,cAAc;YACZ,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;QACA,WAAW;YACT,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;QACA,iCAAiC;YAC/B,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,aAAa,WAAW;IAC5C,MAAM,UAAU,eAAe,CAAC,YAAY,IAAI,eAAe,CAAC,aAAa;IAE7E,IAAI,cAAc,IAAI,OAAO,OAAO,CAAC,MAAM,IAAI;IAC/C,IAAI,cAAc,IAAI,OAAO,OAAO,CAAC,OAAO,IAAI;IAEhD,OAAO,OAAO,CAAC,UAAU,WAAW,GAAG,IAAI,OAAO,CAAC,QAAQ,IAAI;AACjE;AAEA,SAAS,oCAAoC,SAAiB,EAAE,WAAmB,EAAE,YAAoB;IACvG,MAAM,gBAA0B,EAAE;IAElC,kCAAkC;IAClC,IAAI,cAAc,IAAI;QACpB,cAAc,IAAI,CAAC,6BAA6B,gCAAgC;IAClF,OAAO,IAAI,cAAc,IAAI;QAC3B,cAAc,IAAI,CAAC,8BAA8B,4BAA4B;IAC/E;IAEA,gCAAgC;IAChC,OAAQ,UAAU,WAAW;QAC3B,KAAK;YACH,cAAc,IAAI,CAAC,+BAA+B,gCAAgC;YAClF;QACF,KAAK;QACL,KAAK;YACH,cAAc,IAAI,CAAC,iCAAiC,8BAA8B;YAClF;QACF,KAAK;YACH,cAAc,IAAI,CAAC,kCAAkC;YACrD;IACJ;IAEA,OAAO;AACT;AAEA,SAAS,wBAAwB,KAAU,EAAE,YAAoB;IAC/D,IAAI,QAAQ,GAAG,aAAa;IAE5B,MAAM,YAAY,CAAC,MAAM,IAAI,EAAE,QAAQ,EAAE,EAAE,WAAW;IACtD,MAAM,gBAAgB,CAAC,MAAM,QAAQ,EAAE,QAAQ,EAAE,EAAE,WAAW;IAE9D,0BAA0B;IAC1B,MAAM,mBAAmB,oBAAoB;IAC7C,KAAK,MAAM,WAAW,iBAAkB;QACtC,IAAI,UAAU,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,UAAU;YAClE,SAAS;QACX;IACF;IAEA,uBAAuB;IACvB,IAAI,cAAc,QAAQ,CAAC,eAAe,cAAc,QAAQ,CAAC,eAAe;QAC9E,SAAS;IACX;IAEA,OAAO,KAAK,GAAG,CAAC,IAAI;AACtB;AAEA,SAAS,oBAAoB,YAAoB;IAC/C,MAAM,aAAuC;QAC3C,iCAAiC;YAAC;YAAW;YAAW;YAAW;YAAW;YAAc;YAAW;SAAO;QAC9G,cAAc;YAAC;YAAQ;YAAY;YAAW;YAAU;YAAQ;SAAa;QAC7E,WAAW;YAAC;YAAW;YAAU;YAAY;YAAO;YAAW;SAAY;QAC3E,cAAc;YAAC;YAAQ;YAAY;YAAe;YAAM;YAAW;SAAa;IAClF;IAEA,OAAO,UAAU,CAAC,aAAa,WAAW,GAAG,IAAI;QAAC;QAAY;QAAc;KAAe;AAC7F", "debugId": null}}, {"offset": {"line": 3319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/trending-topics.ts"], "sourcesContent": ["/**\r\n * Trending Topics and Market Intelligence System\r\n *\r\n * This module provides real-time trending topics, competitor analysis,\r\n * and market intelligence for content optimization.\r\n */\r\n\r\nimport {\r\n  fetchGoogleTrends,\r\n  fetchTwitterTrends,\r\n  fetchCurrentNews,\r\n  fetchRedditTrends\r\n} from './real-time-trends-integration';\r\n\r\nexport interface TrendingTopic {\r\n  topic: string;\r\n  relevanceScore: number; // 1-10\r\n  platform: string;\r\n  category: 'news' | 'entertainment' | 'business' | 'technology' | 'lifestyle' | 'local';\r\n  timeframe: 'now' | 'today' | 'week' | 'month';\r\n  engagement_potential: 'high' | 'medium' | 'low';\r\n}\r\n\r\nexport interface CompetitorInsight {\r\n  competitor_name: string;\r\n  content_gap: string;\r\n  differentiation_opportunity: string;\r\n  successful_strategy: string;\r\n  avoid_strategy: string;\r\n}\r\n\r\nexport interface CulturalContext {\r\n  location: string;\r\n  cultural_nuances: string[];\r\n  local_customs: string[];\r\n  language_preferences: string[];\r\n  seasonal_relevance: string[];\r\n  local_events: string[];\r\n}\r\n\r\nexport interface MarketIntelligence {\r\n  trending_topics: TrendingTopic[];\r\n  competitor_insights: CompetitorInsight[];\r\n  cultural_context: CulturalContext;\r\n  viral_content_patterns: string[];\r\n  engagement_triggers: string[];\r\n}\r\n\r\n/**\r\n * Generates real-time trending topics with fallback to static data\r\n */\r\nexport async function generateRealTimeTrendingTopics(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string = 'general'\r\n): Promise<TrendingTopic[]> {\r\n  try {\r\n    console.log(`🔍 Fetching real-time trends for ${businessType} in ${location}...`);\r\n    console.log(`📱 Platform: ${platform}`);\r\n\r\n    // Fetch from working real-time sources (temporarily disable failing APIs)\r\n    console.log('🌐 Starting RSS feeds fetch...');\r\n    const [googleTrends, redditTrends] = await Promise.allSettled([\r\n      fetchGoogleTrends(location, businessType),\r\n      fetchRedditTrends(businessType, platform)\r\n    ]);\r\n\r\n    console.log(`📊 Google Trends status: ${googleTrends.status}`);\r\n    console.log(`📊 Reddit Trends status: ${redditTrends.status}`);\r\n\r\n    // Temporarily disable failing APIs until we fix them\r\n    const twitterTrends = { status: 'rejected' as const, reason: 'Temporarily disabled' };\r\n    const currentNews = { status: 'rejected' as const, reason: 'Temporarily disabled' };\r\n\r\n    const allTrends: TrendingTopic[] = [];\r\n\r\n    // Process Google Trends\r\n    if (googleTrends.status === 'fulfilled') {\r\n      console.log(`✅ Google Trends: ${googleTrends.value.length} trends received`);\r\n      allTrends.push(...googleTrends.value.map(trend => ({\r\n        topic: trend.topic,\r\n        relevanceScore: trend.relevanceScore,\r\n        platform: platform,\r\n        category: trend.category as any,\r\n        timeframe: trend.timeframe as any,\r\n        engagement_potential: trend.engagement_potential as any\r\n      })));\r\n    }\r\n\r\n    // Process Twitter Trends\r\n    if (twitterTrends.status === 'fulfilled') {\r\n      allTrends.push(...twitterTrends.value.map(trend => ({\r\n        topic: trend.topic,\r\n        relevanceScore: trend.relevanceScore,\r\n        platform: platform,\r\n        category: trend.category as any,\r\n        timeframe: trend.timeframe as any,\r\n        engagement_potential: trend.engagement_potential as any\r\n      })));\r\n    }\r\n\r\n    // Process News\r\n    if (currentNews.status === 'fulfilled') {\r\n      allTrends.push(...currentNews.value.map(news => ({\r\n        topic: news.topic,\r\n        relevanceScore: news.relevanceScore,\r\n        platform: platform,\r\n        category: news.category as any,\r\n        timeframe: news.timeframe as any,\r\n        engagement_potential: news.engagement_potential as any\r\n      })));\r\n    } else {\r\n      console.warn(`⚠️ Google Trends failed:`, googleTrends.reason);\r\n    }\r\n\r\n    // Process Reddit Trends\r\n    if (redditTrends.status === 'fulfilled') {\r\n      console.log(`✅ Reddit Trends: ${redditTrends.value.length} trends received`);\r\n      allTrends.push(...redditTrends.value.map(trend => ({\r\n        topic: trend.topic,\r\n        relevanceScore: trend.relevanceScore,\r\n        platform: platform,\r\n        category: trend.category as any,\r\n        timeframe: trend.timeframe as any,\r\n        engagement_potential: trend.engagement_potential as any\r\n      })));\r\n    } else {\r\n      console.warn(`⚠️ Reddit Trends failed:`, redditTrends.reason);\r\n    }\r\n\r\n    // If we have real-time trends, use them\r\n    if (allTrends.length > 0) {\r\n      console.log(`✅ Found ${allTrends.length} real-time trends`);\r\n      return allTrends\r\n        .sort((a, b) => b.relevanceScore - a.relevanceScore)\r\n        .slice(0, 10);\r\n    }\r\n\r\n    // Fallback to static trends\r\n    console.log('⚠️ No real-time trends available, using static fallback');\r\n    return generateStaticTrendingTopics(businessType, location, platform);\r\n\r\n  } catch (error) {\r\n    console.error('Error fetching real-time trends:', error);\r\n    return generateStaticTrendingTopics(businessType, location, platform);\r\n  }\r\n}\r\n\r\n/**\r\n * Generates static trending topics (original function, now renamed)\r\n */\r\nexport function generateStaticTrendingTopics(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string = 'general'\r\n): TrendingTopic[] {\r\n\r\n  const businessTrends: Record<string, TrendingTopic[]> = {\r\n    'restaurant': [\r\n      {\r\n        topic: 'Sustainable dining trends',\r\n        relevanceScore: 9,\r\n        platform: 'instagram',\r\n        category: 'lifestyle',\r\n        timeframe: 'now',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Local food festivals',\r\n        relevanceScore: 8,\r\n        platform: 'facebook',\r\n        category: 'local',\r\n        timeframe: 'week',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Plant-based menu innovations',\r\n        relevanceScore: 7,\r\n        platform: 'linkedin',\r\n        category: 'business',\r\n        timeframe: 'month',\r\n        engagement_potential: 'medium'\r\n      }\r\n    ],\r\n    'fitness': [\r\n      {\r\n        topic: 'New Year fitness resolutions',\r\n        relevanceScore: 9,\r\n        platform: 'instagram',\r\n        category: 'lifestyle',\r\n        timeframe: 'now',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Mental health and exercise',\r\n        relevanceScore: 8,\r\n        platform: 'linkedin',\r\n        category: 'lifestyle',\r\n        timeframe: 'today',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Home workout equipment trends',\r\n        relevanceScore: 7,\r\n        platform: 'facebook',\r\n        category: 'lifestyle',\r\n        timeframe: 'week',\r\n        engagement_potential: 'medium'\r\n      }\r\n    ],\r\n    'technology': [\r\n      {\r\n        topic: 'AI in business automation',\r\n        relevanceScore: 10,\r\n        platform: 'linkedin',\r\n        category: 'technology',\r\n        timeframe: 'now',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Cybersecurity awareness',\r\n        relevanceScore: 9,\r\n        platform: 'twitter',\r\n        category: 'technology',\r\n        timeframe: 'today',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Remote work productivity tools',\r\n        relevanceScore: 8,\r\n        platform: 'linkedin',\r\n        category: 'business',\r\n        timeframe: 'week',\r\n        engagement_potential: 'medium'\r\n      }\r\n    ],\r\n    'beauty': [\r\n      {\r\n        topic: 'Clean beauty movement',\r\n        relevanceScore: 9,\r\n        platform: 'instagram',\r\n        category: 'lifestyle',\r\n        timeframe: 'now',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Skincare for different seasons',\r\n        relevanceScore: 8,\r\n        platform: 'instagram',\r\n        category: 'lifestyle',\r\n        timeframe: 'today',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Sustainable beauty packaging',\r\n        relevanceScore: 7,\r\n        platform: 'facebook',\r\n        category: 'lifestyle',\r\n        timeframe: 'week',\r\n        engagement_potential: 'medium'\r\n      }\r\n    ],\r\n    'financial technology software': [\r\n      {\r\n        topic: 'Digital banking adoption in Africa',\r\n        relevanceScore: 10,\r\n        platform: 'linkedin',\r\n        category: 'technology',\r\n        timeframe: 'now',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Financial inclusion initiatives',\r\n        relevanceScore: 9,\r\n        platform: 'twitter',\r\n        category: 'business',\r\n        timeframe: 'today',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Mobile payment security',\r\n        relevanceScore: 8,\r\n        platform: 'linkedin',\r\n        category: 'technology',\r\n        timeframe: 'week',\r\n        engagement_potential: 'medium'\r\n      }\r\n    ]\r\n  };\r\n\r\n  // Get base trends for business type\r\n  const baseTrends = businessTrends[businessType.toLowerCase()] || businessTrends['technology'];\r\n\r\n  // Add location-specific trends\r\n  const locationTrends = generateLocationTrends(location);\r\n\r\n  // Combine and filter by platform\r\n  const allTrends = [...baseTrends, ...locationTrends];\r\n\r\n  return allTrends\r\n    .filter(trend => trend.platform === platform || trend.platform === 'general')\r\n    .sort((a, b) => b.relevanceScore - a.relevanceScore)\r\n    .slice(0, 5);\r\n}\r\n\r\n/**\r\n * Generates location-specific trending topics\r\n */\r\nfunction generateLocationTrends(location: string): TrendingTopic[] {\r\n  const locationMap: Record<string, TrendingTopic[]> = {\r\n    'nairobi': [\r\n      {\r\n        topic: 'Nairobi tech hub growth',\r\n        relevanceScore: 8,\r\n        platform: 'linkedin',\r\n        category: 'business',\r\n        timeframe: 'week',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Kenyan startup ecosystem',\r\n        relevanceScore: 7,\r\n        platform: 'twitter',\r\n        category: 'business',\r\n        timeframe: 'today',\r\n        engagement_potential: 'medium'\r\n      }\r\n    ],\r\n    'new york': [\r\n      {\r\n        topic: 'NYC small business support',\r\n        relevanceScore: 8,\r\n        platform: 'facebook',\r\n        category: 'local',\r\n        timeframe: 'week',\r\n        engagement_potential: 'high'\r\n      }\r\n    ],\r\n    'london': [\r\n      {\r\n        topic: 'London fintech innovation',\r\n        relevanceScore: 8,\r\n        platform: 'linkedin',\r\n        category: 'business',\r\n        timeframe: 'week',\r\n        engagement_potential: 'high'\r\n      }\r\n    ]\r\n  };\r\n\r\n  const locationKey = location.toLowerCase().split(',')[0].trim();\r\n  return locationMap[locationKey] || [];\r\n}\r\n\r\n/**\r\n * Generates competitor analysis insights\r\n */\r\nexport function generateCompetitorInsights(\r\n  businessType: string,\r\n  location: string,\r\n  services?: string\r\n): CompetitorInsight[] {\r\n\r\n  const competitorStrategies: Record<string, CompetitorInsight[]> = {\r\n    'financial technology software': [\r\n      {\r\n        competitor_name: 'Traditional Banks',\r\n        content_gap: 'Lack of educational content about digital banking benefits',\r\n        differentiation_opportunity: 'Focus on simplicity and accessibility for everyday users',\r\n        successful_strategy: 'Trust-building through security messaging',\r\n        avoid_strategy: 'Overly technical jargon that confuses users'\r\n      },\r\n      {\r\n        competitor_name: 'Other Fintech Apps',\r\n        content_gap: 'Limited focus on local market needs and culture',\r\n        differentiation_opportunity: 'Emphasize local partnerships and community impact',\r\n        successful_strategy: 'User testimonials and success stories',\r\n        avoid_strategy: 'Generic global messaging without local relevance'\r\n      }\r\n    ],\r\n    'restaurant': [\r\n      {\r\n        competitor_name: 'Chain Restaurants',\r\n        content_gap: 'Lack of personal connection and local community focus',\r\n        differentiation_opportunity: 'Highlight local sourcing, chef personality, and community involvement',\r\n        successful_strategy: 'Behind-the-scenes content and food preparation videos',\r\n        avoid_strategy: 'Generic food photos without story or context'\r\n      }\r\n    ],\r\n    'fitness': [\r\n      {\r\n        competitor_name: 'Large Gym Chains',\r\n        content_gap: 'Impersonal approach and lack of individual attention',\r\n        differentiation_opportunity: 'Focus on personal transformation stories and community support',\r\n        successful_strategy: 'Client success stories and progress tracking',\r\n        avoid_strategy: 'Intimidating fitness content that discourages beginners'\r\n      }\r\n    ]\r\n  };\r\n\r\n  return competitorStrategies[businessType.toLowerCase()] || [\r\n    {\r\n      competitor_name: 'Industry Leaders',\r\n      content_gap: 'Generic messaging without personal touch',\r\n      differentiation_opportunity: 'Focus on authentic storytelling and customer relationships',\r\n      successful_strategy: 'Educational content that provides real value',\r\n      avoid_strategy: 'Overly promotional content without substance'\r\n    }\r\n  ];\r\n}\r\n\r\n/**\r\n * Generates cultural context for location-specific content\r\n */\r\nexport function generateCulturalContext(location: string): CulturalContext {\r\n  const culturalMap: Record<string, CulturalContext> = {\r\n    'nairobi, kenya': {\r\n      location: 'Nairobi, Kenya',\r\n      cultural_nuances: [\r\n        'Ubuntu philosophy - community and interconnectedness',\r\n        'Respect for elders and traditional values',\r\n        'Entrepreneurial spirit and innovation mindset',\r\n        'Multilingual communication (English, Swahili, local languages)'\r\n      ],\r\n      local_customs: [\r\n        'Harambee - community cooperation and fundraising',\r\n        'Greeting customs and respect protocols',\r\n        'Religious diversity and tolerance',\r\n        'Family-centered decision making'\r\n      ],\r\n      language_preferences: [\r\n        'Mix of English and Swahili phrases',\r\n        'Respectful and formal tone in business contexts',\r\n        'Storytelling and proverb usage',\r\n        'Community-focused language'\r\n      ],\r\n      seasonal_relevance: [\r\n        'Rainy seasons (March-May, October-December)',\r\n        'School calendar considerations',\r\n        'Agricultural seasons and harvest times',\r\n        'Holiday seasons and celebrations'\r\n      ],\r\n      local_events: [\r\n        'Nairobi Innovation Week',\r\n        'Kenya Music Festival',\r\n        'Nairobi Restaurant Week',\r\n        'Local cultural festivals'\r\n      ]\r\n    }\r\n  };\r\n\r\n  const locationKey = location.toLowerCase();\r\n  return culturalMap[locationKey] || {\r\n    location: location,\r\n    cultural_nuances: ['Local community values', 'Regional business customs'],\r\n    local_customs: ['Local traditions', 'Community practices'],\r\n    language_preferences: ['Local language nuances', 'Regional communication styles'],\r\n    seasonal_relevance: ['Local seasons', 'Regional events'],\r\n    local_events: ['Local festivals', 'Community gatherings']\r\n  };\r\n}\r\n\r\n/**\r\n * Generates complete market intelligence\r\n */\r\nexport function generateMarketIntelligence(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string,\r\n  services?: string\r\n): MarketIntelligence {\r\n  return {\r\n    trending_topics: generateStaticTrendingTopics(businessType, location, platform),\r\n    competitor_insights: generateCompetitorInsights(businessType, location, services),\r\n    cultural_context: generateCulturalContext(location),\r\n    viral_content_patterns: [\r\n      'Behind-the-scenes authentic moments',\r\n      'User-generated content and testimonials',\r\n      'Educational content that solves problems',\r\n      'Emotional storytelling with clear outcomes',\r\n      'Interactive content that encourages participation'\r\n    ],\r\n    engagement_triggers: [\r\n      'Ask questions that require personal responses',\r\n      'Share relatable struggles and solutions',\r\n      'Use local references and cultural touchpoints',\r\n      'Create content that people want to share with friends',\r\n      'Provide exclusive insights or early access'\r\n    ]\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;AAED;;AA4CO,eAAe,+BACpB,YAAoB,EACpB,QAAgB,EAChB,WAAmB,SAAS;IAE5B,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,aAAa,IAAI,EAAE,SAAS,GAAG,CAAC;QAChF,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,UAAU;QAEtC,0EAA0E;QAC1E,QAAQ,GAAG,CAAC;QACZ,MAAM,CAAC,cAAc,aAAa,GAAG,MAAM,QAAQ,UAAU,CAAC;YAC5D,CAAA,GAAA,6JAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;YAC5B,CAAA,GAAA,6JAAA,CAAA,oBAAiB,AAAD,EAAE,cAAc;SACjC;QAED,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,aAAa,MAAM,EAAE;QAC7D,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,aAAa,MAAM,EAAE;QAE7D,qDAAqD;QACrD,MAAM,gBAAgB;YAAE,QAAQ;YAAqB,QAAQ;QAAuB;QACpF,MAAM,cAAc;YAAE,QAAQ;YAAqB,QAAQ;QAAuB;QAElF,MAAM,YAA6B,EAAE;QAErC,wBAAwB;QACxB,IAAI,aAAa,MAAM,KAAK,aAAa;YACvC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,aAAa,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAC3E,UAAU,IAAI,IAAI,aAAa,KAAK,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;oBACjD,OAAO,MAAM,KAAK;oBAClB,gBAAgB,MAAM,cAAc;oBACpC,UAAU;oBACV,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS;oBAC1B,sBAAsB,MAAM,oBAAoB;gBAClD,CAAC;QACH;QAEA,yBAAyB;QACzB,IAAI,cAAc,MAAM,KAAK,aAAa;YACxC,UAAU,IAAI,IAAI,cAAc,KAAK,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;oBAClD,OAAO,MAAM,KAAK;oBAClB,gBAAgB,MAAM,cAAc;oBACpC,UAAU;oBACV,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS;oBAC1B,sBAAsB,MAAM,oBAAoB;gBAClD,CAAC;QACH;QAEA,eAAe;QACf,IAAI,YAAY,MAAM,KAAK,aAAa;YACtC,UAAU,IAAI,IAAI,YAAY,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC/C,OAAO,KAAK,KAAK;oBACjB,gBAAgB,KAAK,cAAc;oBACnC,UAAU;oBACV,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS;oBACzB,sBAAsB,KAAK,oBAAoB;gBACjD,CAAC;QACH,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,wBAAwB,CAAC,EAAE,aAAa,MAAM;QAC9D;QAEA,wBAAwB;QACxB,IAAI,aAAa,MAAM,KAAK,aAAa;YACvC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,aAAa,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAC3E,UAAU,IAAI,IAAI,aAAa,KAAK,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;oBACjD,OAAO,MAAM,KAAK;oBAClB,gBAAgB,MAAM,cAAc;oBACpC,UAAU;oBACV,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS;oBAC1B,sBAAsB,MAAM,oBAAoB;gBAClD,CAAC;QACH,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,wBAAwB,CAAC,EAAE,aAAa,MAAM;QAC9D;QAEA,wCAAwC;QACxC,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,MAAM,CAAC,iBAAiB,CAAC;YAC1D,OAAO,UACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,cAAc,GAAG,EAAE,cAAc,EAClD,KAAK,CAAC,GAAG;QACd;QAEA,4BAA4B;QAC5B,QAAQ,GAAG,CAAC;QACZ,OAAO,6BAA6B,cAAc,UAAU;IAE9D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,6BAA6B,cAAc,UAAU;IAC9D;AACF;AAKO,SAAS,6BACd,YAAoB,EACpB,QAAgB,EAChB,WAAmB,SAAS;IAG5B,MAAM,iBAAkD;QACtD,cAAc;YACZ;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;QACD,WAAW;YACT;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;QACD,cAAc;YACZ;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;QACD,UAAU;YACR;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;QACD,iCAAiC;YAC/B;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;IACH;IAEA,oCAAoC;IACpC,MAAM,aAAa,cAAc,CAAC,aAAa,WAAW,GAAG,IAAI,cAAc,CAAC,aAAa;IAE7F,+BAA+B;IAC/B,MAAM,iBAAiB,uBAAuB;IAE9C,iCAAiC;IACjC,MAAM,YAAY;WAAI;WAAe;KAAe;IAEpD,OAAO,UACJ,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK,YAAY,MAAM,QAAQ,KAAK,WAClE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,cAAc,GAAG,EAAE,cAAc,EAClD,KAAK,CAAC,GAAG;AACd;AAEA;;CAEC,GACD,SAAS,uBAAuB,QAAgB;IAC9C,MAAM,cAA+C;QACnD,WAAW;YACT;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;QACD,YAAY;YACV;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;QACD,UAAU;YACR;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;IACH;IAEA,MAAM,cAAc,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IAC7D,OAAO,WAAW,CAAC,YAAY,IAAI,EAAE;AACvC;AAKO,SAAS,2BACd,YAAoB,EACpB,QAAgB,EAChB,QAAiB;IAGjB,MAAM,uBAA4D;QAChE,iCAAiC;YAC/B;gBACE,iBAAiB;gBACjB,aAAa;gBACb,6BAA6B;gBAC7B,qBAAqB;gBACrB,gBAAgB;YAClB;YACA;gBACE,iBAAiB;gBACjB,aAAa;gBACb,6BAA6B;gBAC7B,qBAAqB;gBACrB,gBAAgB;YAClB;SACD;QACD,cAAc;YACZ;gBACE,iBAAiB;gBACjB,aAAa;gBACb,6BAA6B;gBAC7B,qBAAqB;gBACrB,gBAAgB;YAClB;SACD;QACD,WAAW;YACT;gBACE,iBAAiB;gBACjB,aAAa;gBACb,6BAA6B;gBAC7B,qBAAqB;gBACrB,gBAAgB;YAClB;SACD;IACH;IAEA,OAAO,oBAAoB,CAAC,aAAa,WAAW,GAAG,IAAI;QACzD;YACE,iBAAiB;YACjB,aAAa;YACb,6BAA6B;YAC7B,qBAAqB;YACrB,gBAAgB;QAClB;KACD;AACH;AAKO,SAAS,wBAAwB,QAAgB;IACtD,MAAM,cAA+C;QACnD,kBAAkB;YAChB,UAAU;YACV,kBAAkB;gBAChB;gBACA;gBACA;gBACA;aACD;YACD,eAAe;gBACb;gBACA;gBACA;gBACA;aACD;YACD,sBAAsB;gBACpB;gBACA;gBACA;gBACA;aACD;YACD,oBAAoB;gBAClB;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,MAAM,cAAc,SAAS,WAAW;IACxC,OAAO,WAAW,CAAC,YAAY,IAAI;QACjC,UAAU;QACV,kBAAkB;YAAC;YAA0B;SAA4B;QACzE,eAAe;YAAC;YAAoB;SAAsB;QAC1D,sBAAsB;YAAC;YAA0B;SAAgC;QACjF,oBAAoB;YAAC;YAAiB;SAAkB;QACxD,cAAc;YAAC;YAAmB;SAAuB;IAC3D;AACF;AAKO,SAAS,2BACd,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,QAAiB;IAEjB,OAAO;QACL,iBAAiB,6BAA6B,cAAc,UAAU;QACtE,qBAAqB,2BAA2B,cAAc,UAAU;QACxE,kBAAkB,wBAAwB;QAC1C,wBAAwB;YACtB;YACA;YACA;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 3743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/intelligent-context-selector.ts"], "sourcesContent": ["/**\r\n * Intelligent Context Selector\r\n * \r\n * This module acts like a local expert who knows what information\r\n * is relevant for each business type, location, and content context.\r\n * It intelligently selects which data to use and which to ignore.\r\n */\r\n\r\nexport interface ContextRelevance {\r\n  weather: {\r\n    useWeather: boolean;\r\n    relevanceReason: string;\r\n    priority: 'high' | 'medium' | 'low' | 'ignore';\r\n  };\r\n  events: {\r\n    useEvents: boolean;\r\n    relevanceReason: string;\r\n    priority: 'high' | 'medium' | 'low' | 'ignore';\r\n    eventTypes: string[];\r\n  };\r\n  trends: {\r\n    useTrends: boolean;\r\n    relevanceReason: string;\r\n    priority: 'high' | 'medium' | 'low' | 'ignore';\r\n    trendTypes: string[];\r\n  };\r\n  cultural: {\r\n    useCultural: boolean;\r\n    relevanceReason: string;\r\n    priority: 'high' | 'medium' | 'low' | 'ignore';\r\n    culturalElements: string[];\r\n  };\r\n}\r\n\r\n/**\r\n * Intelligently determines what context information to use\r\n * based on business type, location, and content purpose\r\n */\r\nexport function selectRelevantContext(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string,\r\n  contentThemes?: string,\r\n  dayOfWeek?: string\r\n): ContextRelevance {\r\n  \r\n  const businessKey = businessType.toLowerCase();\r\n  const locationKey = location.toLowerCase();\r\n  const isWeekend = dayOfWeek === 'Saturday' || dayOfWeek === 'Sunday';\r\n  \r\n  return {\r\n    weather: analyzeWeatherRelevance(businessKey, locationKey, platform, isWeekend),\r\n    events: analyzeEventsRelevance(businessKey, locationKey, platform, isWeekend),\r\n    trends: analyzeTrendsRelevance(businessKey, locationKey, platform),\r\n    cultural: analyzeCulturalRelevance(businessKey, locationKey, platform)\r\n  };\r\n}\r\n\r\n/**\r\n * Determines if weather information is relevant for this business/location\r\n */\r\nfunction analyzeWeatherRelevance(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string,\r\n  isWeekend: boolean\r\n): ContextRelevance['weather'] {\r\n  \r\n  // High weather relevance businesses\r\n  const weatherSensitiveBusinesses = [\r\n    'restaurant', 'cafe', 'food', 'dining',\r\n    'fitness', 'gym', 'sports', 'outdoor',\r\n    'retail', 'shopping', 'fashion',\r\n    'tourism', 'travel', 'hotel',\r\n    'construction', 'agriculture',\r\n    'delivery', 'transportation'\r\n  ];\r\n  \r\n  // Medium weather relevance\r\n  const moderateWeatherBusinesses = [\r\n    'beauty', 'spa', 'wellness',\r\n    'entertainment', 'events',\r\n    'real estate', 'automotive'\r\n  ];\r\n  \r\n  // Low/No weather relevance\r\n  const weatherIndependentBusinesses = [\r\n    'financial technology software', 'fintech', 'banking',\r\n    'software', 'technology', 'saas',\r\n    'consulting', 'legal', 'accounting',\r\n    'insurance', 'healthcare', 'education',\r\n    'digital marketing', 'design'\r\n  ];\r\n  \r\n  // Check business type relevance\r\n  const isHighRelevance = weatherSensitiveBusinesses.some(type => \r\n    businessType.includes(type)\r\n  );\r\n  \r\n  const isMediumRelevance = moderateWeatherBusinesses.some(type => \r\n    businessType.includes(type)\r\n  );\r\n  \r\n  const isLowRelevance = weatherIndependentBusinesses.some(type => \r\n    businessType.includes(type)\r\n  );\r\n  \r\n  // Location-based adjustments\r\n  const isExtremeWeatherLocation = location.includes('nairobi') || \r\n                                   location.includes('kenya') ||\r\n                                   location.includes('tropical');\r\n  \r\n  if (isHighRelevance) {\r\n    return {\r\n      useWeather: true,\r\n      relevanceReason: `${businessType} customers are highly influenced by weather conditions`,\r\n      priority: 'high'\r\n    };\r\n  }\r\n  \r\n  if (isMediumRelevance) {\r\n    return {\r\n      useWeather: true,\r\n      relevanceReason: `Weather can impact ${businessType} customer behavior`,\r\n      priority: 'medium'\r\n    };\r\n  }\r\n  \r\n  if (isLowRelevance) {\r\n    return {\r\n      useWeather: false,\r\n      relevanceReason: `${businessType} operates independently of weather conditions`,\r\n      priority: 'ignore'\r\n    };\r\n  }\r\n  \r\n  // Default case\r\n  return {\r\n    useWeather: isExtremeWeatherLocation,\r\n    relevanceReason: isExtremeWeatherLocation ? \r\n      'Local weather is culturally significant' : \r\n      'Weather has minimal business impact',\r\n    priority: isExtremeWeatherLocation ? 'low' : 'ignore'\r\n  };\r\n}\r\n\r\n/**\r\n * Determines if local events are relevant for this business/location\r\n */\r\nfunction analyzeEventsRelevance(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string,\r\n  isWeekend: boolean\r\n): ContextRelevance['events'] {\r\n  \r\n  // Always relevant for networking/community businesses\r\n  const networkingBusinesses = [\r\n    'consulting', 'marketing', 'business services',\r\n    'financial technology software', 'fintech',\r\n    'real estate', 'insurance', 'legal'\r\n  ];\r\n  \r\n  // Event-dependent businesses\r\n  const eventDependentBusinesses = [\r\n    'restaurant', 'entertainment', 'retail',\r\n    'fitness', 'beauty', 'tourism'\r\n  ];\r\n  \r\n  // B2B vs B2C consideration\r\n  const isB2B = networkingBusinesses.some(type => businessType.includes(type)) ||\r\n                businessType.includes('software') ||\r\n                businessType.includes('technology');\r\n  \r\n  const isB2C = eventDependentBusinesses.some(type => businessType.includes(type));\r\n  \r\n  // Relevant event types based on business\r\n  let eventTypes: string[] = [];\r\n  \r\n  if (isB2B) {\r\n    eventTypes = ['business', 'networking', 'conference', 'workshop', 'professional'];\r\n  }\r\n  \r\n  if (isB2C) {\r\n    eventTypes = ['community', 'festival', 'entertainment', 'cultural', 'local'];\r\n  }\r\n  \r\n  // Location-based event culture\r\n  const isEventCentricLocation = location.includes('nairobi') ||\r\n                                 location.includes('new york') ||\r\n                                 location.includes('london');\r\n  \r\n  if (isB2B && isEventCentricLocation) {\r\n    return {\r\n      useEvents: true,\r\n      relevanceReason: `${businessType} benefits from professional networking events`,\r\n      priority: 'high',\r\n      eventTypes\r\n    };\r\n  }\r\n  \r\n  if (isB2C) {\r\n    return {\r\n      useEvents: true,\r\n      relevanceReason: `Local events drive foot traffic for ${businessType}`,\r\n      priority: 'medium',\r\n      eventTypes\r\n    };\r\n  }\r\n  \r\n  return {\r\n    useEvents: isEventCentricLocation,\r\n    relevanceReason: isEventCentricLocation ? \r\n      'Local events show community engagement' : \r\n      'Events have minimal business relevance',\r\n    priority: isEventCentricLocation ? 'low' : 'ignore',\r\n    eventTypes: ['community']\r\n  };\r\n}\r\n\r\n/**\r\n * Determines trending topics relevance\r\n */\r\nfunction analyzeTrendsRelevance(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string\r\n): ContextRelevance['trends'] {\r\n  \r\n  // Always use trends for social media businesses\r\n  const trendDependentBusinesses = [\r\n    'marketing', 'social media', 'content',\r\n    'entertainment', 'fashion', 'beauty',\r\n    'technology', 'startup'\r\n  ];\r\n  \r\n  // Industry-specific trend types\r\n  let trendTypes: string[] = [];\r\n  \r\n  if (businessType.includes('technology') || businessType.includes('fintech')) {\r\n    trendTypes = ['technology', 'business', 'innovation', 'startup'];\r\n  } else if (businessType.includes('restaurant') || businessType.includes('food')) {\r\n    trendTypes = ['food', 'lifestyle', 'local', 'cultural'];\r\n  } else if (businessType.includes('fitness')) {\r\n    trendTypes = ['health', 'wellness', 'lifestyle', 'sports'];\r\n  } else {\r\n    trendTypes = ['business', 'local', 'community'];\r\n  }\r\n  \r\n  const isTrendSensitive = trendDependentBusinesses.some(type => \r\n    businessType.includes(type)\r\n  );\r\n  \r\n  // Platform consideration\r\n  const isSocialPlatform = platform === 'instagram' || platform === 'twitter';\r\n  \r\n  return {\r\n    useTrends: true, // Most businesses benefit from some trending topics\r\n    relevanceReason: isTrendSensitive ? \r\n      `${businessType} thrives on current trends and conversations` :\r\n      'Trending topics increase content relevance and engagement',\r\n    priority: isTrendSensitive ? 'high' : 'medium',\r\n    trendTypes\r\n  };\r\n}\r\n\r\n/**\r\n * Determines cultural context relevance\r\n */\r\nfunction analyzeCulturalRelevance(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string\r\n): ContextRelevance['cultural'] {\r\n  \r\n  // Always high relevance for local businesses\r\n  const localBusinesses = [\r\n    'restaurant', 'retail', 'fitness', 'beauty',\r\n    'real estate', 'healthcare', 'education'\r\n  ];\r\n  \r\n  // Cultural elements to emphasize\r\n  let culturalElements: string[] = [];\r\n  \r\n  if (location.includes('nairobi') || location.includes('kenya')) {\r\n    culturalElements = ['ubuntu philosophy', 'harambee spirit', 'swahili expressions', 'community values'];\r\n  } else if (location.includes('new york')) {\r\n    culturalElements = ['diversity', 'hustle culture', 'innovation', 'fast-paced lifestyle'];\r\n  } else if (location.includes('london')) {\r\n    culturalElements = ['tradition', 'multiculturalism', 'business etiquette', 'dry humor'];\r\n  } else {\r\n    culturalElements = ['local customs', 'community values', 'regional preferences'];\r\n  }\r\n  \r\n  const isLocalBusiness = localBusinesses.some(type => businessType.includes(type));\r\n  const isInternationalLocation = !location.includes('united states');\r\n  \r\n  return {\r\n    useCultural: true, // Cultural context is almost always relevant\r\n    relevanceReason: isLocalBusiness ? \r\n      `Local ${businessType} must connect with community culture` :\r\n      'Cultural awareness builds authentic connections',\r\n    priority: isLocalBusiness || isInternationalLocation ? 'high' : 'medium',\r\n    culturalElements\r\n  };\r\n}\r\n\r\n/**\r\n * Filters and prioritizes available context data based on relevance analysis\r\n */\r\nexport function filterContextData(\r\n  relevance: ContextRelevance,\r\n  availableData: {\r\n    weather?: any;\r\n    events?: any[];\r\n    trends?: any[];\r\n    cultural?: any;\r\n  }\r\n): {\r\n  selectedWeather?: any;\r\n  selectedEvents?: any[];\r\n  selectedTrends?: any[];\r\n  selectedCultural?: any;\r\n  contextInstructions: string;\r\n} {\r\n  \r\n  const result: any = {\r\n    contextInstructions: generateContextInstructions(relevance)\r\n  };\r\n  \r\n  // Filter weather data\r\n  if (relevance.weather.useWeather && availableData.weather) {\r\n    result.selectedWeather = availableData.weather;\r\n  }\r\n  \r\n  // Filter events data\r\n  if (relevance.events.useEvents && availableData.events) {\r\n    result.selectedEvents = availableData.events\r\n      .filter(event => \r\n        relevance.events.eventTypes.some(type => \r\n          event.category?.toLowerCase().includes(type) ||\r\n          event.name?.toLowerCase().includes(type)\r\n        )\r\n      )\r\n      .slice(0, relevance.events.priority === 'high' ? 3 : 1);\r\n  }\r\n  \r\n  // Filter trends data\r\n  if (relevance.trends.useTrends && availableData.trends) {\r\n    result.selectedTrends = availableData.trends\r\n      .filter(trend => \r\n        relevance.trends.trendTypes.some(type => \r\n          trend.category?.toLowerCase().includes(type) ||\r\n          trend.topic?.toLowerCase().includes(type)\r\n        )\r\n      )\r\n      .slice(0, relevance.trends.priority === 'high' ? 5 : 3);\r\n  }\r\n  \r\n  // Filter cultural data\r\n  if (relevance.cultural.useCultural && availableData.cultural) {\r\n    result.selectedCultural = {\r\n      ...availableData.cultural,\r\n      cultural_nuances: availableData.cultural.cultural_nuances?.filter(nuance =>\r\n        relevance.cultural.culturalElements.some(element =>\r\n          nuance.toLowerCase().includes(element.toLowerCase())\r\n        )\r\n      ).slice(0, 3)\r\n    };\r\n  }\r\n  \r\n  return result;\r\n}\r\n\r\n/**\r\n * Generates context-specific instructions for the AI\r\n */\r\nfunction generateContextInstructions(relevance: ContextRelevance): string {\r\n  const instructions: string[] = [];\r\n  \r\n  if (relevance.weather.useWeather) {\r\n    if (relevance.weather.priority === 'high') {\r\n      instructions.push('WEATHER: Integrate weather naturally as it significantly impacts customer behavior');\r\n    } else if (relevance.weather.priority === 'medium') {\r\n      instructions.push('WEATHER: Mention weather subtly if it adds value to the message');\r\n    }\r\n  } else {\r\n    instructions.push('WEATHER: Ignore weather data - not relevant for this business type');\r\n  }\r\n  \r\n  if (relevance.events.useEvents) {\r\n    if (relevance.events.priority === 'high') {\r\n      instructions.push('EVENTS: Highlight relevant local events as key business opportunities');\r\n    } else {\r\n      instructions.push('EVENTS: Reference events only if they add community connection value');\r\n    }\r\n  } else {\r\n    instructions.push('EVENTS: Skip event references - focus on core business value');\r\n  }\r\n  \r\n  if (relevance.trends.priority === 'high') {\r\n    instructions.push('TRENDS: Lead with trending topics to maximize engagement and relevance');\r\n  } else {\r\n    instructions.push('TRENDS: Use trends subtly to add contemporary relevance');\r\n  }\r\n  \r\n  if (relevance.cultural.priority === 'high') {\r\n    instructions.push('CULTURE: Deeply integrate local cultural elements for authentic connection');\r\n  } else {\r\n    instructions.push('CULTURE: Include respectful cultural awareness without overdoing it');\r\n  }\r\n  \r\n  return instructions.join('\\n');\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;AAgCM,SAAS,sBACd,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,aAAsB,EACtB,SAAkB;IAGlB,MAAM,cAAc,aAAa,WAAW;IAC5C,MAAM,cAAc,SAAS,WAAW;IACxC,MAAM,YAAY,cAAc,cAAc,cAAc;IAE5D,OAAO;QACL,SAAS,wBAAwB,aAAa,aAAa,UAAU;QACrE,QAAQ,uBAAuB,aAAa,aAAa,UAAU;QACnE,QAAQ,uBAAuB,aAAa,aAAa;QACzD,UAAU,yBAAyB,aAAa,aAAa;IAC/D;AACF;AAEA;;CAEC,GACD,SAAS,wBACP,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,SAAkB;IAGlB,oCAAoC;IACpC,MAAM,6BAA6B;QACjC;QAAc;QAAQ;QAAQ;QAC9B;QAAW;QAAO;QAAU;QAC5B;QAAU;QAAY;QACtB;QAAW;QAAU;QACrB;QAAgB;QAChB;QAAY;KACb;IAED,2BAA2B;IAC3B,MAAM,4BAA4B;QAChC;QAAU;QAAO;QACjB;QAAiB;QACjB;QAAe;KAChB;IAED,2BAA2B;IAC3B,MAAM,+BAA+B;QACnC;QAAiC;QAAW;QAC5C;QAAY;QAAc;QAC1B;QAAc;QAAS;QACvB;QAAa;QAAc;QAC3B;QAAqB;KACtB;IAED,gCAAgC;IAChC,MAAM,kBAAkB,2BAA2B,IAAI,CAAC,CAAA,OACtD,aAAa,QAAQ,CAAC;IAGxB,MAAM,oBAAoB,0BAA0B,IAAI,CAAC,CAAA,OACvD,aAAa,QAAQ,CAAC;IAGxB,MAAM,iBAAiB,6BAA6B,IAAI,CAAC,CAAA,OACvD,aAAa,QAAQ,CAAC;IAGxB,6BAA6B;IAC7B,MAAM,2BAA2B,SAAS,QAAQ,CAAC,cAClB,SAAS,QAAQ,CAAC,YAClB,SAAS,QAAQ,CAAC;IAEnD,IAAI,iBAAiB;QACnB,OAAO;YACL,YAAY;YACZ,iBAAiB,GAAG,aAAa,sDAAsD,CAAC;YACxF,UAAU;QACZ;IACF;IAEA,IAAI,mBAAmB;QACrB,OAAO;YACL,YAAY;YACZ,iBAAiB,CAAC,mBAAmB,EAAE,aAAa,kBAAkB,CAAC;YACvE,UAAU;QACZ;IACF;IAEA,IAAI,gBAAgB;QAClB,OAAO;YACL,YAAY;YACZ,iBAAiB,GAAG,aAAa,6CAA6C,CAAC;YAC/E,UAAU;QACZ;IACF;IAEA,eAAe;IACf,OAAO;QACL,YAAY;QACZ,iBAAiB,2BACf,4CACA;QACF,UAAU,2BAA2B,QAAQ;IAC/C;AACF;AAEA;;CAEC,GACD,SAAS,uBACP,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,SAAkB;IAGlB,sDAAsD;IACtD,MAAM,uBAAuB;QAC3B;QAAc;QAAa;QAC3B;QAAiC;QACjC;QAAe;QAAa;KAC7B;IAED,6BAA6B;IAC7B,MAAM,2BAA2B;QAC/B;QAAc;QAAiB;QAC/B;QAAW;QAAU;KACtB;IAED,2BAA2B;IAC3B,MAAM,QAAQ,qBAAqB,IAAI,CAAC,CAAA,OAAQ,aAAa,QAAQ,CAAC,UACxD,aAAa,QAAQ,CAAC,eACtB,aAAa,QAAQ,CAAC;IAEpC,MAAM,QAAQ,yBAAyB,IAAI,CAAC,CAAA,OAAQ,aAAa,QAAQ,CAAC;IAE1E,yCAAyC;IACzC,IAAI,aAAuB,EAAE;IAE7B,IAAI,OAAO;QACT,aAAa;YAAC;YAAY;YAAc;YAAc;YAAY;SAAe;IACnF;IAEA,IAAI,OAAO;QACT,aAAa;YAAC;YAAa;YAAY;YAAiB;YAAY;SAAQ;IAC9E;IAEA,+BAA+B;IAC/B,MAAM,yBAAyB,SAAS,QAAQ,CAAC,cAClB,SAAS,QAAQ,CAAC,eAClB,SAAS,QAAQ,CAAC;IAEjD,IAAI,SAAS,wBAAwB;QACnC,OAAO;YACL,WAAW;YACX,iBAAiB,GAAG,aAAa,6CAA6C,CAAC;YAC/E,UAAU;YACV;QACF;IACF;IAEA,IAAI,OAAO;QACT,OAAO;YACL,WAAW;YACX,iBAAiB,CAAC,oCAAoC,EAAE,cAAc;YACtE,UAAU;YACV;QACF;IACF;IAEA,OAAO;QACL,WAAW;QACX,iBAAiB,yBACf,2CACA;QACF,UAAU,yBAAyB,QAAQ;QAC3C,YAAY;YAAC;SAAY;IAC3B;AACF;AAEA;;CAEC,GACD,SAAS,uBACP,YAAoB,EACpB,QAAgB,EAChB,QAAgB;IAGhB,gDAAgD;IAChD,MAAM,2BAA2B;QAC/B;QAAa;QAAgB;QAC7B;QAAiB;QAAW;QAC5B;QAAc;KACf;IAED,gCAAgC;IAChC,IAAI,aAAuB,EAAE;IAE7B,IAAI,aAAa,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,YAAY;QAC3E,aAAa;YAAC;YAAc;YAAY;YAAc;SAAU;IAClE,OAAO,IAAI,aAAa,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,SAAS;QAC/E,aAAa;YAAC;YAAQ;YAAa;YAAS;SAAW;IACzD,OAAO,IAAI,aAAa,QAAQ,CAAC,YAAY;QAC3C,aAAa;YAAC;YAAU;YAAY;YAAa;SAAS;IAC5D,OAAO;QACL,aAAa;YAAC;YAAY;YAAS;SAAY;IACjD;IAEA,MAAM,mBAAmB,yBAAyB,IAAI,CAAC,CAAA,OACrD,aAAa,QAAQ,CAAC;IAGxB,yBAAyB;IACzB,MAAM,mBAAmB,aAAa,eAAe,aAAa;IAElE,OAAO;QACL,WAAW;QACX,iBAAiB,mBACf,GAAG,aAAa,4CAA4C,CAAC,GAC7D;QACF,UAAU,mBAAmB,SAAS;QACtC;IACF;AACF;AAEA;;CAEC,GACD,SAAS,yBACP,YAAoB,EACpB,QAAgB,EAChB,QAAgB;IAGhB,6CAA6C;IAC7C,MAAM,kBAAkB;QACtB;QAAc;QAAU;QAAW;QACnC;QAAe;QAAc;KAC9B;IAED,iCAAiC;IACjC,IAAI,mBAA6B,EAAE;IAEnC,IAAI,SAAS,QAAQ,CAAC,cAAc,SAAS,QAAQ,CAAC,UAAU;QAC9D,mBAAmB;YAAC;YAAqB;YAAmB;YAAuB;SAAmB;IACxG,OAAO,IAAI,SAAS,QAAQ,CAAC,aAAa;QACxC,mBAAmB;YAAC;YAAa;YAAkB;YAAc;SAAuB;IAC1F,OAAO,IAAI,SAAS,QAAQ,CAAC,WAAW;QACtC,mBAAmB;YAAC;YAAa;YAAoB;YAAsB;SAAY;IACzF,OAAO;QACL,mBAAmB;YAAC;YAAiB;YAAoB;SAAuB;IAClF;IAEA,MAAM,kBAAkB,gBAAgB,IAAI,CAAC,CAAA,OAAQ,aAAa,QAAQ,CAAC;IAC3E,MAAM,0BAA0B,CAAC,SAAS,QAAQ,CAAC;IAEnD,OAAO;QACL,aAAa;QACb,iBAAiB,kBACf,CAAC,MAAM,EAAE,aAAa,oCAAoC,CAAC,GAC3D;QACF,UAAU,mBAAmB,0BAA0B,SAAS;QAChE;IACF;AACF;AAKO,SAAS,kBACd,SAA2B,EAC3B,aAKC;IASD,MAAM,SAAc;QAClB,qBAAqB,4BAA4B;IACnD;IAEA,sBAAsB;IACtB,IAAI,UAAU,OAAO,CAAC,UAAU,IAAI,cAAc,OAAO,EAAE;QACzD,OAAO,eAAe,GAAG,cAAc,OAAO;IAChD;IAEA,qBAAqB;IACrB,IAAI,UAAU,MAAM,CAAC,SAAS,IAAI,cAAc,MAAM,EAAE;QACtD,OAAO,cAAc,GAAG,cAAc,MAAM,CACzC,MAAM,CAAC,CAAA,QACN,UAAU,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA,OAC/B,MAAM,QAAQ,EAAE,cAAc,SAAS,SACvC,MAAM,IAAI,EAAE,cAAc,SAAS,QAGtC,KAAK,CAAC,GAAG,UAAU,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI;IACzD;IAEA,qBAAqB;IACrB,IAAI,UAAU,MAAM,CAAC,SAAS,IAAI,cAAc,MAAM,EAAE;QACtD,OAAO,cAAc,GAAG,cAAc,MAAM,CACzC,MAAM,CAAC,CAAA,QACN,UAAU,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA,OAC/B,MAAM,QAAQ,EAAE,cAAc,SAAS,SACvC,MAAM,KAAK,EAAE,cAAc,SAAS,QAGvC,KAAK,CAAC,GAAG,UAAU,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI;IACzD;IAEA,uBAAuB;IACvB,IAAI,UAAU,QAAQ,CAAC,WAAW,IAAI,cAAc,QAAQ,EAAE;QAC5D,OAAO,gBAAgB,GAAG;YACxB,GAAG,cAAc,QAAQ;YACzB,kBAAkB,cAAc,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAA,SAChE,UAAU,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,UACvC,OAAO,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW,MAEnD,MAAM,GAAG;QACb;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,4BAA4B,SAA2B;IAC9D,MAAM,eAAyB,EAAE;IAEjC,IAAI,UAAU,OAAO,CAAC,UAAU,EAAE;QAChC,IAAI,UAAU,OAAO,CAAC,QAAQ,KAAK,QAAQ;YACzC,aAAa,IAAI,CAAC;QACpB,OAAO,IAAI,UAAU,OAAO,CAAC,QAAQ,KAAK,UAAU;YAClD,aAAa,IAAI,CAAC;QACpB;IACF,OAAO;QACL,aAAa,IAAI,CAAC;IACpB;IAEA,IAAI,UAAU,MAAM,CAAC,SAAS,EAAE;QAC9B,IAAI,UAAU,MAAM,CAAC,QAAQ,KAAK,QAAQ;YACxC,aAAa,IAAI,CAAC;QACpB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;IACF,OAAO;QACL,aAAa,IAAI,CAAC;IACpB;IAEA,IAAI,UAAU,MAAM,CAAC,QAAQ,KAAK,QAAQ;QACxC,aAAa,IAAI,CAAC;IACpB,OAAO;QACL,aAAa,IAAI,CAAC;IACpB;IAEA,IAAI,UAAU,QAAQ,CAAC,QAAQ,KAAK,QAAQ;QAC1C,aAAa,IAAI,CAAC;IACpB,OAAO;QACL,aAAa,IAAI,CAAC;IACpB;IAEA,OAAO,aAAa,IAAI,CAAC;AAC3B", "debugId": null}}, {"offset": {"line": 4093, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/human-content-generator.ts"], "sourcesContent": ["/**\r\n * Human-Like Content Generation System\r\n * \r\n * This module provides techniques to make AI-generated content\r\n * feel authentic, human, and engaging while avoiding AI detection.\r\n */\r\n\r\nexport interface HumanizationTechniques {\r\n  personality_markers: string[];\r\n  authenticity_elements: string[];\r\n  conversational_patterns: string[];\r\n  storytelling_devices: string[];\r\n  emotional_connectors: string[];\r\n  imperfection_markers: string[];\r\n}\r\n\r\nexport interface TrafficDrivingElements {\r\n  viral_hooks: string[];\r\n  engagement_magnets: string[];\r\n  conversion_triggers: string[];\r\n  shareability_factors: string[];\r\n  curiosity_gaps: string[];\r\n  social_proof_elements: string[];\r\n}\r\n\r\n/**\r\n * Generates human-like content techniques based on business type and brand voice\r\n */\r\nexport function generateHumanizationTechniques(\r\n  businessType: string,\r\n  brandVoice: string,\r\n  location: string\r\n): HumanizationTechniques {\r\n  \r\n  const basePersonality = getPersonalityMarkers(brandVoice);\r\n  const industryAuthenticity = getIndustryAuthenticity(businessType);\r\n  const locationConversation = getLocationConversation(location);\r\n  \r\n  return {\r\n    personality_markers: [\r\n      ...basePersonality,\r\n      'Use first-person perspective occasionally',\r\n      'Include personal opinions and preferences',\r\n      'Show vulnerability and learning moments',\r\n      'Express genuine excitement about successes'\r\n    ],\r\n    authenticity_elements: [\r\n      ...industryAuthenticity,\r\n      'Share behind-the-scenes moments',\r\n      'Admit mistakes and lessons learned',\r\n      'Use specific details instead of generalities',\r\n      'Reference real experiences and observations',\r\n      'Include time-specific references (today, this morning, etc.)'\r\n    ],\r\n    conversational_patterns: [\r\n      ...locationConversation,\r\n      'Start sentences with \"You know what?\"',\r\n      'Use rhetorical questions naturally',\r\n      'Include conversational fillers like \"honestly\" or \"actually\"',\r\n      'Break up long thoughts with shorter sentences',\r\n      'Use contractions (we\\'re, don\\'t, can\\'t) naturally'\r\n    ],\r\n    storytelling_devices: [\r\n      'Start with \"I remember when...\" or \"Last week...\"',\r\n      'Use the \"But here\\'s the thing...\" transition',\r\n      'Include dialogue: \"My customer said...\"',\r\n      'Paint vivid scenes with sensory details',\r\n      'End with unexpected insights or realizations'\r\n    ],\r\n    emotional_connectors: [\r\n      'Share moments of doubt and breakthrough',\r\n      'Express genuine gratitude to customers',\r\n      'Show empathy for customer struggles',\r\n      'Celebrate small wins with enthusiasm',\r\n      'Use emotional language that resonates'\r\n    ],\r\n    imperfection_markers: [\r\n      'Occasional typos that feel natural (but not distracting)',\r\n      'Slightly informal grammar in casual contexts',\r\n      'Stream-of-consciousness moments',\r\n      'Self-corrections: \"Actually, let me rephrase that...\"',\r\n      'Honest admissions: \"I\\'m still figuring this out...\"'\r\n    ]\r\n  };\r\n}\r\n\r\n/**\r\n * Generates traffic-driving content elements\r\n */\r\nexport function generateTrafficDrivingElements(\r\n  businessType: string,\r\n  platform: string,\r\n  targetAudience?: string\r\n): TrafficDrivingElements {\r\n  \r\n  return {\r\n    viral_hooks: [\r\n      'Controversial but respectful opinions',\r\n      'Surprising industry statistics',\r\n      'Before/after transformations',\r\n      'Myth-busting content',\r\n      'Exclusive behind-the-scenes reveals',\r\n      'Timely reactions to trending topics',\r\n      'Unexpected collaborations or partnerships'\r\n    ],\r\n    engagement_magnets: [\r\n      'Fill-in-the-blank questions',\r\n      'This or that choices',\r\n      'Caption this photo challenges',\r\n      'Share your experience prompts',\r\n      'Prediction requests',\r\n      'Opinion polls and surveys',\r\n      'Challenge participation invites'\r\n    ],\r\n    conversion_triggers: [\r\n      'Limited-time offers with urgency',\r\n      'Exclusive access for followers',\r\n      'Free valuable resources',\r\n      'Personal consultation offers',\r\n      'Early bird opportunities',\r\n      'Member-only benefits',\r\n      'Referral incentives'\r\n    ],\r\n    shareability_factors: [\r\n      'Relatable everyday struggles',\r\n      'Inspirational success stories',\r\n      'Useful tips people want to save',\r\n      'Funny observations about the industry',\r\n      'Heartwarming customer stories',\r\n      'Educational content that teaches',\r\n      'Content that makes people look smart for sharing'\r\n    ],\r\n    curiosity_gaps: [\r\n      'The one thing nobody tells you about...',\r\n      'What happened next will surprise you...',\r\n      'The secret that changed everything...',\r\n      'Why everyone is wrong about...',\r\n      'The mistake I made that taught me...',\r\n      'What I wish I knew before...',\r\n      'The truth about... that nobody talks about'\r\n    ],\r\n    social_proof_elements: [\r\n      'Customer testimonials and reviews',\r\n      'User-generated content features',\r\n      'Industry recognition and awards',\r\n      'Media mentions and press coverage',\r\n      'Collaboration with respected figures',\r\n      'Community size and engagement stats',\r\n      'Success metrics and achievements'\r\n    ]\r\n  };\r\n}\r\n\r\n/**\r\n * Gets personality markers based on brand voice\r\n */\r\nfunction getPersonalityMarkers(brandVoice: string): string[] {\r\n  const voiceMap: Record<string, string[]> = {\r\n    'friendly': [\r\n      'Use warm, welcoming language',\r\n      'Include friendly greetings and sign-offs',\r\n      'Show genuine interest in followers',\r\n      'Use inclusive language that brings people together'\r\n    ],\r\n    'professional': [\r\n      'Maintain expertise while being approachable',\r\n      'Use industry knowledge to build authority',\r\n      'Balance formal tone with personal touches',\r\n      'Show competence through specific examples'\r\n    ],\r\n    'casual': [\r\n      'Use everyday language and slang appropriately',\r\n      'Be relaxed and conversational',\r\n      'Include humor and light-hearted moments',\r\n      'Feel like talking to a friend'\r\n    ],\r\n    'innovative': [\r\n      'Show forward-thinking perspectives',\r\n      'Challenge conventional wisdom respectfully',\r\n      'Share cutting-edge insights',\r\n      'Express excitement about new possibilities'\r\n    ]\r\n  };\r\n\r\n  // Extract key words from brand voice description\r\n  const lowerVoice = brandVoice.toLowerCase();\r\n  for (const [key, markers] of Object.entries(voiceMap)) {\r\n    if (lowerVoice.includes(key)) {\r\n      return markers;\r\n    }\r\n  }\r\n\r\n  return voiceMap['friendly']; // Default fallback\r\n}\r\n\r\n/**\r\n * Gets industry-specific authenticity elements\r\n */\r\nfunction getIndustryAuthenticity(businessType: string): string[] {\r\n  const industryMap: Record<string, string[]> = {\r\n    'restaurant': [\r\n      'Share cooking failures and successes',\r\n      'Talk about ingredient sourcing stories',\r\n      'Mention customer reactions and feedback',\r\n      'Describe the sensory experience of food'\r\n    ],\r\n    'fitness': [\r\n      'Share personal workout struggles',\r\n      'Admit to having off days',\r\n      'Celebrate client progress genuinely',\r\n      'Talk about the mental health benefits'\r\n    ],\r\n    'technology': [\r\n      'Explain complex concepts simply',\r\n      'Share debugging stories and solutions',\r\n      'Admit when technology isn\\'t perfect',\r\n      'Focus on human impact of technology'\r\n    ],\r\n    'financial technology software': [\r\n      'Share stories about financial inclusion impact',\r\n      'Explain complex financial concepts simply',\r\n      'Highlight real customer success stories',\r\n      'Address common financial fears and concerns',\r\n      'Show the human side of financial technology'\r\n    ],\r\n    'beauty': [\r\n      'Share makeup fails and learning moments',\r\n      'Talk about skin struggles and solutions',\r\n      'Celebrate diverse beauty standards',\r\n      'Share product testing experiences'\r\n    ]\r\n  };\r\n\r\n  return industryMap[businessType.toLowerCase()] || [\r\n    'Share real customer interactions',\r\n    'Talk about daily business challenges',\r\n    'Celebrate small business wins',\r\n    'Show the human side of your industry'\r\n  ];\r\n}\r\n\r\n/**\r\n * Gets location-specific conversational patterns\r\n */\r\nfunction getLocationConversation(location: string): string[] {\r\n  const locationMap: Record<string, string[]> = {\r\n    'nairobi': [\r\n      'Use occasional Swahili phrases naturally',\r\n      'Reference local landmarks and experiences',\r\n      'Include community-focused language',\r\n      'Show respect for local customs and values'\r\n    ],\r\n    'new york': [\r\n      'Use direct, fast-paced communication',\r\n      'Reference city experiences and culture',\r\n      'Include diverse perspectives',\r\n      'Show hustle and ambition'\r\n    ],\r\n    'london': [\r\n      'Use British expressions naturally',\r\n      'Include dry humor appropriately',\r\n      'Reference local culture and experiences',\r\n      'Maintain polite but direct communication'\r\n    ]\r\n  };\r\n\r\n  const locationKey = location.toLowerCase().split(',')[0].trim();\r\n  return locationMap[locationKey] || [\r\n    'Use local expressions and references',\r\n    'Include regional cultural touchpoints',\r\n    'Show understanding of local context',\r\n    'Connect with community values'\r\n  ];\r\n}\r\n\r\n/**\r\n * Generates content optimization strategies for maximum engagement\r\n */\r\nexport function generateContentOptimization(\r\n  platform: string,\r\n  businessType: string,\r\n  timeOfDay: string = 'morning'\r\n): {\r\n  posting_strategy: string[];\r\n  engagement_timing: string[];\r\n  content_mix: string[];\r\n  performance_indicators: string[];\r\n} {\r\n  \r\n  const platformStrategies: Record<string, any> = {\r\n    'instagram': {\r\n      posting_strategy: [\r\n        'Use high-quality visuals as primary hook',\r\n        'Write captions that encourage saves and shares',\r\n        'Include clear call-to-actions in stories',\r\n        'Use relevant hashtags strategically'\r\n      ],\r\n      engagement_timing: [\r\n        'Post when your audience is most active',\r\n        'Respond to comments within first hour',\r\n        'Use stories for real-time engagement',\r\n        'Go live during peak audience times'\r\n      ]\r\n    },\r\n    'linkedin': {\r\n      posting_strategy: [\r\n        'Lead with valuable insights or questions',\r\n        'Use professional but personal tone',\r\n        'Include industry-relevant hashtags',\r\n        'Share thought leadership content'\r\n      ],\r\n      engagement_timing: [\r\n        'Post during business hours for B2B',\r\n        'Engage with comments professionally',\r\n        'Share in relevant LinkedIn groups',\r\n        'Connect with commenters personally'\r\n      ]\r\n    },\r\n    'twitter': {\r\n      posting_strategy: [\r\n        'Use trending hashtags when relevant',\r\n        'Create tweetable quotes and insights',\r\n        'Engage in real-time conversations',\r\n        'Share quick tips and observations'\r\n      ],\r\n      engagement_timing: [\r\n        'Tweet during peak conversation times',\r\n        'Respond quickly to mentions',\r\n        'Join trending conversations',\r\n        'Retweet with thoughtful comments'\r\n      ]\r\n    },\r\n    'facebook': {\r\n      posting_strategy: [\r\n        'Create community-focused content',\r\n        'Use longer-form storytelling',\r\n        'Encourage group discussions',\r\n        'Share local community content'\r\n      ],\r\n      engagement_timing: [\r\n        'Post when your community is online',\r\n        'Respond to all comments personally',\r\n        'Share in relevant Facebook groups',\r\n        'Use Facebook events for promotion'\r\n      ]\r\n    }\r\n  };\r\n\r\n  const strategy = platformStrategies[platform.toLowerCase()] || platformStrategies['instagram'];\r\n  \r\n  return {\r\n    ...strategy,\r\n    content_mix: [\r\n      '60% educational/valuable content',\r\n      '20% behind-the-scenes/personal',\r\n      '15% promotional/business',\r\n      '5% trending/entertainment'\r\n    ],\r\n    performance_indicators: [\r\n      'Comments and meaningful engagement',\r\n      'Saves and shares over likes',\r\n      'Profile visits and follows',\r\n      'Website clicks and conversions',\r\n      'Direct messages and inquiries'\r\n    ]\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAuBM,SAAS,+BACd,YAAoB,EACpB,UAAkB,EAClB,QAAgB;IAGhB,MAAM,kBAAkB,sBAAsB;IAC9C,MAAM,uBAAuB,wBAAwB;IACrD,MAAM,uBAAuB,wBAAwB;IAErD,OAAO;QACL,qBAAqB;eAChB;YACH;YACA;YACA;YACA;SACD;QACD,uBAAuB;eAClB;YACH;YACA;YACA;YACA;YACA;SACD;QACD,yBAAyB;eACpB;YACH;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAKO,SAAS,+BACd,YAAoB,EACpB,QAAgB,EAChB,cAAuB;IAGvB,OAAO;QACL,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoB;YAClB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,uBAAuB;YACrB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAEA;;CAEC,GACD,SAAS,sBAAsB,UAAkB;IAC/C,MAAM,WAAqC;QACzC,YAAY;YACV;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;IACH;IAEA,iDAAiD;IACjD,MAAM,aAAa,WAAW,WAAW;IACzC,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,UAAW;QACrD,IAAI,WAAW,QAAQ,CAAC,MAAM;YAC5B,OAAO;QACT;IACF;IAEA,OAAO,QAAQ,CAAC,WAAW,EAAE,mBAAmB;AAClD;AAEA;;CAEC,GACD,SAAS,wBAAwB,YAAoB;IACnD,MAAM,cAAwC;QAC5C,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,WAAW;YACT;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,iCAAiC;YAC/B;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,OAAO,WAAW,CAAC,aAAa,WAAW,GAAG,IAAI;QAChD;QACA;QACA;QACA;KACD;AACH;AAEA;;CAEC,GACD,SAAS,wBAAwB,QAAgB;IAC/C,MAAM,cAAwC;QAC5C,WAAW;YACT;YACA;YACA;YACA;SACD;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,cAAc,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IAC7D,OAAO,WAAW,CAAC,YAAY,IAAI;QACjC;QACA;QACA;QACA;KACD;AACH;AAKO,SAAS,4BACd,QAAgB,EAChB,YAAoB,EACpB,YAAoB,SAAS;IAQ7B,MAAM,qBAA0C;QAC9C,aAAa;YACX,kBAAkB;gBAChB;gBACA;gBACA;gBACA;aACD;YACD,mBAAmB;gBACjB;gBACA;gBACA;gBACA;aACD;QACH;QACA,YAAY;YACV,kBAAkB;gBAChB;gBACA;gBACA;gBACA;aACD;YACD,mBAAmB;gBACjB;gBACA;gBACA;gBACA;aACD;QACH;QACA,WAAW;YACT,kBAAkB;gBAChB;gBACA;gBACA;gBACA;aACD;YACD,mBAAmB;gBACjB;gBACA;gBACA;gBACA;aACD;QACH;QACA,YAAY;YACV,kBAAkB;gBAChB;gBACA;gBACA;gBACA;aACD;YACD,mBAAmB;gBACjB;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,MAAM,WAAW,kBAAkB,CAAC,SAAS,WAAW,GAAG,IAAI,kBAAkB,CAAC,YAAY;IAE9F,OAAO;QACL,GAAG,QAAQ;QACX,aAAa;YACX;YACA;YACA;YACA;SACD;QACD,wBAAwB;YACtB;YACA;YACA;YACA;YACA;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 4407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/design-analysis.ts"], "sourcesContent": ["/**\r\n * Design Analysis Utilities\r\n * \r\n * Intelligent analysis and processing of design examples for better AI generation\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\n\r\n// Schema for design analysis results\r\nexport const DesignAnalysisSchema = z.object({\r\n  colorPalette: z.object({\r\n    primary: z.string().describe('Primary color in hex format'),\r\n    secondary: z.string().describe('Secondary color in hex format'),\r\n    accent: z.string().describe('Accent color in hex format'),\r\n    colorHarmony: z.enum(['complementary', 'analogous', 'triadic', 'monochromatic', 'split-complementary']).describe('Type of color harmony used'),\r\n    colorMood: z.string().describe('Overall mood conveyed by the color scheme')\r\n  }),\r\n  composition: z.object({\r\n    layout: z.enum(['centered', 'left-aligned', 'right-aligned', 'asymmetrical', 'grid-based']).describe('Primary layout structure'),\r\n    visualHierarchy: z.string().describe('How visual hierarchy is established'),\r\n    focalPoint: z.string().describe('Primary focal point and how it\\'s created'),\r\n    balance: z.enum(['symmetrical', 'asymmetrical', 'radial']).describe('Type of visual balance'),\r\n    whitespace: z.enum(['minimal', 'moderate', 'generous']).describe('Use of negative space')\r\n  }),\r\n  typography: z.object({\r\n    primaryFont: z.string().describe('Primary font style/category'),\r\n    hierarchy: z.string().describe('Typographic hierarchy structure'),\r\n    textTreatment: z.string().describe('Special text treatments or effects'),\r\n    readability: z.enum(['high', 'medium', 'stylized']).describe('Text readability level')\r\n  }),\r\n  style: z.object({\r\n    aesthetic: z.string().describe('Overall design aesthetic (modern, vintage, minimalist, etc.)'),\r\n    mood: z.string().describe('Emotional mood and feeling'),\r\n    sophistication: z.enum(['casual', 'professional', 'luxury', 'playful']).describe('Level of sophistication'),\r\n    trends: z.array(z.string()).describe('Current design trends incorporated')\r\n  }),\r\n  effectiveness: z.object({\r\n    attention: z.number().min(1).max(10).describe('Attention-grabbing potential (1-10)'),\r\n    clarity: z.number().min(1).max(10).describe('Message clarity (1-10)'),\r\n    brandAlignment: z.number().min(1).max(10).describe('Brand alignment strength (1-10)'),\r\n    platformOptimization: z.number().min(1).max(10).describe('Platform optimization (1-10)')\r\n  })\r\n});\r\n\r\nexport type DesignAnalysis = z.infer<typeof DesignAnalysisSchema>;\r\n\r\n// Design analysis prompt\r\nconst designAnalysisPrompt = ai.definePrompt({\r\n  name: 'analyzeDesignExample',\r\n  input: {\r\n    schema: z.object({\r\n      businessType: z.string(),\r\n      platform: z.string().optional(),\r\n      designContext: z.string().optional()\r\n    })\r\n  },\r\n  output: {\r\n    schema: DesignAnalysisSchema\r\n  },\r\n  prompt: `You are an expert design analyst with deep knowledge of visual design principles, color theory, typography, and modern design trends.\r\n\r\nAnalyze the provided design image and extract detailed insights about its design elements and effectiveness.\r\n\r\nBusiness Context: {{businessType}}\r\nPlatform: {{platform}}\r\nContext: {{designContext}}\r\n\r\nProvide a comprehensive analysis covering:\r\n\r\n1. **Color Analysis**: Identify the color palette, harmony type, and psychological impact\r\n2. **Composition Analysis**: Evaluate layout, hierarchy, balance, and use of space\r\n3. **Typography Analysis**: Assess font choices, hierarchy, and text treatment\r\n4. **Style Analysis**: Determine aesthetic approach, mood, and trend incorporation\r\n5. **Effectiveness Analysis**: Rate attention-grabbing power, clarity, brand alignment, and platform optimization\r\n\r\nBe specific and actionable in your analysis. Focus on elements that can be replicated or adapted for new designs.`\r\n});\r\n\r\n/**\r\n * Analyzes a design example to extract key design elements and patterns\r\n */\r\nexport async function analyzeDesignExample(\r\n  designImageUrl: string,\r\n  businessType: string,\r\n  platform?: string,\r\n  context?: string\r\n): Promise<DesignAnalysis> {\r\n  try {\r\n    // For now, return a mock analysis to avoid API issues\r\n    // This can be replaced with actual AI analysis once the prompt system is stable\r\n    return {\r\n      colorPalette: {\r\n        primary: '#FF6B6B',\r\n        secondary: '#4ECDC4',\r\n        accent: '#45B7D1',\r\n        colorHarmony: 'complementary',\r\n        colorMood: 'Energetic and modern'\r\n      },\r\n      composition: {\r\n        layout: 'centered',\r\n        visualHierarchy: 'Clear size-based hierarchy with strong focal point',\r\n        focalPoint: 'Central logo and headline combination',\r\n        balance: 'symmetrical',\r\n        whitespace: 'moderate'\r\n      },\r\n      typography: {\r\n        primaryFont: 'Modern sans-serif',\r\n        hierarchy: 'Large headline, medium subtext, small details',\r\n        textTreatment: 'Bold headlines with subtle shadows',\r\n        readability: 'high'\r\n      },\r\n      style: {\r\n        aesthetic: 'Modern minimalist',\r\n        mood: 'Professional and approachable',\r\n        sophistication: 'professional',\r\n        trends: ['Bold typography', 'Minimalist design', 'High contrast']\r\n      },\r\n      effectiveness: {\r\n        attention: 8,\r\n        clarity: 9,\r\n        brandAlignment: 8,\r\n        platformOptimization: 7\r\n      }\r\n    };\r\n  } catch (error) {\r\n    console.error('Design analysis failed:', error);\r\n    throw new Error('Failed to analyze design example');\r\n  }\r\n}\r\n\r\n/**\r\n * Selects the best design examples based on content type and platform\r\n */\r\nexport function selectOptimalDesignExamples(\r\n  designExamples: string[],\r\n  analyses: DesignAnalysis[],\r\n  contentType: string,\r\n  platform: string,\r\n  maxExamples: number = 3\r\n): string[] {\r\n  if (!analyses.length || !designExamples.length) {\r\n    return designExamples.slice(0, maxExamples);\r\n  }\r\n\r\n  // Score each design based on relevance and effectiveness\r\n  const scoredExamples = designExamples.map((example, index) => {\r\n    const analysis = analyses[index];\r\n    if (!analysis) return { example, score: 0 };\r\n\r\n    let score = 0;\r\n\r\n    // Weight effectiveness metrics\r\n    score += analysis.effectiveness.attention * 0.3;\r\n    score += analysis.effectiveness.clarity * 0.25;\r\n    score += analysis.effectiveness.brandAlignment * 0.25;\r\n    score += analysis.effectiveness.platformOptimization * 0.2;\r\n\r\n    // Bonus for sophisticated designs\r\n    if (analysis.style.sophistication === 'professional' || analysis.style.sophistication === 'luxury') {\r\n      score += 1;\r\n    }\r\n\r\n    // Bonus for modern trends\r\n    score += analysis.style.trends.length * 0.5;\r\n\r\n    return { example, score, analysis };\r\n  });\r\n\r\n  // Sort by score and return top examples\r\n  return scoredExamples\r\n    .sort((a, b) => b.score - a.score)\r\n    .slice(0, maxExamples)\r\n    .map(item => item.example);\r\n}\r\n\r\n/**\r\n * Generates design DNA from analyzed examples\r\n */\r\nexport function extractDesignDNA(analyses: DesignAnalysis[]): string {\r\n  if (!analyses.length) return '';\r\n\r\n  const commonElements = {\r\n    colors: analyses.map(a => a.colorPalette.colorHarmony),\r\n    layouts: analyses.map(a => a.composition.layout),\r\n    aesthetics: analyses.map(a => a.style.aesthetic),\r\n    moods: analyses.map(a => a.style.mood)\r\n  };\r\n\r\n  // Find most common elements\r\n  const mostCommonColor = getMostCommon(commonElements.colors);\r\n  const mostCommonLayout = getMostCommon(commonElements.layouts);\r\n  const mostCommonAesthetic = getMostCommon(commonElements.aesthetics);\r\n  const mostCommonMood = getMostCommon(commonElements.moods);\r\n\r\n  return `\r\n**EXTRACTED DESIGN DNA:**\r\n- **Color Harmony**: Primarily uses ${mostCommonColor} color schemes\r\n- **Layout Pattern**: Favors ${mostCommonLayout} compositions\r\n- **Aesthetic Style**: Consistent ${mostCommonAesthetic} approach\r\n- **Emotional Tone**: Maintains ${mostCommonMood} mood throughout\r\n- **Visual Sophistication**: ${analyses[0]?.style.sophistication} level presentation\r\n- **Typography Approach**: ${analyses[0]?.typography.hierarchy} hierarchy structure\r\n`;\r\n}\r\n\r\n/**\r\n * Helper function to find most common element in array\r\n */\r\nfunction getMostCommon<T>(arr: T[]): T {\r\n  const counts = arr.reduce((acc, item) => {\r\n    acc[item as string] = (acc[item as string] || 0) + 1;\r\n    return acc;\r\n  }, {} as Record<string, number>);\r\n\r\n  return Object.entries(counts).reduce((a, b) => counts[a[0]] > counts[b[0]] ? a : b)[0] as T;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;AAED;AACA;;;AAGO,MAAM,uBAAuB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC/B,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC5B,cAAc,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAiB;YAAa;YAAW;YAAiB;SAAsB,EAAE,QAAQ,CAAC;QACjH,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC;IACA,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,QAAQ,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAY;YAAgB;YAAiB;YAAgB;SAAa,EAAE,QAAQ,CAAC;QACrG,iBAAiB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACrC,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAChC,SAAS,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAe;YAAgB;SAAS,EAAE,QAAQ,CAAC;QACpE,YAAY,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAW;YAAY;SAAW,EAAE,QAAQ,CAAC;IACnE;IACA,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACjC,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC/B,eAAe,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACnC,aAAa,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;SAAW,EAAE,QAAQ,CAAC;IAC/D;IACA,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACd,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC/B,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC1B,gBAAgB,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAU;YAAgB;YAAU;SAAU,EAAE,QAAQ,CAAC;QACjF,QAAQ,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IACvC;IACA,eAAe,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACtB,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC9C,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC5C,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QACnD,sBAAsB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;IAC3D;AACF;AAIA,yBAAyB;AACzB,MAAM,uBAAuB,qHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC3C,MAAM;IACN,OAAO;QACL,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM;YACtB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,eAAe,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACpC;IACF;IACA,QAAQ;QACN,QAAQ;IACV;IACA,QAAQ,CAAC;;;;;;;;;;;;;;;;iHAgBsG,CAAC;AAClH;AAKO,eAAe,qBACpB,cAAsB,EACtB,YAAoB,EACpB,QAAiB,EACjB,OAAgB;IAEhB,IAAI;QACF,sDAAsD;QACtD,gFAAgF;QAChF,OAAO;YACL,cAAc;gBACZ,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,cAAc;gBACd,WAAW;YACb;YACA,aAAa;gBACX,QAAQ;gBACR,iBAAiB;gBACjB,YAAY;gBACZ,SAAS;gBACT,YAAY;YACd;YACA,YAAY;gBACV,aAAa;gBACb,WAAW;gBACX,eAAe;gBACf,aAAa;YACf;YACA,OAAO;gBACL,WAAW;gBACX,MAAM;gBACN,gBAAgB;gBAChB,QAAQ;oBAAC;oBAAmB;oBAAqB;iBAAgB;YACnE;YACA,eAAe;gBACb,WAAW;gBACX,SAAS;gBACT,gBAAgB;gBAChB,sBAAsB;YACxB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,SAAS,4BACd,cAAwB,EACxB,QAA0B,EAC1B,WAAmB,EACnB,QAAgB,EAChB,cAAsB,CAAC;IAEvB,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,eAAe,MAAM,EAAE;QAC9C,OAAO,eAAe,KAAK,CAAC,GAAG;IACjC;IAEA,yDAAyD;IACzD,MAAM,iBAAiB,eAAe,GAAG,CAAC,CAAC,SAAS;QAClD,MAAM,WAAW,QAAQ,CAAC,MAAM;QAChC,IAAI,CAAC,UAAU,OAAO;YAAE;YAAS,OAAO;QAAE;QAE1C,IAAI,QAAQ;QAEZ,+BAA+B;QAC/B,SAAS,SAAS,aAAa,CAAC,SAAS,GAAG;QAC5C,SAAS,SAAS,aAAa,CAAC,OAAO,GAAG;QAC1C,SAAS,SAAS,aAAa,CAAC,cAAc,GAAG;QACjD,SAAS,SAAS,aAAa,CAAC,oBAAoB,GAAG;QAEvD,kCAAkC;QAClC,IAAI,SAAS,KAAK,CAAC,cAAc,KAAK,kBAAkB,SAAS,KAAK,CAAC,cAAc,KAAK,UAAU;YAClG,SAAS;QACX;QAEA,0BAA0B;QAC1B,SAAS,SAAS,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG;QAExC,OAAO;YAAE;YAAS;YAAO;QAAS;IACpC;IAEA,wCAAwC;IACxC,OAAO,eACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,aACT,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO;AAC7B;AAKO,SAAS,iBAAiB,QAA0B;IACzD,IAAI,CAAC,SAAS,MAAM,EAAE,OAAO;IAE7B,MAAM,iBAAiB;QACrB,QAAQ,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,YAAY,CAAC,YAAY;QACrD,SAAS,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW,CAAC,MAAM;QAC/C,YAAY,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,SAAS;QAC/C,OAAO,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,IAAI;IACvC;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB,cAAc,eAAe,MAAM;IAC3D,MAAM,mBAAmB,cAAc,eAAe,OAAO;IAC7D,MAAM,sBAAsB,cAAc,eAAe,UAAU;IACnE,MAAM,iBAAiB,cAAc,eAAe,KAAK;IAEzD,OAAO,CAAC;;oCAE0B,EAAE,gBAAgB;6BACzB,EAAE,iBAAiB;kCACd,EAAE,oBAAoB;gCACxB,EAAE,eAAe;6BACpB,EAAE,QAAQ,CAAC,EAAE,EAAE,MAAM,eAAe;2BACtC,EAAE,QAAQ,CAAC,EAAE,EAAE,WAAW,UAAU;AAC/D,CAAC;AACD;AAEA;;CAEC,GACD,SAAS,cAAiB,GAAQ;IAChC,MAAM,SAAS,IAAI,MAAM,CAAC,CAAC,KAAK;QAC9B,GAAG,CAAC,KAAe,GAAG,CAAC,GAAG,CAAC,KAAe,IAAI,CAAC,IAAI;QACnD,OAAO;IACT,GAAG,CAAC;IAEJ,OAAO,OAAO,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE;AACxF", "debugId": null}}, {"offset": {"line": 4632, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/design-quality.ts"], "sourcesContent": ["/**\r\n * Design Quality Validation and Enhancement\r\n * \r\n * System for validating, scoring, and iteratively improving generated designs\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\n\r\n// Schema for design quality assessment\r\nexport const DesignQualitySchema = z.object({\r\n  overall: z.object({\r\n    score: z.number().min(1).max(10).describe('Overall design quality score (1-10)'),\r\n    grade: z.enum(['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'F']).describe('Letter grade for design quality'),\r\n    summary: z.string().describe('Brief summary of design strengths and weaknesses')\r\n  }),\r\n  composition: z.object({\r\n    score: z.number().min(1).max(10).describe('Composition and layout quality (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on composition'),\r\n    improvements: z.array(z.string()).describe('Suggested composition improvements')\r\n  }),\r\n  typography: z.object({\r\n    score: z.number().min(1).max(10).describe('Typography quality and readability (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on typography'),\r\n    improvements: z.array(z.string()).describe('Suggested typography improvements')\r\n  }),\r\n  colorDesign: z.object({\r\n    score: z.number().min(1).max(10).describe('Color usage and harmony (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on color choices'),\r\n    improvements: z.array(z.string()).describe('Suggested color improvements')\r\n  }),\r\n  brandAlignment: z.object({\r\n    score: z.number().min(1).max(10).describe('Brand consistency and alignment (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on brand alignment'),\r\n    improvements: z.array(z.string()).describe('Suggested brand alignment improvements')\r\n  }),\r\n  platformOptimization: z.object({\r\n    score: z.number().min(1).max(10).describe('Platform-specific optimization (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on platform optimization'),\r\n    improvements: z.array(z.string()).describe('Suggested platform optimization improvements')\r\n  }),\r\n  technicalQuality: z.object({\r\n    score: z.number().min(1).max(10).describe('Technical execution quality (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on technical aspects'),\r\n    improvements: z.array(z.string()).describe('Suggested technical improvements')\r\n  }),\r\n  recommendedActions: z.array(z.object({\r\n    priority: z.enum(['high', 'medium', 'low']).describe('Priority level of the action'),\r\n    action: z.string().describe('Specific action to take'),\r\n    expectedImpact: z.string().describe('Expected impact of the action')\r\n  })).describe('Prioritized list of recommended improvements')\r\n});\r\n\r\nexport type DesignQuality = z.infer<typeof DesignQualitySchema>;\r\n\r\n// Design quality assessment prompt\r\nconst designQualityPrompt = ai.definePrompt({\r\n  name: 'assessDesignQuality',\r\n  input: {\r\n    schema: z.object({\r\n      businessType: z.string(),\r\n      platform: z.string(),\r\n      visualStyle: z.string(),\r\n      brandColors: z.string().optional(),\r\n      designGoals: z.string().optional()\r\n    })\r\n  },\r\n  output: {\r\n    schema: DesignQualitySchema\r\n  },\r\n  prompt: `You are a world-class design critic and quality assessor with expertise in visual design, branding, and social media optimization.\r\n\r\nEvaluate the provided design image with the highest professional standards.\r\n\r\n**Context:**\r\n- Business Type: {{businessType}}\r\n- Platform: {{platform}}\r\n- Visual Style Goal: {{visualStyle}}\r\n- Brand Colors: {{brandColors}}\r\n- Design Goals: {{designGoals}}\r\n\r\n**Assessment Criteria:**\r\n\r\n1. **Composition & Layout** (25%):\r\n   - Visual hierarchy and flow\r\n   - Balance and proportion\r\n   - Use of negative space\r\n   - Rule of thirds application\r\n   - Focal point effectiveness\r\n\r\n2. **Typography** (20%):\r\n   - Readability and legibility\r\n   - Hierarchy and contrast\r\n   - Font choice appropriateness\r\n   - Text positioning and spacing\r\n   - Accessibility compliance\r\n\r\n3. **Color Design** (20%):\r\n   - Color harmony and theory\r\n   - Brand color integration\r\n   - Contrast and accessibility\r\n   - Psychological impact\r\n   - Platform appropriateness\r\n\r\n4. **Brand Alignment** (15%):\r\n   - Brand consistency\r\n   - Logo integration\r\n   - Visual style adherence\r\n   - Brand personality expression\r\n   - Professional presentation\r\n\r\n5. **Platform Optimization** (10%):\r\n   - Platform-specific best practices\r\n   - Mobile optimization\r\n   - Engagement potential\r\n   - Algorithm friendliness\r\n   - Format appropriateness\r\n\r\n6. **Technical Quality** (10%):\r\n   - Image resolution and clarity\r\n   - Professional finish\r\n   - Technical execution\r\n   - Scalability\r\n   - Print/digital readiness\r\n\r\nProvide specific, actionable feedback with concrete improvement suggestions. Be thorough but constructive.`\r\n});\r\n\r\n/**\r\n * Assesses the quality of a generated design\r\n */\r\nexport async function assessDesignQuality(\r\n  designImageUrl: string,\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  brandColors?: string,\r\n  designGoals?: string\r\n): Promise<DesignQuality> {\r\n  try {\r\n    // For now, return a mock quality assessment to avoid API issues\r\n    // This provides realistic quality scores while the system is being tested\r\n    const baseScore = 7 + Math.random() * 2; // Random score between 7-9\r\n\r\n    return {\r\n      overall: {\r\n        score: Math.round(baseScore * 10) / 10,\r\n        grade: baseScore >= 8.5 ? 'A' : baseScore >= 7.5 ? 'B+' : 'B',\r\n        summary: `Professional ${visualStyle} design for ${businessType} with good composition and brand alignment.`\r\n      },\r\n      composition: {\r\n        score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,\r\n        feedback: 'Strong visual hierarchy with balanced composition',\r\n        improvements: baseScore < 8 ? ['Improve focal point clarity', 'Enhance visual balance'] : []\r\n      },\r\n      typography: {\r\n        score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,\r\n        feedback: 'Clear, readable typography with appropriate hierarchy',\r\n        improvements: baseScore < 8 ? ['Increase text contrast', 'Improve font pairing'] : []\r\n      },\r\n      colorDesign: {\r\n        score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,\r\n        feedback: brandColors ? 'Good brand color integration' : 'Appropriate color choices for business type',\r\n        improvements: baseScore < 8 ? ['Enhance color harmony', 'Improve contrast ratios'] : []\r\n      },\r\n      brandAlignment: {\r\n        score: brandColors ? Math.round((baseScore + 0.5) * 10) / 10 : Math.round((baseScore - 0.5) * 10) / 10,\r\n        feedback: brandColors ? 'Strong brand consistency maintained' : 'Generic design approach',\r\n        improvements: !brandColors ? ['Integrate brand elements', 'Improve brand consistency'] : []\r\n      },\r\n      platformOptimization: {\r\n        score: Math.round((baseScore + Math.random() * 0.3) * 10) / 10,\r\n        feedback: `Well optimized for ${platform} format and audience`,\r\n        improvements: baseScore < 8 ? ['Optimize for mobile viewing', 'Improve platform-specific elements'] : []\r\n      },\r\n      technicalQuality: {\r\n        score: Math.round((baseScore + 0.2) * 10) / 10,\r\n        feedback: 'High resolution with professional finish',\r\n        improvements: baseScore < 8 ? ['Improve image resolution', 'Enhance visual polish'] : []\r\n      },\r\n      recommendedActions: [\r\n        {\r\n          priority: baseScore < 7.5 ? 'high' : 'medium',\r\n          action: 'Enhance visual impact through stronger focal points',\r\n          expectedImpact: 'Improved attention and engagement'\r\n        },\r\n        {\r\n          priority: 'medium',\r\n          action: 'Optimize typography for better readability',\r\n          expectedImpact: 'Clearer message communication'\r\n        }\r\n      ].filter(action => baseScore < 8.5 || action.priority === 'medium')\r\n    };\r\n  } catch (error) {\r\n    console.error('Design quality assessment failed:', error);\r\n    throw new Error('Failed to assess design quality');\r\n  }\r\n}\r\n\r\n/**\r\n * Generates improvement suggestions based on quality assessment\r\n */\r\nexport function generateImprovementPrompt(quality: DesignQuality): string {\r\n  const highPriorityActions = quality.recommendedActions\r\n    .filter(action => action.priority === 'high')\r\n    .map(action => action.action);\r\n\r\n  const mediumPriorityActions = quality.recommendedActions\r\n    .filter(action => action.priority === 'medium')\r\n    .map(action => action.action);\r\n\r\n  let improvementPrompt = `\r\n**DESIGN IMPROVEMENT INSTRUCTIONS:**\r\n\r\nBased on professional design assessment (Overall Score: ${quality.overall.score}/10, Grade: ${quality.overall.grade}):\r\n\r\n**CRITICAL IMPROVEMENTS (High Priority):**\r\n${highPriorityActions.map(action => `- ${action}`).join('\\n')}\r\n\r\n**RECOMMENDED ENHANCEMENTS (Medium Priority):**\r\n${mediumPriorityActions.map(action => `- ${action}`).join('\\n')}\r\n\r\n**SPECIFIC AREA FEEDBACK:**\r\n`;\r\n\r\n  if (quality.composition.score < 7) {\r\n    improvementPrompt += `\r\n**Composition Issues to Address:**\r\n${quality.composition.improvements.map(imp => `- ${imp}`).join('\\n')}\r\n`;\r\n  }\r\n\r\n  if (quality.typography.score < 7) {\r\n    improvementPrompt += `\r\n**Typography Issues to Address:**\r\n${quality.typography.improvements.map(imp => `- ${imp}`).join('\\n')}\r\n`;\r\n  }\r\n\r\n  if (quality.colorDesign.score < 7) {\r\n    improvementPrompt += `\r\n**Color Design Issues to Address:**\r\n${quality.colorDesign.improvements.map(imp => `- ${imp}`).join('\\n')}\r\n`;\r\n  }\r\n\r\n  if (quality.brandAlignment.score < 7) {\r\n    improvementPrompt += `\r\n**Brand Alignment Issues to Address:**\r\n${quality.brandAlignment.improvements.map(imp => `- ${imp}`).join('\\n')}\r\n`;\r\n  }\r\n\r\n  return improvementPrompt;\r\n}\r\n\r\n/**\r\n * Determines if a design meets quality standards\r\n */\r\nexport function meetsQualityStandards(quality: DesignQuality, minimumScore: number = 7): boolean {\r\n  return quality.overall.score >= minimumScore &&\r\n    quality.composition.score >= minimumScore - 1 &&\r\n    quality.typography.score >= minimumScore - 1 &&\r\n    quality.brandAlignment.score >= minimumScore - 1;\r\n}\r\n\r\n/**\r\n * Calculates weighted quality score\r\n */\r\nexport function calculateWeightedScore(quality: DesignQuality): number {\r\n  const weights = {\r\n    composition: 0.25,\r\n    typography: 0.20,\r\n    colorDesign: 0.20,\r\n    brandAlignment: 0.15,\r\n    platformOptimization: 0.10,\r\n    technicalQuality: 0.10\r\n  };\r\n\r\n  return (\r\n    quality.composition.score * weights.composition +\r\n    quality.typography.score * weights.typography +\r\n    quality.colorDesign.score * weights.colorDesign +\r\n    quality.brandAlignment.score * weights.brandAlignment +\r\n    quality.platformOptimization.score * weights.platformOptimization +\r\n    quality.technicalQuality.score * weights.technicalQuality\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;AAED;AACA;;;AAGO,MAAM,sBAAsB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,OAAO,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAM;YAAK;YAAM;YAAK;YAAM;YAAK;YAAK;SAAI,EAAE,QAAQ,CAAC;QACpE,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B;IACA,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACvB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,sBAAsB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC7B,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,kBAAkB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,oBAAoB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnC,UAAU,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;SAAM,EAAE,QAAQ,CAAC;QACrD,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC5B,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACtC,IAAI,QAAQ,CAAC;AACf;AAIA,mCAAmC;AACnC,MAAM,sBAAsB,qHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC1C,MAAM;IACN,OAAO;QACL,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM;YACtB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAChC,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAClC;IACF;IACA,QAAQ;QACN,QAAQ;IACV;IACA,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0GAuD+F,CAAC;AAC3G;AAKO,eAAe,oBACpB,cAAsB,EACtB,YAAoB,EACpB,QAAgB,EAChB,WAAmB,EACnB,WAAoB,EACpB,WAAoB;IAEpB,IAAI;QACF,gEAAgE;QAChE,0EAA0E;QAC1E,MAAM,YAAY,IAAI,KAAK,MAAM,KAAK,GAAG,2BAA2B;QAEpE,OAAO;YACL,SAAS;gBACP,OAAO,KAAK,KAAK,CAAC,YAAY,MAAM;gBACpC,OAAO,aAAa,MAAM,MAAM,aAAa,MAAM,OAAO;gBAC1D,SAAS,CAAC,aAAa,EAAE,YAAY,YAAY,EAAE,aAAa,2CAA2C,CAAC;YAC9G;YACA,aAAa;gBACX,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM;gBAC5D,UAAU;gBACV,cAAc,YAAY,IAAI;oBAAC;oBAA+B;iBAAyB,GAAG,EAAE;YAC9F;YACA,YAAY;gBACV,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM;gBAC5D,UAAU;gBACV,cAAc,YAAY,IAAI;oBAAC;oBAA0B;iBAAuB,GAAG,EAAE;YACvF;YACA,aAAa;gBACX,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM;gBAC5D,UAAU,cAAc,iCAAiC;gBACzD,cAAc,YAAY,IAAI;oBAAC;oBAAyB;iBAA0B,GAAG,EAAE;YACzF;YACA,gBAAgB;gBACd,OAAO,cAAc,KAAK,KAAK,CAAC,CAAC,YAAY,GAAG,IAAI,MAAM,KAAK,KAAK,KAAK,CAAC,CAAC,YAAY,GAAG,IAAI,MAAM;gBACpG,UAAU,cAAc,wCAAwC;gBAChE,cAAc,CAAC,cAAc;oBAAC;oBAA4B;iBAA4B,GAAG,EAAE;YAC7F;YACA,sBAAsB;gBACpB,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM;gBAC5D,UAAU,CAAC,mBAAmB,EAAE,SAAS,oBAAoB,CAAC;gBAC9D,cAAc,YAAY,IAAI;oBAAC;oBAA+B;iBAAqC,GAAG,EAAE;YAC1G;YACA,kBAAkB;gBAChB,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,GAAG,IAAI,MAAM;gBAC5C,UAAU;gBACV,cAAc,YAAY,IAAI;oBAAC;oBAA4B;iBAAwB,GAAG,EAAE;YAC1F;YACA,oBAAoB;gBAClB;oBACE,UAAU,YAAY,MAAM,SAAS;oBACrC,QAAQ;oBACR,gBAAgB;gBAClB;gBACA;oBACE,UAAU;oBACV,QAAQ;oBACR,gBAAgB;gBAClB;aACD,CAAC,MAAM,CAAC,CAAA,SAAU,YAAY,OAAO,OAAO,QAAQ,KAAK;QAC5D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,SAAS,0BAA0B,OAAsB;IAC9D,MAAM,sBAAsB,QAAQ,kBAAkB,CACnD,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK,QACrC,GAAG,CAAC,CAAA,SAAU,OAAO,MAAM;IAE9B,MAAM,wBAAwB,QAAQ,kBAAkB,CACrD,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK,UACrC,GAAG,CAAC,CAAA,SAAU,OAAO,MAAM;IAE9B,IAAI,oBAAoB,CAAC;;;wDAG6B,EAAE,QAAQ,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,QAAQ,OAAO,CAAC,KAAK,CAAC;;;AAGpH,EAAE,oBAAoB,GAAG,CAAC,CAAA,SAAU,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM;;;AAG9D,EAAE,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM;;;AAGhE,CAAC;IAEC,IAAI,QAAQ,WAAW,CAAC,KAAK,GAAG,GAAG;QACjC,qBAAqB,CAAC;;AAE1B,EAAE,QAAQ,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACrE,CAAC;IACC;IAEA,IAAI,QAAQ,UAAU,CAAC,KAAK,GAAG,GAAG;QAChC,qBAAqB,CAAC;;AAE1B,EAAE,QAAQ,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACpE,CAAC;IACC;IAEA,IAAI,QAAQ,WAAW,CAAC,KAAK,GAAG,GAAG;QACjC,qBAAqB,CAAC;;AAE1B,EAAE,QAAQ,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACrE,CAAC;IACC;IAEA,IAAI,QAAQ,cAAc,CAAC,KAAK,GAAG,GAAG;QACpC,qBAAqB,CAAC;;AAE1B,EAAE,QAAQ,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACxE,CAAC;IACC;IAEA,OAAO;AACT;AAKO,SAAS,sBAAsB,OAAsB,EAAE,eAAuB,CAAC;IACpF,OAAO,QAAQ,OAAO,CAAC,KAAK,IAAI,gBAC9B,QAAQ,WAAW,CAAC,KAAK,IAAI,eAAe,KAC5C,QAAQ,UAAU,CAAC,KAAK,IAAI,eAAe,KAC3C,QAAQ,cAAc,CAAC,KAAK,IAAI,eAAe;AACnD;AAKO,SAAS,uBAAuB,OAAsB;IAC3D,MAAM,UAAU;QACd,aAAa;QACb,YAAY;QACZ,aAAa;QACb,gBAAgB;QAChB,sBAAsB;QACtB,kBAAkB;IACpB;IAEA,OACE,QAAQ,WAAW,CAAC,KAAK,GAAG,QAAQ,WAAW,GAC/C,QAAQ,UAAU,CAAC,KAAK,GAAG,QAAQ,UAAU,GAC7C,QAAQ,WAAW,CAAC,KAAK,GAAG,QAAQ,WAAW,GAC/C,QAAQ,cAAc,CAAC,KAAK,GAAG,QAAQ,cAAc,GACrD,QAAQ,oBAAoB,CAAC,KAAK,GAAG,QAAQ,oBAAoB,GACjE,QAAQ,gBAAgB,CAAC,KAAK,GAAG,QAAQ,gBAAgB;AAE7D", "debugId": null}}, {"offset": {"line": 4913, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/flows/generate-post-from-profile.ts"], "sourcesContent": ["\r\n'use server';\r\n\r\n/**\r\n * @fileOverview This file defines a Genkit flow for generating a daily social media post.\r\n *\r\n * It takes into account business type, location, brand voice, current weather, and local events to create engaging content.\r\n * @exports generatePostFromProfile - The main function to generate a post.\r\n * @exports GeneratePostFromProfileInput - The input type for the generation flow.\r\n * @exports GeneratePostFromProfileOutput - The output type for the generation flow.\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { GenerateRequest } from 'genkit/generate';\r\nimport { z } from 'zod';\r\nimport { getWeatherTool, getEventsTool } from '@/ai/tools/local-data';\r\nimport { getEnhancedEventsTool, getEnhancedWeatherTool } from '@/ai/tools/enhanced-local-data';\r\nimport { ENHANCED_CAPTION_PROMPT, PLATFORM_SPECIFIC_OPTIMIZATIONS } from '@/ai/prompts/enhanced-caption-prompt';\r\nimport { ADVANCED_AI_PROMPT } from '@/ai/prompts/advanced-ai-prompt';\r\nimport { generateHashtagStrategy, analyzeHashtags } from '@/ai/utils/hashtag-strategy';\r\nimport { generateMarketIntelligence, generateRealTimeTrendingTopics } from '@/ai/utils/trending-topics';\r\nimport { fetchLocalContext } from '@/ai/utils/real-time-trends-integration';\r\nimport { selectRelevantContext, filterContextData } from '@/ai/utils/intelligent-context-selector';\r\nimport { generateHumanizationTechniques, generateTrafficDrivingElements } from '@/ai/utils/human-content-generator';\r\nimport {\r\n  ADVANCED_DESIGN_PRINCIPLES,\r\n  PLATFORM_SPECIFIC_GUIDELINES,\r\n  BUSINESS_TYPE_DESIGN_DNA,\r\n  QUALITY_ENHANCEMENT_INSTRUCTIONS\r\n} from '@/ai/prompts/advanced-design-prompts';\r\nimport {\r\n  analyzeDesignExample,\r\n  selectOptimalDesignExamples,\r\n  extractDesignDNA,\r\n  type DesignAnalysis\r\n} from '@/ai/utils/design-analysis';\r\nimport {\r\n  assessDesignQuality,\r\n  generateImprovementPrompt,\r\n  meetsQualityStandards,\r\n  type DesignQuality\r\n} from '@/ai/utils/design-quality';\r\nimport {\r\n  getCachedDesignTrends,\r\n  generateTrendInstructions,\r\n  type DesignTrends\r\n} from '@/ai/utils/design-trends';\r\nimport {\r\n  recordDesignGeneration,\r\n  generatePerformanceOptimizedInstructions\r\n} from '@/ai/utils/design-analytics';\r\n\r\nconst GeneratePostFromProfileInputSchema = z.object({\r\n  businessType: z.string().describe('The type of business (e.g., restaurant, salon).'),\r\n  location: z.string().describe('The location of the business (city, state).'),\r\n  visualStyle: z.string().describe('The visual style of the brand (e.g., modern, vintage).'),\r\n  writingTone: z.string().describe('The brand voice of the business.'),\r\n  contentThemes: z.string().describe('The content themes of the business.'),\r\n  logoDataUrl: z.string().describe(\"The business logo as a data URI that must include a MIME type and use Base64 encoding. Expected format: 'data:<mimetype>;base64,<encoded_data>'.\"),\r\n  designExamples: z.array(z.string()).optional().describe(\"Array of design example data URIs to use as style reference for generating similar designs.\"),\r\n  dayOfWeek: z.string().describe('The day of the week for the post.'),\r\n  currentDate: z.string().describe('The current date for the post.'),\r\n  variants: z.array(z.object({\r\n    platform: z.string(),\r\n    aspectRatio: z.string(),\r\n  })).describe('An array of platform and aspect ratio variants to generate.'),\r\n  primaryColor: z.string().optional().describe('The primary brand color in HSL format.'),\r\n  accentColor: z.string().optional().describe('The accent brand color in HSL format.'),\r\n  backgroundColor: z.string().optional().describe('The background brand color in HSL format.'),\r\n\r\n  // New detailed fields for richer content\r\n  services: z.string().optional().describe('A newline-separated list of key services or products.'),\r\n  targetAudience: z.string().optional().describe('A description of the target audience.'),\r\n  keyFeatures: z.string().optional().describe('A newline-separated list of key features or selling points.'),\r\n  competitiveAdvantages: z.string().optional().describe('A newline-separated list of competitive advantages.'),\r\n\r\n  // Brand consistency preferences\r\n  brandConsistency: z.object({\r\n    strictConsistency: z.boolean().optional(),\r\n    followBrandColors: z.boolean().optional(),\r\n  }).optional().describe('Brand consistency preferences for content generation.'),\r\n\r\n  // Enhanced brand context\r\n  websiteUrl: z.string().optional().describe('The business website URL for additional context.'),\r\n  description: z.string().optional().describe('Detailed business description for better content context.'),\r\n  contactInfo: z.object({\r\n    phone: z.string().optional(),\r\n    email: z.string().optional(),\r\n    address: z.string().optional(),\r\n  }).optional().describe('Contact information for business context.'),\r\n  socialMedia: z.object({\r\n    facebook: z.string().optional(),\r\n    instagram: z.string().optional(),\r\n    twitter: z.string().optional(),\r\n    linkedin: z.string().optional(),\r\n  }).optional().describe('Social media handles for cross-platform consistency.'),\r\n\r\n  // Language preferences\r\n  useLocalLanguage: z.boolean().optional().describe('Whether to use local language in content generation (default: false).'),\r\n});\r\n\r\nexport type GeneratePostFromProfileInput = z.infer<typeof GeneratePostFromProfileInputSchema>;\r\n\r\nconst GeneratePostFromProfileOutputSchema = z.object({\r\n  content: z.string().describe('The primary generated social media post content (the caption).'),\r\n  catchyWords: z.string().describe('Catchy words for the image (max 5 words). Must be directly related to the specific business services/products, not generic phrases. Required for ALL posts.'),\r\n  subheadline: z.string().nullable().optional().describe('Optional subheadline (max 14 words). Add only when it would make the post more effective based on marketing strategy.'),\r\n  callToAction: z.string().nullable().optional().describe('Optional call to action. Add only when it would drive better engagement or conversions based on marketing strategy.'),\r\n  hashtags: z.string().describe('Strategically selected hashtags for the post.'),\r\n  contentVariants: z.array(z.object({\r\n    content: z.string().describe('Alternative caption variant.'),\r\n    approach: z.string().describe('The copywriting approach used (e.g., AIDA, PAS, Storytelling).'),\r\n    rationale: z.string().describe('Why this variant might perform well.')\r\n  })).optional().describe('Alternative caption variants for A/B testing.'),\r\n  hashtagAnalysis: z.object({\r\n    trending: z.array(z.string()).describe('Trending hashtags for reach.'),\r\n    niche: z.array(z.string()).describe('Industry-specific hashtags.'),\r\n    location: z.array(z.string()).describe('Location-based hashtags.'),\r\n    community: z.array(z.string()).describe('Community engagement hashtags.')\r\n  }).optional().describe('Categorized hashtag strategy.'),\r\n  marketIntelligence: z.object({\r\n    trending_topics: z.array(z.object({\r\n      topic: z.string(),\r\n      relevanceScore: z.number(),\r\n      category: z.string(),\r\n      engagement_potential: z.string()\r\n    })).describe('Current trending topics relevant to the business.'),\r\n    competitor_insights: z.array(z.object({\r\n      competitor_name: z.string(),\r\n      content_gap: z.string(),\r\n      differentiation_opportunity: z.string()\r\n    })).describe('Competitor analysis and differentiation opportunities.'),\r\n    cultural_context: z.object({\r\n      location: z.string(),\r\n      cultural_nuances: z.array(z.string()),\r\n      local_customs: z.array(z.string())\r\n    }).describe('Cultural and location-specific context.'),\r\n    viral_patterns: z.array(z.string()).describe('Content patterns that drive viral engagement.'),\r\n    engagement_triggers: z.array(z.string()).describe('Psychological triggers for maximum engagement.')\r\n  }).optional().describe('Advanced market intelligence and optimization data.'),\r\n  localContext: z.object({\r\n    weather: z.object({\r\n      temperature: z.number(),\r\n      condition: z.string(),\r\n      business_impact: z.string(),\r\n      content_opportunities: z.array(z.string())\r\n    }).optional().describe('Current weather context and business opportunities.'),\r\n    events: z.array(z.object({\r\n      name: z.string(),\r\n      category: z.string(),\r\n      relevance_score: z.number(),\r\n      start_date: z.string()\r\n    })).optional().describe('Relevant local events for content integration.')\r\n  }).optional().describe('Local context including weather and events.'),\r\n  variants: z.array(z.object({\r\n    platform: z.string(),\r\n    imageUrl: z.string(),\r\n  })),\r\n});\r\n\r\nexport type GeneratePostFromProfileOutput = z.infer<typeof GeneratePostFromProfileOutputSchema>;\r\n\r\nexport async function generatePostFromProfile(input: GeneratePostFromProfileInput): Promise<GeneratePostFromProfileOutput> {\r\n  return generatePostFromProfileFlow(input);\r\n}\r\n\r\n\r\n/**\r\n * Combines catchy words, subheadline, and call to action into a single text for image overlay\r\n */\r\nfunction combineTextComponents(catchyWords: string, subheadline?: string, callToAction?: string): string {\r\n  const components = [catchyWords];\r\n\r\n  if (subheadline && subheadline.trim()) {\r\n    components.push(subheadline.trim());\r\n  }\r\n\r\n  if (callToAction && callToAction.trim()) {\r\n    components.push(callToAction.trim());\r\n  }\r\n\r\n  return components.join('\\n');\r\n}\r\n\r\n// Define the enhanced text generation prompt\r\nconst enhancedTextGenPrompt = ai.definePrompt({\r\n  name: 'enhancedGeneratePostTextPrompt',\r\n  input: {\r\n    schema: z.object({\r\n      businessType: z.string(),\r\n      location: z.string(),\r\n      writingTone: z.string(),\r\n      contentThemes: z.string(),\r\n      dayOfWeek: z.string(),\r\n      currentDate: z.string(),\r\n      platform: z.string().optional(),\r\n      services: z.string().optional(),\r\n      targetAudience: z.string().optional(),\r\n      keyFeatures: z.string().optional(),\r\n      competitiveAdvantages: z.string().optional(),\r\n      contentVariation: z.string().optional(),\r\n      contextInstructions: z.string().optional(),\r\n      selectedWeather: z.any().optional(),\r\n      selectedEvents: z.any().optional(),\r\n      selectedTrends: z.any().optional(),\r\n      selectedCultural: z.any().optional(),\r\n      useLocalLanguage: z.boolean().optional(),\r\n    })\r\n  },\r\n  output: {\r\n    schema: z.object({\r\n      content: z.string().describe('The primary generated social media post content (the caption).'),\r\n      catchyWords: z.string().describe('Catchy words for the image (max 5 words). Must be directly related to the specific business services/products, not generic phrases. Required for ALL posts.'),\r\n      subheadline: z.string().nullable().optional().describe('Optional subheadline (max 14 words). Add only when it would make the post more effective based on marketing strategy.'),\r\n      callToAction: z.string().nullable().optional().describe('Optional call to action. Add only when it would drive better engagement or conversions based on marketing strategy.'),\r\n      hashtags: z.string().describe('Strategically selected hashtags for the post.'),\r\n      contentVariants: z.array(z.object({\r\n        content: z.string().describe('Alternative caption variant.'),\r\n        approach: z.string().describe('The copywriting approach used (e.g., AIDA, PAS, Storytelling).'),\r\n        rationale: z.string().describe('Why this variant might perform well.')\r\n      })).describe('2-3 alternative caption variants for A/B testing.'),\r\n    })\r\n  },\r\n  tools: [getWeatherTool, getEventsTool, getEnhancedWeatherTool, getEnhancedEventsTool],\r\n  prompt: ADVANCED_AI_PROMPT,\r\n});\r\n\r\n/**\r\n * Wraps ai.generate with retry logic for 503 errors.\r\n */\r\nasync function generateWithRetry(request: GenerateRequest, retries = 3, delay = 1000) {\r\n  for (let i = 0; i < retries; i++) {\r\n    try {\r\n      const result = await ai.generate(request);\r\n      return result;\r\n    } catch (e: any) {\r\n      if (e.message && e.message.includes('503') && i < retries - 1) {\r\n        console.log(`Attempt ${i + 1} failed with 503. Retrying in ${delay}ms...`);\r\n        await new Promise(resolve => setTimeout(resolve, delay));\r\n      } else {\r\n        if (e.message && e.message.includes('503')) {\r\n          throw new Error(\"The AI model is currently overloaded. Please try again in a few moments.\");\r\n        }\r\n        if (e.message && e.message.includes('429')) {\r\n          throw new Error(\"You've exceeded your request limit for the AI model. Please check your plan or try again later.\");\r\n        }\r\n        throw e; // Rethrow other errors immediately\r\n      }\r\n    }\r\n  }\r\n  // This line should not be reachable if retries are configured, but as a fallback:\r\n  throw new Error(\"The AI model is currently overloaded after multiple retries. Please try again later.\");\r\n}\r\n\r\nconst getMimeTypeFromDataURI = (dataURI: string): string => {\r\n  const match = dataURI.match(/^data:(.*?);/);\r\n  return match ? match[1] : 'application/octet-stream'; // Default if no match\r\n};\r\n\r\n// Helper function to generate an image for a single variant with advanced design principles\r\nasync function generateImageForVariant(\r\n  variant: { platform: string, aspectRatio: string },\r\n  input: GeneratePostFromProfileInput,\r\n  textOutput: { imageText: string }\r\n) {\r\n  // Determine consistency level based on preferences first\r\n  const isStrictConsistency = input.brandConsistency?.strictConsistency ?? false;\r\n  const followBrandColors = input.brandConsistency?.followBrandColors ?? true;\r\n\r\n  // Enhanced color instructions with psychology and usage guidelines\r\n  const colorInstructions = followBrandColors ? `\r\n  **BRAND COLOR PALETTE (MANDATORY):**\r\n  - Primary Color: ${input.primaryColor} - Use for main elements, headers, and key focal points\r\n  - Accent Color: ${input.accentColor} - Use for highlights, buttons, and secondary elements\r\n  - Background Color: ${input.backgroundColor} - Use for backgrounds and neutral areas\r\n\r\n  **COLOR USAGE REQUIREMENTS:**\r\n  - Primary color should dominate the design (40-60% of color usage)\r\n  - Accent color for emphasis and call-to-action elements (20-30% of color usage)\r\n  - Background color for balance and readability (10-40% of color usage)\r\n  - Ensure high contrast ratios for text readability (minimum 4.5:1)\r\n  - Use color gradients and variations within the brand palette\r\n  - Avoid colors outside the brand palette unless absolutely necessary for contrast\r\n  ` : `\r\n  **COLOR GUIDANCE:**\r\n  - Brand colors available: Primary ${input.primaryColor}, Accent ${input.accentColor}, Background ${input.backgroundColor}\r\n  - Feel free to use complementary colors that work well with the brand palette\r\n  - Maintain visual harmony and professional appearance\r\n  `;\r\n\r\n  // Get platform-specific guidelines\r\n  const platformGuidelines = PLATFORM_SPECIFIC_GUIDELINES[variant.platform as keyof typeof PLATFORM_SPECIFIC_GUIDELINES] || PLATFORM_SPECIFIC_GUIDELINES.instagram;\r\n\r\n  // Get business-specific design DNA\r\n  const businessDNA = BUSINESS_TYPE_DESIGN_DNA[input.businessType as keyof typeof BUSINESS_TYPE_DESIGN_DNA] || BUSINESS_TYPE_DESIGN_DNA.default;\r\n\r\n  // Get current design trends\r\n  let trendInstructions = '';\r\n  try {\r\n    const trends = await getCachedDesignTrends(\r\n      input.businessType,\r\n      variant.platform,\r\n      input.targetAudience,\r\n      input.businessType\r\n    );\r\n    trendInstructions = generateTrendInstructions(trends, variant.platform);\r\n  } catch (error) {\r\n    console.warn('Failed to get design trends, continuing without:', error);\r\n  }\r\n\r\n  // Get performance-optimized instructions\r\n  const performanceInstructions = generatePerformanceOptimizedInstructions(\r\n    input.businessType,\r\n    variant.platform,\r\n    input.visualStyle\r\n  );\r\n\r\n  // Enhanced brand context for better design generation\r\n  const businessContext = `\r\n  **BUSINESS PROFILE:**\r\n  - Name: ${input.businessName || 'Business'}\r\n  - Type: ${input.businessType}\r\n  - Location: ${input.location}\r\n  - Description: ${input.description || 'Professional business'}\r\n  ${input.services ? `- Services: ${input.services.split('\\n').slice(0, 3).join(', ')}` : ''}\r\n  ${input.targetAudience ? `- Target Audience: ${input.targetAudience}` : ''}\r\n  ${input.websiteUrl ? `- Website: ${input.websiteUrl}` : ''}\r\n  `;\r\n\r\n  // Generate visual variation approach for diversity\r\n  const visualVariations = [\r\n    'minimalist_clean', 'bold_dynamic', 'elegant_sophisticated', 'playful_creative',\r\n    'modern_geometric', 'organic_natural', 'industrial_urban', 'artistic_abstract',\r\n    'photographic_realistic', 'illustrated_stylized', 'gradient_colorful', 'monochrome_accent'\r\n  ];\r\n  const selectedVisualVariation = visualVariations[Math.floor(Math.random() * visualVariations.length)];\r\n  console.log(`🎨 Selected visual variation: ${selectedVisualVariation}`);\r\n\r\n  let imagePrompt = `Create a stunning, professional social media design for ${input.businessName || input.businessType} business.\r\n\r\n  **VISUAL APPROACH:** ${selectedVisualVariation} (MANDATORY: Use this specific visual style approach)\r\n\r\n    ${businessContext}\r\n\r\n    **DESIGN SPECIFICATIONS:**\r\n    - Platform: ${variant.platform} (${variant.aspectRatio} aspect ratio)\r\n    - Visual Style: ${input.visualStyle}, modern, clean, professional\r\n    - Text Content: \"${combineTextComponents(textOutput.catchyWords, textOutput.subheadline, textOutput.callToAction)}\"\r\n\r\n    ${colorInstructions}\r\n\r\n    **DESIGN REQUIREMENTS:**\r\n    - High-quality, professional design that reflects the business personality\r\n    - Clear, readable text with excellent contrast (minimum 4.5:1 ratio)\r\n    - ${input.visualStyle} aesthetic that appeals to ${input.targetAudience || 'target audience'}\r\n    - Perfect representation of ${input.businessType} business values\r\n    - Brand colors prominently and strategically featured\r\n    - Clean, modern layout optimized for ${variant.platform}\r\n    - Professional social media appearance that drives engagement\r\n    - Text must be perfectly readable, properly sized, and not cut off\r\n    - ${variant.aspectRatio} aspect ratio optimized for ${variant.platform}\r\n    - Design should reflect the business's location (${input.location}) and cultural context\r\n\r\n    **BUSINESS DNA INTEGRATION:**\r\n    ${businessDNA}\r\n\r\n    **PLATFORM OPTIMIZATION:**\r\n    ${platformGuidelines.designGuidelines || `Optimize for ${variant.platform} best practices`}\r\n\r\n    Create a beautiful, professional design that authentically represents the business and drives engagement.`;\r\n\r\n  // Intelligent design examples processing\r\n  let designDNA = '';\r\n  let selectedExamples: string[] = [];\r\n\r\n  if (input.designExamples && input.designExamples.length > 0) {\r\n    try {\r\n      // Analyze design examples for intelligent processing\r\n      const analyses: DesignAnalysis[] = [];\r\n      for (const example of input.designExamples.slice(0, 5)) { // Limit to 5 for performance\r\n        try {\r\n          const analysis = await analyzeDesignExample(\r\n            example,\r\n            input.businessType,\r\n            variant.platform,\r\n            `${input.visualStyle} design for ${textOutput.imageText}`\r\n          );\r\n          analyses.push(analysis);\r\n        } catch (error) {\r\n          console.warn('Design analysis failed for example, skipping:', error);\r\n        }\r\n      }\r\n\r\n      if (analyses.length > 0) {\r\n        // Extract design DNA from analyzed examples\r\n        designDNA = extractDesignDNA(analyses);\r\n\r\n        // Select optimal examples based on analysis\r\n        selectedExamples = selectOptimalDesignExamples(\r\n          input.designExamples,\r\n          analyses,\r\n          textOutput.imageText,\r\n          variant.platform,\r\n          isStrictConsistency ? 3 : 1\r\n        );\r\n      } else {\r\n        // Fallback to original logic if analysis fails\r\n        selectedExamples = isStrictConsistency\r\n          ? input.designExamples\r\n          : [input.designExamples[Math.floor(Math.random() * input.designExamples.length)]];\r\n      }\r\n    } catch (error) {\r\n      console.warn('Design analysis system failed, using fallback:', error);\r\n      selectedExamples = isStrictConsistency\r\n        ? input.designExamples\r\n        : [input.designExamples[Math.floor(Math.random() * input.designExamples.length)]];\r\n    }\r\n\r\n    // Add design consistency instructions based on analysis\r\n    if (isStrictConsistency) {\r\n      imagePrompt += `\\n    **STRICT STYLE REFERENCE:**\r\n      Use the provided design examples as strict style reference. Closely match the visual aesthetic, color scheme, typography, layout patterns, and overall design approach of the reference designs. Create content that looks very similar to the uploaded examples while incorporating the new text and subject matter.\r\n\r\n      ${designDNA}`;\r\n    } else {\r\n      imagePrompt += `\\n    **STYLE INSPIRATION:**\r\n      Use the provided design examples as loose inspiration for the overall aesthetic and mood, but feel free to create more varied and creative designs while maintaining the brand essence.\r\n\r\n      ${designDNA}\r\n\r\n      **CREATIVE VARIATION:** Feel free to experiment with different layouts, compositions, and design elements to create fresh, engaging content that avoids repetitive appearance while maintaining brand recognition.\r\n\r\n      **STRICT UNIQUENESS REQUIREMENT:** This design MUST be completely different from any previous generation. MANDATORY variations:\r\n      - Layout compositions: Choose from grid, asymmetrical, centered, diagonal, circular, or organic layouts\r\n      - Color emphasis: Vary primary/accent color dominance (primary-heavy, accent-heavy, or balanced)\r\n      - Typography placement: Top, bottom, center, side, overlay, or integrated into imagery\r\n      - Visual elements: Abstract shapes, geometric patterns, organic forms, or photographic elements\r\n      - Background treatments: Solid, gradient, textured, patterned, or photographic\r\n      - Design style: Minimalist, bold, elegant, playful, modern, or artistic\r\n      - Content arrangement: Single focus, multiple elements, layered, or split-screen\r\n\r\n      **DIVERSITY ENFORCEMENT:**\r\n      - Never repeat the same layout pattern twice in a row\r\n      - Alternate between different color emphasis approaches\r\n      - Vary typography size, weight, and positioning significantly\r\n      - Use different visual metaphors and imagery styles\r\n      - Change background complexity and treatment style\r\n\r\n      **GENERATION ID:** ${Date.now()}_${Math.random().toString(36).substr(2, 9)} - Use this unique identifier to ensure no two designs are identical.`;\r\n    }\r\n  }\r\n\r\n  // Build prompt parts array\r\n  const promptParts: any[] = [{ text: imagePrompt }];\r\n\r\n  // Enhanced logo integration with analysis\r\n  if (input.logoDataUrl) {\r\n    // Add logo analysis instructions to the prompt\r\n    const logoInstructions = `\r\n\r\n    **CRITICAL LOGO USAGE REQUIREMENTS:**\r\n    🚨 MANDATORY: You MUST use the uploaded brand logo image provided below. DO NOT create, generate, or design a new logo.\r\n\r\n    **LOGO INTEGRATION REQUIREMENTS:**\r\n    - Use ONLY the provided logo image - never create or generate a new logo\r\n    - The uploaded logo is the official brand logo and must be used exactly as provided\r\n    - Incorporate the provided logo naturally and prominently into the design\r\n    - Ensure logo is clearly visible and properly sized for the platform (minimum 10% of design area)\r\n    - Maintain logo's original proportions and readability - do not distort or modify the logo\r\n    - Position logo strategically: ${platformGuidelines.logoPlacement || 'Place logo prominently in corner or integrated into layout'}\r\n    - Ensure sufficient contrast between logo and background (minimum 4.5:1 ratio)\r\n    - For ${variant.platform}: Logo should be clearly visible and recognizable\r\n\r\n    **BRAND CONSISTENCY WITH UPLOADED LOGO:**\r\n    - Extract and use colors from the provided logo for the overall color scheme\r\n    - Match the design style to complement the logo's aesthetic and personality\r\n    - Ensure visual harmony between the uploaded logo and all design elements\r\n    - The logo is the primary brand identifier - treat it as the most important visual element\r\n\r\n    **LOGO PLACEMENT PRIORITY:**\r\n    - Logo visibility is more important than other design elements\r\n    - If space is limited, reduce other elements to ensure logo prominence\r\n    - Logo should be one of the first things viewers notice in the design\r\n    `;\r\n\r\n    // Update the main prompt with logo instructions\r\n    promptParts[0].text += logoInstructions;\r\n\r\n    // Add logo as media with high priority\r\n    promptParts.push({\r\n      media: {\r\n        url: input.logoDataUrl,\r\n        contentType: getMimeTypeFromDataURI(input.logoDataUrl)\r\n      }\r\n    });\r\n\r\n    console.log(`🎨 Logo integrated: ${input.logoDataUrl.substring(0, 50)}...`);\r\n  } else {\r\n    console.log('⚠️ No logo provided - design will be generated without brand logo');\r\n  }\r\n\r\n  // Add selected design examples\r\n  selectedExamples.forEach(example => {\r\n    promptParts.push({ media: { url: example, contentType: getMimeTypeFromDataURI(example) } });\r\n  });\r\n\r\n  // Generate initial design\r\n  let finalImageUrl = '';\r\n  let attempts = 0;\r\n  const maxAttempts = 2; // Limit attempts to avoid excessive API calls\r\n\r\n  while (attempts < maxAttempts) {\r\n    attempts++;\r\n\r\n    try {\r\n      const { media } = await generateWithRetry({\r\n        model: 'googleai/gemini-2.5-flash',\r\n        prompt: promptParts,\r\n        config: {\r\n          responseModalities: ['TEXT', 'IMAGE'],\r\n        },\r\n      });\r\n\r\n      let imageUrl = media?.url ?? '';\r\n      if (!imageUrl) {\r\n        throw new Error('No image generated');\r\n      }\r\n\r\n      // Apply aspect ratio correction for non-square platforms\r\n      const { cropImageFromUrl, needsAspectRatioCorrection } = await import('@/lib/image-processing');\r\n      if (needsAspectRatioCorrection(variant.platform)) {\r\n        console.log(`🖼️ Applying aspect ratio correction for ${variant.platform}...`);\r\n        try {\r\n          imageUrl = await cropImageFromUrl(imageUrl, variant.platform);\r\n          console.log(`✅ Image cropped successfully for ${variant.platform}`);\r\n        } catch (cropError) {\r\n          console.warn('⚠️ Image cropping failed, using original:', cropError);\r\n          // Continue with original image if cropping fails\r\n        }\r\n      }\r\n\r\n      // Quality validation for first attempt\r\n      if (attempts === 1) {\r\n        try {\r\n          const quality = await assessDesignQuality(\r\n            imageUrl,\r\n            input.businessType,\r\n            variant.platform,\r\n            input.visualStyle,\r\n            followBrandColors && input.primaryColor ? colorInstructions : undefined,\r\n            `Create engaging design for: ${textOutput.catchyWords}`\r\n          );\r\n\r\n          // If quality is acceptable, use this design\r\n          if (meetsQualityStandards(quality, 7)) {\r\n            finalImageUrl = imageUrl;\r\n            break;\r\n          }\r\n\r\n          // If quality is poor and we have attempts left, try to improve\r\n          if (attempts < maxAttempts) {\r\n            console.log(`Design quality score: ${quality.overall.score}/10. Attempting improvement...`);\r\n\r\n            // Add improvement instructions to prompt\r\n            const improvementInstructions = generateImprovementPrompt(quality);\r\n            const improvedPrompt = `${imagePrompt}\\n\\n${improvementInstructions}`;\r\n            promptParts[0] = { text: improvedPrompt };\r\n            continue;\r\n          } else {\r\n            // Use the design even if quality is subpar (better than nothing)\r\n            finalImageUrl = imageUrl;\r\n            break;\r\n          }\r\n        } catch (qualityError) {\r\n          console.warn('Quality assessment failed, using generated design:', qualityError);\r\n          finalImageUrl = imageUrl;\r\n          break;\r\n        }\r\n      } else {\r\n        // For subsequent attempts, use the result\r\n        finalImageUrl = imageUrl;\r\n        break;\r\n      }\r\n    } catch (error) {\r\n      console.error(`Design generation attempt ${attempts} failed:`, error);\r\n      if (attempts === maxAttempts) {\r\n        throw error;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Record design generation for analytics\r\n  if (finalImageUrl) {\r\n    try {\r\n      const designId = `design_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n      recordDesignGeneration(\r\n        designId,\r\n        input.businessType,\r\n        variant.platform,\r\n        input.visualStyle,\r\n        9.2, // HD quality score with enhanced settings and prompting\r\n        {\r\n          colorPalette: input.primaryColor ? [input.primaryColor, input.accentColor, input.backgroundColor].filter(Boolean) : [],\r\n          typography: 'Modern social media optimized',\r\n          composition: variant.aspectRatio,\r\n          trends: selectedExamples.length > 0 ? ['design-examples-based'] : ['ai-generated'],\r\n          businessDNA: businessDNA.substring(0, 100) // Truncate for storage\r\n        },\r\n        {\r\n          engagement: 8,\r\n          brandAlignment: followBrandColors ? 9 : 7,\r\n          technicalQuality: 8,\r\n          trendRelevance: trendInstructions ? 8 : 6\r\n        }\r\n      );\r\n    } catch (analyticsError) {\r\n      console.warn('Failed to record design analytics:', analyticsError);\r\n    }\r\n  }\r\n\r\n  return {\r\n    platform: variant.platform,\r\n    imageUrl: finalImageUrl,\r\n  }\r\n}\r\n\r\n\r\nconst generatePostFromProfileFlow = ai.defineFlow(\r\n  {\r\n    name: 'generatePostFromProfileFlow',\r\n    inputSchema: GeneratePostFromProfileInputSchema,\r\n    outputSchema: GeneratePostFromProfileOutputSchema,\r\n  },\r\n  async (input) => {\r\n    // Determine the primary platform for optimization\r\n    const primaryPlatform = input.variants[0]?.platform || 'instagram';\r\n\r\n    // Generate unique content variation approach to ensure diversity\r\n    const contentVariations = [\r\n      'trending_hook', 'story_driven', 'educational_tip', 'behind_scenes',\r\n      'question_engagement', 'statistic_driven', 'personal_insight', 'industry_contrarian',\r\n      'local_cultural', 'seasonal_relevance', 'problem_solution', 'inspiration_motivation'\r\n    ];\r\n    const selectedVariation = contentVariations[Math.floor(Math.random() * contentVariations.length)];\r\n    console.log(`🎯 Selected content variation approach: ${selectedVariation}`);\r\n\r\n    // Step 1: Intelligent Context Analysis - Determine what information is relevant\r\n    const contextRelevance = selectRelevantContext(\r\n      input.businessType,\r\n      input.location,\r\n      primaryPlatform,\r\n      input.contentThemes,\r\n      input.dayOfWeek\r\n    );\r\n\r\n    console.log(`🧠 Context Analysis for ${input.businessType} in ${input.location}:`);\r\n    console.log(`   Weather: ${contextRelevance.weather.priority} - ${contextRelevance.weather.relevanceReason}`);\r\n    console.log(`   Events: ${contextRelevance.events.priority} - ${contextRelevance.events.relevanceReason}`);\r\n    console.log(`   Trends: ${contextRelevance.trends.priority} - ${contextRelevance.trends.relevanceReason}`);\r\n    console.log(`   Culture: ${contextRelevance.cultural.priority} - ${contextRelevance.cultural.relevanceReason}`);\r\n\r\n    // Step 2: Fetch Real-Time Trending Topics (always useful)\r\n    const realTimeTrends = await generateRealTimeTrendingTopics(\r\n      input.businessType,\r\n      input.location,\r\n      primaryPlatform\r\n    );\r\n\r\n    // Step 3: Fetch Local Context (Weather + Events) - but filter intelligently\r\n    const rawLocalContext = await fetchLocalContext(\r\n      input.location,\r\n      input.businessType\r\n    );\r\n\r\n    // Step 4: Generate Market Intelligence for Advanced Content\r\n    const marketIntelligence = generateMarketIntelligence(\r\n      input.businessType,\r\n      input.location,\r\n      primaryPlatform,\r\n      input.services\r\n    );\r\n\r\n    // Step 5: Intelligently Filter Context Data\r\n    const filteredContext = filterContextData(contextRelevance, {\r\n      weather: rawLocalContext.weather,\r\n      events: rawLocalContext.events,\r\n      trends: realTimeTrends,\r\n      cultural: marketIntelligence.cultural_context\r\n    });\r\n\r\n    // Enhance market intelligence with filtered real-time trends\r\n    marketIntelligence.trending_topics = [\r\n      ...(filteredContext.selectedTrends || []).slice(0, 3),\r\n      ...marketIntelligence.trending_topics.slice(0, 2)\r\n    ];\r\n\r\n    // Step 6: Generate Human-like Content Techniques\r\n    const humanizationTechniques = generateHumanizationTechniques(\r\n      input.businessType,\r\n      input.writingTone,\r\n      input.location\r\n    );\r\n\r\n    // Step 7: Generate Traffic-Driving Elements\r\n    const trafficElements = generateTrafficDrivingElements(\r\n      input.businessType,\r\n      primaryPlatform,\r\n      input.targetAudience\r\n    );\r\n\r\n    // Step 8: Generate Enhanced Text Content with Intelligent Context\r\n    const { output: textOutput } = await enhancedTextGenPrompt({\r\n      businessType: input.businessType,\r\n      location: input.location,\r\n      writingTone: input.writingTone,\r\n      contentThemes: input.contentThemes,\r\n      dayOfWeek: input.dayOfWeek,\r\n      currentDate: input.currentDate,\r\n      platform: primaryPlatform,\r\n      services: input.services,\r\n      targetAudience: input.targetAudience,\r\n      keyFeatures: input.keyFeatures,\r\n      competitiveAdvantages: input.competitiveAdvantages,\r\n      // Add intelligent context instructions\r\n      contextInstructions: filteredContext.contextInstructions,\r\n      selectedWeather: filteredContext.selectedWeather,\r\n      selectedEvents: filteredContext.selectedEvents,\r\n      selectedTrends: filteredContext.selectedTrends,\r\n      selectedCultural: filteredContext.selectedCultural,\r\n      // Add content variation for diversity\r\n      contentVariation: selectedVariation,\r\n      // Language preferences\r\n      useLocalLanguage: input.useLocalLanguage || false,\r\n    });\r\n\r\n    if (!textOutput) {\r\n      throw new Error('Failed to generate advanced AI post content.');\r\n    }\r\n\r\n    // Step 9: Generate Strategic Hashtag Analysis (exactly 10 hashtags)\r\n    const hashtagStrategy = generateHashtagStrategy(\r\n      input.businessType,\r\n      input.location,\r\n      primaryPlatform,\r\n      input.services,\r\n      input.targetAudience,\r\n      textOutput.catchyWords || textOutput.content, // Post topic from generated content\r\n      input.visualStyle || 'modern' // Design style\r\n    );\r\n\r\n    // Step 10: Generate Image for each variant in parallel\r\n    const imagePromises = input.variants.map(variant =>\r\n      generateImageForVariant(variant, input, textOutput)\r\n    );\r\n\r\n    const variants = await Promise.all(imagePromises);\r\n\r\n    // Step 11: Combine text components for image overlay\r\n    const combinedImageText = combineTextComponents(\r\n      textOutput.catchyWords,\r\n      textOutput.subheadline,\r\n      textOutput.callToAction\r\n    );\r\n\r\n    // Step 12: Convert hashtag strategy to exactly 10 hashtags\r\n    const finalHashtags = [\r\n      ...hashtagStrategy.trending,\r\n      ...hashtagStrategy.niche,\r\n      ...hashtagStrategy.branded,\r\n      ...hashtagStrategy.location,\r\n      ...hashtagStrategy.community\r\n    ].slice(0, 10); // Ensure exactly 10 hashtags\r\n\r\n    // Step 13: Combine results with intelligently selected context\r\n    return {\r\n      content: textOutput.content,\r\n      catchyWords: textOutput.catchyWords,\r\n      subheadline: textOutput.subheadline,\r\n      callToAction: textOutput.callToAction,\r\n      hashtags: finalHashtags.join(' '), // Convert to string format\r\n      contentVariants: textOutput.contentVariants,\r\n      hashtagAnalysis: {\r\n        trending: hashtagStrategy.trending,\r\n        niche: hashtagStrategy.niche,\r\n        location: hashtagStrategy.location,\r\n        community: hashtagStrategy.community,\r\n      },\r\n      // Advanced AI features metadata (for future UI display)\r\n      marketIntelligence: {\r\n        trending_topics: marketIntelligence.trending_topics.slice(0, 3),\r\n        competitor_insights: marketIntelligence.competitor_insights.slice(0, 2),\r\n        cultural_context: marketIntelligence.cultural_context,\r\n        viral_patterns: marketIntelligence.viral_content_patterns.slice(0, 3),\r\n        engagement_triggers: marketIntelligence.engagement_triggers.slice(0, 3)\r\n      },\r\n      // Intelligently selected local context\r\n      localContext: {\r\n        weather: filteredContext.selectedWeather,\r\n        events: filteredContext.selectedEvents,\r\n        contextRelevance: {\r\n          weather: contextRelevance.weather.priority,\r\n          events: contextRelevance.events.priority,\r\n          weatherReason: contextRelevance.weather.relevanceReason,\r\n          eventsReason: contextRelevance.events.relevanceReason\r\n        }\r\n      },\r\n      variants,\r\n    };\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;;;AAGA;;;;;;;CAOC,GAED;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AAMA;AAMA;AAKA;;;;;;;;;;;;;;;;;;;AAKA,MAAM,qCAAqC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClD,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAClC,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,eAAe,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACnC,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,gBAAgB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAC;IACxD,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,UAAU,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAAI,QAAQ,CAAC;IACb,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC7C,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC5C,iBAAiB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEhD,yCAAyC;IACzC,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACzC,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC/C,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC5C,uBAAuB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEtD,gCAAgC;IAChC,kBAAkB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,mBAAmB,sIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;QACvC,mBAAmB,sIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IACzC,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEvB,yBAAyB;IACzB,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC3C,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC5C,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACvB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC9B,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEvB,uBAAuB;IACvB,kBAAkB,sIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACpD;AAIA,MAAM,sCAAsC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnD,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC7B,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACvD,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACxD,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,iBAAiB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChC,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,IAAI,QAAQ,GAAG,QAAQ,CAAC;IACxB,iBAAiB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACxB,UAAU,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACvC,OAAO,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACpC,UAAU,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACvC,WAAW,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC1C,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACvB,oBAAoB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC3B,iBAAiB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YAChC,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM;YACf,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM;YACxB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,sBAAsB,sIAAA,CAAA,IAAC,CAAC,MAAM;QAChC,IAAI,QAAQ,CAAC;QACb,qBAAqB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACpC,iBAAiB,sIAAA,CAAA,IAAC,CAAC,MAAM;YACzB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,6BAA6B,sIAAA,CAAA,IAAC,CAAC,MAAM;QACvC,IAAI,QAAQ,CAAC;QACb,kBAAkB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACzB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,kBAAkB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM;YAClC,eAAe,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM;QACjC,GAAG,QAAQ,CAAC;QACZ,gBAAgB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QAC7C,qBAAqB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IACpD,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACvB,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YAChB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM;YACnB,iBAAiB,sIAAA,CAAA,IAAC,CAAC,MAAM;YACzB,uBAAuB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM;QACzC,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACvB,QAAQ,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACvB,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM;YACd,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,iBAAiB,sIAAA,CAAA,IAAC,CAAC,MAAM;YACzB,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM;QACtB,IAAI,QAAQ,GAAG,QAAQ,CAAC;IAC1B,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACvB,UAAU,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;IACpB;AACF;AAIO,eAAe,wBAAwB,KAAmC;IAC/E,OAAO,4BAA4B;AACrC;AAGA;;CAEC,GACD,SAAS,sBAAsB,WAAmB,EAAE,WAAoB,EAAE,YAAqB;IAC7F,MAAM,aAAa;QAAC;KAAY;IAEhC,IAAI,eAAe,YAAY,IAAI,IAAI;QACrC,WAAW,IAAI,CAAC,YAAY,IAAI;IAClC;IAEA,IAAI,gBAAgB,aAAa,IAAI,IAAI;QACvC,WAAW,IAAI,CAAC,aAAa,IAAI;IACnC;IAEA,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA,6CAA6C;AAC7C,MAAM,wBAAwB,qHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC5C,MAAM;IACN,OAAO;QACL,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM;YACtB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,eAAe,sIAAA,CAAA,IAAC,CAAC,MAAM;YACvB,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM;YACnB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACnC,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAChC,uBAAuB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC1C,kBAAkB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACrC,qBAAqB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACxC,iBAAiB,sIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;YACjC,gBAAgB,sIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;YAChC,gBAAgB,sIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;YAChC,kBAAkB,sIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;YAClC,kBAAkB,sIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;QACxC;IACF;IACA,QAAQ;QACN,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;YAC7B,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;YACjC,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;YACvD,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;YACxD,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;YAC9B,iBAAiB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;gBAChC,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;gBAC7B,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;gBAC9B,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;YACjC,IAAI,QAAQ,CAAC;QACf;IACF;IACA,OAAO;QAAC,qIAAA,CAAA,iBAAc;QAAE,qIAAA,CAAA,gBAAa;QAAE,iJAAA,CAAA,yBAAsB;QAAE,iJAAA,CAAA,wBAAqB;KAAC;IACrF,QAAQ,kJAAA,CAAA,qBAAkB;AAC5B;AAEA;;CAEC,GACD,eAAe,kBAAkB,OAAwB,EAAE,UAAU,CAAC,EAAE,QAAQ,IAAI;IAClF,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;QAChC,IAAI;YACF,MAAM,SAAS,MAAM,qHAAA,CAAA,KAAE,CAAC,QAAQ,CAAC;YACjC,OAAO;QACT,EAAE,OAAO,GAAQ;YACf,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,IAAI,UAAU,GAAG;gBAC7D,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,KAAK,CAAC;gBACzE,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD,OAAO;gBACL,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBAC1C,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBAC1C,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,GAAG,mCAAmC;YAC9C;QACF;IACF;IACA,kFAAkF;IAClF,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,yBAAyB,CAAC;IAC9B,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG,4BAA4B,sBAAsB;AAC9E;AAEA,4FAA4F;AAC5F,eAAe,wBACb,OAAkD,EAClD,KAAmC,EACnC,UAAiC;IAEjC,yDAAyD;IACzD,MAAM,sBAAsB,MAAM,gBAAgB,EAAE,qBAAqB;IACzE,MAAM,oBAAoB,MAAM,gBAAgB,EAAE,qBAAqB;IAEvE,mEAAmE;IACnE,MAAM,oBAAoB,oBAAoB,CAAC;;mBAE9B,EAAE,MAAM,YAAY,CAAC;kBACtB,EAAE,MAAM,WAAW,CAAC;sBAChB,EAAE,MAAM,eAAe,CAAC;;;;;;;;;EAS5C,CAAC,GAAG,CAAC;;oCAE6B,EAAE,MAAM,YAAY,CAAC,SAAS,EAAE,MAAM,WAAW,CAAC,aAAa,EAAE,MAAM,eAAe,CAAC;;;EAGzH,CAAC;IAED,mCAAmC;IACnC,MAAM,qBAAqB,uJAAA,CAAA,+BAA4B,CAAC,QAAQ,QAAQ,CAA8C,IAAI,uJAAA,CAAA,+BAA4B,CAAC,SAAS;IAEhK,mCAAmC;IACnC,MAAM,cAAc,uJAAA,CAAA,2BAAwB,CAAC,MAAM,YAAY,CAA0C,IAAI,uJAAA,CAAA,2BAAwB,CAAC,OAAO;IAE7I,4BAA4B;IAC5B,IAAI,oBAAoB;IACxB,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD,EACvC,MAAM,YAAY,EAClB,QAAQ,QAAQ,EAChB,MAAM,cAAc,EACpB,MAAM,YAAY;QAEpB,oBAAoB,CAAA,GAAA,wIAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,QAAQ,QAAQ;IACxE,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,oDAAoD;IACnE;IAEA,yCAAyC;IACzC,MAAM,0BAA0B,CAAA,GAAA,2IAAA,CAAA,2CAAwC,AAAD,EACrE,MAAM,YAAY,EAClB,QAAQ,QAAQ,EAChB,MAAM,WAAW;IAGnB,sDAAsD;IACtD,MAAM,kBAAkB,CAAC;;UAEjB,EAAE,MAAM,YAAY,IAAI,WAAW;UACnC,EAAE,MAAM,YAAY,CAAC;cACjB,EAAE,MAAM,QAAQ,CAAC;iBACd,EAAE,MAAM,WAAW,IAAI,wBAAwB;EAC9D,EAAE,MAAM,QAAQ,GAAG,CAAC,YAAY,EAAE,MAAM,QAAQ,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG;EAC3F,EAAE,MAAM,cAAc,GAAG,CAAC,mBAAmB,EAAE,MAAM,cAAc,EAAE,GAAG,GAAG;EAC3E,EAAE,MAAM,UAAU,GAAG,CAAC,WAAW,EAAE,MAAM,UAAU,EAAE,GAAG,GAAG;EAC3D,CAAC;IAED,mDAAmD;IACnD,MAAM,mBAAmB;QACvB;QAAoB;QAAgB;QAAyB;QAC7D;QAAoB;QAAmB;QAAoB;QAC3D;QAA0B;QAAwB;QAAqB;KACxE;IACD,MAAM,0BAA0B,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;IACrG,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,yBAAyB;IAEtE,IAAI,cAAc,CAAC,wDAAwD,EAAE,MAAM,YAAY,IAAI,MAAM,YAAY,CAAC;;uBAEjG,EAAE,wBAAwB;;IAE7C,EAAE,gBAAgB;;;gBAGN,EAAE,QAAQ,QAAQ,CAAC,EAAE,EAAE,QAAQ,WAAW,CAAC;oBACvC,EAAE,MAAM,WAAW,CAAC;qBACnB,EAAE,sBAAsB,WAAW,WAAW,EAAE,WAAW,WAAW,EAAE,WAAW,YAAY,EAAE;;IAElH,EAAE,kBAAkB;;;;;MAKlB,EAAE,MAAM,WAAW,CAAC,2BAA2B,EAAE,MAAM,cAAc,IAAI,kBAAkB;gCACjE,EAAE,MAAM,YAAY,CAAC;;yCAEZ,EAAE,QAAQ,QAAQ,CAAC;;;MAGtD,EAAE,QAAQ,WAAW,CAAC,4BAA4B,EAAE,QAAQ,QAAQ,CAAC;qDACtB,EAAE,MAAM,QAAQ,CAAC;;;IAGlE,EAAE,YAAY;;;IAGd,EAAE,mBAAmB,gBAAgB,IAAI,CAAC,aAAa,EAAE,QAAQ,QAAQ,CAAC,eAAe,CAAC,CAAC;;6GAEc,CAAC;IAE5G,yCAAyC;IACzC,IAAI,YAAY;IAChB,IAAI,mBAA6B,EAAE;IAEnC,IAAI,MAAM,cAAc,IAAI,MAAM,cAAc,CAAC,MAAM,GAAG,GAAG;QAC3D,IAAI;YACF,qDAAqD;YACrD,MAAM,WAA6B,EAAE;YACrC,KAAK,MAAM,WAAW,MAAM,cAAc,CAAC,KAAK,CAAC,GAAG,GAAI;gBACtD,IAAI;oBACF,MAAM,WAAW,MAAM,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EACxC,SACA,MAAM,YAAY,EAClB,QAAQ,QAAQ,EAChB,GAAG,MAAM,WAAW,CAAC,YAAY,EAAE,WAAW,SAAS,EAAE;oBAE3D,SAAS,IAAI,CAAC;gBAChB,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,iDAAiD;gBAChE;YACF;YAEA,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,4CAA4C;gBAC5C,YAAY,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD,EAAE;gBAE7B,4CAA4C;gBAC5C,mBAAmB,CAAA,GAAA,0IAAA,CAAA,8BAA2B,AAAD,EAC3C,MAAM,cAAc,EACpB,UACA,WAAW,SAAS,EACpB,QAAQ,QAAQ,EAChB,sBAAsB,IAAI;YAE9B,OAAO;gBACL,+CAA+C;gBAC/C,mBAAmB,sBACf,MAAM,cAAc,GACpB;oBAAC,MAAM,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,cAAc,CAAC,MAAM,EAAE;iBAAC;YACrF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,kDAAkD;YAC/D,mBAAmB,sBACf,MAAM,cAAc,GACpB;gBAAC,MAAM,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,cAAc,CAAC,MAAM,EAAE;aAAC;QACrF;QAEA,wDAAwD;QACxD,IAAI,qBAAqB;YACvB,eAAe,CAAC;;;MAGhB,EAAE,WAAW;QACf,OAAO;YACL,eAAe,CAAC;;;MAGhB,EAAE,UAAU;;;;;;;;;;;;;;;;;;;;yBAoBO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,qEAAqE,CAAC;QACnJ;IACF;IAEA,2BAA2B;IAC3B,MAAM,cAAqB;QAAC;YAAE,MAAM;QAAY;KAAE;IAElD,0CAA0C;IAC1C,IAAI,MAAM,WAAW,EAAE;QACrB,+CAA+C;QAC/C,MAAM,mBAAmB,CAAC;;;;;;;;;;;mCAWK,EAAE,mBAAmB,aAAa,IAAI,6DAA6D;;UAE5H,EAAE,QAAQ,QAAQ,CAAC;;;;;;;;;;;;IAYzB,CAAC;QAED,gDAAgD;QAChD,WAAW,CAAC,EAAE,CAAC,IAAI,IAAI;QAEvB,uCAAuC;QACvC,YAAY,IAAI,CAAC;YACf,OAAO;gBACL,KAAK,MAAM,WAAW;gBACtB,aAAa,uBAAuB,MAAM,WAAW;YACvD;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,MAAM,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;IAC5E,OAAO;QACL,QAAQ,GAAG,CAAC;IACd;IAEA,+BAA+B;IAC/B,iBAAiB,OAAO,CAAC,CAAA;QACvB,YAAY,IAAI,CAAC;YAAE,OAAO;gBAAE,KAAK;gBAAS,aAAa,uBAAuB;YAAS;QAAE;IAC3F;IAEA,0BAA0B;IAC1B,IAAI,gBAAgB;IACpB,IAAI,WAAW;IACf,MAAM,cAAc,GAAG,8CAA8C;IAErE,MAAO,WAAW,YAAa;QAC7B;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kBAAkB;gBACxC,OAAO;gBACP,QAAQ;gBACR,QAAQ;oBACN,oBAAoB;wBAAC;wBAAQ;qBAAQ;gBACvC;YACF;YAEA,IAAI,WAAW,OAAO,OAAO;YAC7B,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,yDAAyD;YACzD,MAAM,EAAE,gBAAgB,EAAE,0BAA0B,EAAE,GAAG;YACzD,IAAI,2BAA2B,QAAQ,QAAQ,GAAG;gBAChD,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,QAAQ,QAAQ,CAAC,GAAG,CAAC;gBAC7E,IAAI;oBACF,WAAW,MAAM,iBAAiB,UAAU,QAAQ,QAAQ;oBAC5D,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,QAAQ,QAAQ,EAAE;gBACpE,EAAE,OAAO,WAAW;oBAClB,QAAQ,IAAI,CAAC,6CAA6C;gBAC1D,iDAAiD;gBACnD;YACF;YAEA,uCAAuC;YACvC,IAAI,aAAa,GAAG;gBAClB,IAAI;oBACF,MAAM,UAAU,MAAM,CAAA,GAAA,yIAAA,CAAA,sBAAmB,AAAD,EACtC,UACA,MAAM,YAAY,EAClB,QAAQ,QAAQ,EAChB,MAAM,WAAW,EACjB,qBAAqB,MAAM,YAAY,GAAG,oBAAoB,WAC9D,CAAC,4BAA4B,EAAE,WAAW,WAAW,EAAE;oBAGzD,4CAA4C;oBAC5C,IAAI,CAAA,GAAA,yIAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,IAAI;wBACrC,gBAAgB;wBAChB;oBACF;oBAEA,+DAA+D;oBAC/D,IAAI,WAAW,aAAa;wBAC1B,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,QAAQ,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC;wBAE1F,yCAAyC;wBACzC,MAAM,0BAA0B,CAAA,GAAA,yIAAA,CAAA,4BAAyB,AAAD,EAAE;wBAC1D,MAAM,iBAAiB,GAAG,YAAY,IAAI,EAAE,yBAAyB;wBACrE,WAAW,CAAC,EAAE,GAAG;4BAAE,MAAM;wBAAe;wBACxC;oBACF,OAAO;wBACL,iEAAiE;wBACjE,gBAAgB;wBAChB;oBACF;gBACF,EAAE,OAAO,cAAc;oBACrB,QAAQ,IAAI,CAAC,sDAAsD;oBACnE,gBAAgB;oBAChB;gBACF;YACF,OAAO;gBACL,0CAA0C;gBAC1C,gBAAgB;gBAChB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,SAAS,QAAQ,CAAC,EAAE;YAC/D,IAAI,aAAa,aAAa;gBAC5B,MAAM;YACR;QACF;IACF;IAEA,yCAAyC;IACzC,IAAI,eAAe;QACjB,IAAI;YACF,MAAM,WAAW,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAClF,CAAA,GAAA,2IAAA,CAAA,yBAAsB,AAAD,EACnB,UACA,MAAM,YAAY,EAClB,QAAQ,QAAQ,EAChB,MAAM,WAAW,EACjB,KACA;gBACE,cAAc,MAAM,YAAY,GAAG;oBAAC,MAAM,YAAY;oBAAE,MAAM,WAAW;oBAAE,MAAM,eAAe;iBAAC,CAAC,MAAM,CAAC,WAAW,EAAE;gBACtH,YAAY;gBACZ,aAAa,QAAQ,WAAW;gBAChC,QAAQ,iBAAiB,MAAM,GAAG,IAAI;oBAAC;iBAAwB,GAAG;oBAAC;iBAAe;gBAClF,aAAa,YAAY,SAAS,CAAC,GAAG,KAAK,uBAAuB;YACpE,GACA;gBACE,YAAY;gBACZ,gBAAgB,oBAAoB,IAAI;gBACxC,kBAAkB;gBAClB,gBAAgB,oBAAoB,IAAI;YAC1C;QAEJ,EAAE,OAAO,gBAAgB;YACvB,QAAQ,IAAI,CAAC,sCAAsC;QACrD;IACF;IAEA,OAAO;QACL,UAAU,QAAQ,QAAQ;QAC1B,UAAU;IACZ;AACF;AAGA,MAAM,8BAA8B,qHAAA,CAAA,KAAE,CAAC,UAAU,CAC/C;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO;IACL,kDAAkD;IAClD,MAAM,kBAAkB,MAAM,QAAQ,CAAC,EAAE,EAAE,YAAY;IAEvD,iEAAiE;IACjE,MAAM,oBAAoB;QACxB;QAAiB;QAAgB;QAAmB;QACpD;QAAuB;QAAoB;QAAoB;QAC/D;QAAkB;QAAsB;QAAoB;KAC7D;IACD,MAAM,oBAAoB,iBAAiB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,kBAAkB,MAAM,EAAE;IACjG,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,mBAAmB;IAE1E,gFAAgF;IAChF,MAAM,mBAAmB,CAAA,GAAA,0JAAA,CAAA,wBAAqB,AAAD,EAC3C,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,MAAM,aAAa,EACnB,MAAM,SAAS;IAGjB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,MAAM,YAAY,CAAC,IAAI,EAAE,MAAM,QAAQ,CAAC,CAAC,CAAC;IACjF,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,iBAAiB,OAAO,CAAC,eAAe,EAAE;IAC5G,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,iBAAiB,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,iBAAiB,MAAM,CAAC,eAAe,EAAE;IACzG,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,iBAAiB,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,iBAAiB,MAAM,CAAC,eAAe,EAAE;IACzG,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,iBAAiB,QAAQ,CAAC,eAAe,EAAE;IAE9G,0DAA0D;IAC1D,MAAM,iBAAiB,MAAM,CAAA,GAAA,0IAAA,CAAA,iCAA8B,AAAD,EACxD,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd;IAGF,4EAA4E;IAC5E,MAAM,kBAAkB,MAAM,CAAA,GAAA,6JAAA,CAAA,oBAAiB,AAAD,EAC5C,MAAM,QAAQ,EACd,MAAM,YAAY;IAGpB,4DAA4D;IAC5D,MAAM,qBAAqB,CAAA,GAAA,0IAAA,CAAA,6BAA0B,AAAD,EAClD,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,MAAM,QAAQ;IAGhB,4CAA4C;IAC5C,MAAM,kBAAkB,CAAA,GAAA,0JAAA,CAAA,oBAAiB,AAAD,EAAE,kBAAkB;QAC1D,SAAS,gBAAgB,OAAO;QAChC,QAAQ,gBAAgB,MAAM;QAC9B,QAAQ;QACR,UAAU,mBAAmB,gBAAgB;IAC/C;IAEA,6DAA6D;IAC7D,mBAAmB,eAAe,GAAG;WAChC,CAAC,gBAAgB,cAAc,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG;WAChD,mBAAmB,eAAe,CAAC,KAAK,CAAC,GAAG;KAChD;IAED,iDAAiD;IACjD,MAAM,yBAAyB,CAAA,GAAA,qJAAA,CAAA,iCAA8B,AAAD,EAC1D,MAAM,YAAY,EAClB,MAAM,WAAW,EACjB,MAAM,QAAQ;IAGhB,4CAA4C;IAC5C,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,iCAA8B,AAAD,EACnD,MAAM,YAAY,EAClB,iBACA,MAAM,cAAc;IAGtB,kEAAkE;IAClE,MAAM,EAAE,QAAQ,UAAU,EAAE,GAAG,MAAM,sBAAsB;QACzD,cAAc,MAAM,YAAY;QAChC,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,eAAe,MAAM,aAAa;QAClC,WAAW,MAAM,SAAS;QAC1B,aAAa,MAAM,WAAW;QAC9B,UAAU;QACV,UAAU,MAAM,QAAQ;QACxB,gBAAgB,MAAM,cAAc;QACpC,aAAa,MAAM,WAAW;QAC9B,uBAAuB,MAAM,qBAAqB;QAClD,uCAAuC;QACvC,qBAAqB,gBAAgB,mBAAmB;QACxD,iBAAiB,gBAAgB,eAAe;QAChD,gBAAgB,gBAAgB,cAAc;QAC9C,gBAAgB,gBAAgB,cAAc;QAC9C,kBAAkB,gBAAgB,gBAAgB;QAClD,sCAAsC;QACtC,kBAAkB;QAClB,uBAAuB;QACvB,kBAAkB,MAAM,gBAAgB,IAAI;IAC9C;IAEA,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM;IAClB;IAEA,oEAAoE;IACpE,MAAM,kBAAkB,CAAA,GAAA,2IAAA,CAAA,0BAAuB,AAAD,EAC5C,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,MAAM,QAAQ,EACd,MAAM,cAAc,EACpB,WAAW,WAAW,IAAI,WAAW,OAAO,EAC5C,MAAM,WAAW,IAAI,SAAS,eAAe;;IAG/C,uDAAuD;IACvD,MAAM,gBAAgB,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,UACvC,wBAAwB,SAAS,OAAO;IAG1C,MAAM,WAAW,MAAM,QAAQ,GAAG,CAAC;IAEnC,qDAAqD;IACrD,MAAM,oBAAoB,sBACxB,WAAW,WAAW,EACtB,WAAW,WAAW,EACtB,WAAW,YAAY;IAGzB,2DAA2D;IAC3D,MAAM,gBAAgB;WACjB,gBAAgB,QAAQ;WACxB,gBAAgB,KAAK;WACrB,gBAAgB,OAAO;WACvB,gBAAgB,QAAQ;WACxB,gBAAgB,SAAS;KAC7B,CAAC,KAAK,CAAC,GAAG,KAAK,6BAA6B;IAE7C,+DAA+D;IAC/D,OAAO;QACL,SAAS,WAAW,OAAO;QAC3B,aAAa,WAAW,WAAW;QACnC,aAAa,WAAW,WAAW;QACnC,cAAc,WAAW,YAAY;QACrC,UAAU,cAAc,IAAI,CAAC;QAC7B,iBAAiB,WAAW,eAAe;QAC3C,iBAAiB;YACf,UAAU,gBAAgB,QAAQ;YAClC,OAAO,gBAAgB,KAAK;YAC5B,UAAU,gBAAgB,QAAQ;YAClC,WAAW,gBAAgB,SAAS;QACtC;QACA,wDAAwD;QACxD,oBAAoB;YAClB,iBAAiB,mBAAmB,eAAe,CAAC,KAAK,CAAC,GAAG;YAC7D,qBAAqB,mBAAmB,mBAAmB,CAAC,KAAK,CAAC,GAAG;YACrE,kBAAkB,mBAAmB,gBAAgB;YACrD,gBAAgB,mBAAmB,sBAAsB,CAAC,KAAK,CAAC,GAAG;YACnE,qBAAqB,mBAAmB,mBAAmB,CAAC,KAAK,CAAC,GAAG;QACvE;QACA,uCAAuC;QACvC,cAAc;YACZ,SAAS,gBAAgB,eAAe;YACxC,QAAQ,gBAAgB,cAAc;YACtC,kBAAkB;gBAChB,SAAS,iBAAiB,OAAO,CAAC,QAAQ;gBAC1C,QAAQ,iBAAiB,MAAM,CAAC,QAAQ;gBACxC,eAAe,iBAAiB,OAAO,CAAC,eAAe;gBACvD,cAAc,iBAAiB,MAAM,CAAC,eAAe;YACvD;QACF;QACA;IACF;AACF;;;IAtoBoB;;AAAA,iPAAA", "debugId": null}}, {"offset": {"line": 5622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/revo-2.0-service.ts"], "sourcesContent": ["/**\r\n * Revo 2.0 - Next-Generation AI Service\r\n * Revolutionary AI model with native image generation, character consistency, and intelligent editing\r\n */\r\n\r\nimport { GoogleGenerativeAI } from '@google/generative-ai';\r\nimport { BrandProfile } from '@/lib/types';\r\nimport {\r\n  ADVANCED_DESIGN_PRINCIPLES,\r\n  PLATFORM_SPECIFIC_GUIDELINES,\r\n  BUSINESS_TYPE_DESIGN_DNA,\r\n  QUALITY_ENHANCEMENT_INSTRUCTIONS\r\n} from '@/ai/prompts/advanced-design-prompts';\r\nimport {\r\n  analyzeDesignExample,\r\n  selectOptimalDesignExamples,\r\n  extractDesignDNA,\r\n  type DesignAnalysis\r\n} from '@/ai/utils/design-analysis';\r\nimport {\r\n  getCachedDesignTrends,\r\n  generateTrendInstructions,\r\n  type DesignTrends\r\n} from '@/ai/utils/design-trends';\r\n// Performance optimization will be handled inline\r\nimport { recordDesignGeneration } from '@/ai/utils/design-analytics';\r\nimport { generatePostFromProfile } from '@/ai/flows/generate-post-from-profile';\r\nimport { generateRevo2CaptionPrompt } from '@/ai/prompts/revo-2-caption-prompt';\r\nimport { generateRealTimeTrendingTopics } from '@/ai/utils/trending-topics';\r\n// import { fetchLocalContext } from '@/ai/utils/real-time-trends-integration';\r\n// import { selectRelevantContext, filterContextData } from '@/ai/utils/intelligent-context-selector';\r\nimport OpenAI from 'openai';\r\n\r\n// Get API keys (supporting both server-side and client-side)\r\nconst apiKey =\r\n  process.env.GEMINI_API_KEY ||\r\n  process.env.GOOGLE_API_KEY ||\r\n  process.env.GOOGLE_GENAI_API_KEY ||\r\n  process.env.NEXT_PUBLIC_GEMINI_API_KEY ||\r\n  process.env.NEXT_PUBLIC_GOOGLE_API_KEY ||\r\n  process.env.NEXT_PUBLIC_GOOGLE_GENAI_API_KEY;\r\n\r\nconst openaiApiKey =\r\n  process.env.OPENAI_API_KEY ||\r\n  process.env.NEXT_PUBLIC_OPENAI_API_KEY;\r\n\r\nif (!apiKey) {\r\n  console.error(\"❌ No Google AI API key found for Revo 2.0\");\r\n  console.error(\"Available env vars:\", {\r\n    server: {\r\n      GEMINI_API_KEY: !!process.env.GEMINI_API_KEY,\r\n      GOOGLE_API_KEY: !!process.env.GOOGLE_API_KEY,\r\n      GOOGLE_GENAI_API_KEY: !!process.env.GOOGLE_GENAI_API_KEY\r\n    },\r\n    client: {\r\n      NEXT_PUBLIC_GEMINI_API_KEY: !!process.env.NEXT_PUBLIC_GEMINI_API_KEY,\r\n      NEXT_PUBLIC_GOOGLE_API_KEY: !!process.env.NEXT_PUBLIC_GOOGLE_API_KEY,\r\n      NEXT_PUBLIC_GOOGLE_GENAI_API_KEY: !!process.env.NEXT_PUBLIC_GOOGLE_GENAI_API_KEY\r\n    }\r\n  });\r\n}\r\n\r\n// Initialize Google GenAI client (following official Node.js example)\r\nconst ai = new GoogleGenerativeAI(apiKey);\r\n\r\n// Initialize OpenAI client for creative ideation\r\nconst openai = openaiApiKey ? new OpenAI({ apiKey: openaiApiKey }) : null;\r\n\r\nif (!openaiApiKey) {\r\n  console.warn('⚠️ OpenAI API key not found. GPT creative ideation layer will be disabled.');\r\n}\r\n\r\n/**\r\n * Clean website URL by removing https://, http://, and www.\r\n */\r\nfunction cleanWebsiteUrl(url: string): string {\r\n  if (!url) return '';\r\n  return url\r\n    .replace(/^https?:\\/\\//, '')\r\n    .replace(/^www\\./, '')\r\n    .replace(/\\/$/, ''); // Remove trailing slash\r\n}\r\n\r\n// Types for GPT Creative Ideation\r\ninterface CreativeIdeas {\r\n  concept: string;\r\n  catchwords: string[];\r\n  visualDirection: string;\r\n  designElements: string[];\r\n  colorSuggestions: string[];\r\n  moodKeywords: string[];\r\n  targetEmotions: string[];\r\n}\r\n\r\ninterface CreativeIdeationInput {\r\n  businessType: string;\r\n  businessName: string;\r\n  location: string;\r\n  platform: string;\r\n  targetAudience: string;\r\n  brandVoice: string;\r\n  services: string[];\r\n  brandColors: string[];\r\n}\r\n\r\n// GPT Creative Ideation Function\r\nasync function generateCreativeIdeasWithGPT(input: CreativeIdeationInput): Promise<CreativeIdeas> {\r\n  if (!openai) {\r\n    // Fallback to basic creative ideas if OpenAI is not available\r\n    console.log('🔄 OpenAI not available, using fallback creative ideas...');\r\n    return {\r\n      concept: `Professional ${input.businessType.toLowerCase()} content showcasing ${input.businessName}'s expertise`,\r\n      catchwords: ['Professional', 'Quality', 'Excellence', 'Innovation', 'Trust'],\r\n      visualDirection: `Clean, modern design with professional aesthetics suitable for ${input.platform}`,\r\n      designElements: ['Clean typography', 'Professional imagery', 'Brand colors', 'Minimalist layout'],\r\n      colorSuggestions: input.brandColors.length > 0 ? input.brandColors : ['#2563eb', '#1f2937', '#f8fafc'],\r\n      moodKeywords: ['Professional', 'Trustworthy', 'Modern', 'Clean'],\r\n      targetEmotions: ['Trust', 'Confidence', 'Professionalism']\r\n    };\r\n  }\r\n\r\n  try {\r\n    console.log('🧠 Generating creative ideas with GPT-4...');\r\n\r\n    const prompt = `You are a local creative who understands what makes people in ${input.location} genuinely connect with businesses. Think like someone who lives in this community and wants to help local businesses succeed.\r\n\r\nBUSINESS CONTEXT:\r\n- Business: ${input.businessName}\r\n- Type: ${input.businessType}\r\n- Location: ${input.location}\r\n- Platform: ${input.platform}\r\n- Target Audience: ${input.targetAudience}\r\n- Brand Voice: ${input.brandVoice}\r\n- Services: ${Array.isArray(input.services) ? input.services.join(', ') : 'General services'}\r\n- Brand Colors: ${Array.isArray(input.brandColors) ? input.brandColors.join(', ') : 'Not specified'}\r\n\r\nCREATIVE BRIEF:\r\nGenerate authentic, relatable creative ideas that feel human and genuine - not corporate or overly polished. Think about what would make locals stop scrolling and think \"I want to support this business.\"\r\n\r\nREQUIREMENTS:\r\n1. Create a warm, relatable concept that tells a human story\r\n2. Generate 5 simple, authentic catchwords that feel conversational\r\n3. Suggest visual direction that feels real and community-focused\r\n4. Recommend design elements that are approachable, not intimidating\r\n5. Suggest colors that feel warm and welcoming\r\n6. Define mood keywords that are friendly and genuine\r\n7. Target emotions that build trust and community connection\r\n\r\nHUMAN-FIRST APPROACH:\r\n- Concepts should feel like a neighbor recommending a local business\r\n- Catchwords should be warm and relatable, not corporate buzzwords\r\n- Visual direction should feel authentic, not overly produced\r\n- Focus on building genuine connections, not just sales\r\n\r\nLANGUAGE SAFETY:\r\n- Only use local language words when 100% certain of accuracy\r\n- When in doubt about local language, use English instead\r\n- Better clear English than incorrect local language\r\n\r\nReturn your response in this exact JSON format:\r\n{\r\n  \"concept\": \"Relatable, human concept that locals would connect with (2-3 sentences, conversational tone)\",\r\n  \"catchwords\": [\"word1\", \"word2\", \"word3\", \"word4\", \"word5\"],\r\n  \"visualDirection\": \"Authentic visual direction that feels real and community-focused (2-3 sentences)\",\r\n  \"designElements\": [\"element1\", \"element2\", \"element3\", \"element4\"],\r\n  \"colorSuggestions\": [\"#color1\", \"#color2\", \"#color3\"],\r\n  \"moodKeywords\": [\"mood1\", \"mood2\", \"mood3\", \"mood4\"],\r\n  \"targetEmotions\": [\"emotion1\", \"emotion2\", \"emotion3\"]\r\n}`;\r\n\r\n    const response = await openai.chat.completions.create({\r\n      model: 'gpt-4o',\r\n      messages: [{ role: 'user', content: prompt }],\r\n      temperature: 0.8,\r\n      max_tokens: 1000\r\n    });\r\n\r\n    const content = response.choices[0]?.message?.content;\r\n    if (!content) {\r\n      throw new Error('No response from GPT');\r\n    }\r\n\r\n    // Parse JSON response (handle markdown formatting)\r\n    let jsonContent = content.trim();\r\n    if (jsonContent.startsWith('```json')) {\r\n      jsonContent = jsonContent.replace(/```json\\s*/, '').replace(/```\\s*$/, '');\r\n    }\r\n    const creativeIdeas = JSON.parse(jsonContent) as CreativeIdeas;\r\n\r\n    console.log('✅ GPT Creative Ideas Generated Successfully');\r\n    return creativeIdeas;\r\n\r\n  } catch (error) {\r\n    console.error('❌ GPT Creative Ideation failed:', error);\r\n    // Fallback to basic creative ideas\r\n    return {\r\n      concept: `Engaging ${input.businessType.toLowerCase()} content that showcases ${input.businessName}'s unique value`,\r\n      catchwords: ['Innovative', 'Excellence', 'Quality', 'Professional', 'Trusted'],\r\n      visualDirection: `Modern, eye-catching design with strong visual hierarchy for ${input.platform}`,\r\n      designElements: ['Bold typography', 'High-quality imagery', 'Brand integration', 'Clean composition'],\r\n      colorSuggestions: input.brandColors.length > 0 ? input.brandColors.slice(0, 3) : ['#3b82f6', '#1e293b', '#f1f5f9'],\r\n      moodKeywords: ['Professional', 'Innovative', 'Trustworthy', 'Modern'],\r\n      targetEmotions: ['Confidence', 'Trust', 'Excitement']\r\n    };\r\n  }\r\n}\r\n\r\n// Revo 2.0 uses Gemini 2.5 Flash Image model (following official docs)\r\nconst REVO_2_0_MODEL = 'gemini-2.5-flash-image-preview';\r\n\r\nexport interface Revo20GenerationInput {\r\n  businessType: string;\r\n  platform: string;\r\n  visualStyle: string;\r\n  imageText: string;\r\n  brandProfile: BrandProfile;\r\n  aspectRatio?: '1:1' | '16:9' | '9:16' | '4:3' | '3:4';\r\n  referenceImage?: string; // Base64 encoded reference image for character consistency\r\n  editingInstructions?: string; // For intelligent editing (inpainting/outpainting)\r\n  characterConsistency?: boolean; // Maintain character consistency\r\n  intelligentEditing?: boolean; // Enable intelligent editing features\r\n  includePeopleInDesigns?: boolean; // Control whether designs should include people (default: true)\r\n  useLocalLanguage?: boolean; // Control whether to use local language in text (default: false)\r\n}\r\n\r\nexport interface Revo20GenerationResult {\r\n  imageUrl: string;\r\n  model: string;\r\n  processingTime: number;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  caption: string;\r\n  hashtags: string[];\r\n  metadata: {\r\n    characterConsistency: boolean;\r\n    intelligentEditing: boolean;\r\n    aspectRatio: string;\r\n    textRendering: 'perfect' | 'good' | 'basic';\r\n  };\r\n}\r\n\r\n/**\r\n * Generate advanced captions and hashtags using enhanced Revo 2.0 system\r\n * Enhanced with better RSS integration, trending topics, and cultural awareness\r\n */\r\nasync function generateAdvancedCaptionAndHashtags(input: Revo20GenerationInput, creativeIdeas?: CreativeIdeas): Promise<{ caption: string; hashtags: string[] }> {\r\n  try {\r\n    console.log('🎯 Revo 2.0: Starting enhanced caption generation with RSS integration...');\r\n    console.log(`📍 Location: ${input.brandProfile.location || 'Not specified'}`);\r\n    console.log(`🏢 Business: ${input.businessType}`);\r\n    console.log(`📱 Platform: ${input.platform}`);\r\n\r\n    // Step 1: Fetch trending topics for context\r\n    console.log('🔍 Fetching trending topics for caption context...');\r\n    const trendingTopics = await generateRealTimeTrendingTopics(\r\n      input.businessType,\r\n      input.brandProfile.location || '',\r\n      input.platform\r\n    );\r\n    console.log(`✅ Found ${trendingTopics.length} trending topics for caption generation`);\r\n\r\n    // Step 2: Try enhanced Revo 2.0 caption generation first\r\n    try {\r\n      console.log('🚀 Using Revo 2.0 enhanced caption generation...');\r\n      const captionResult = await generateRevo2EnhancedCaption(input, trendingTopics, creativeIdeas);\r\n      if (captionResult.caption && captionResult.hashtags.length > 0) {\r\n        console.log('✅ Revo 2.0 enhanced caption generation successful!');\r\n        return captionResult;\r\n      }\r\n    } catch (error) {\r\n      console.warn('⚠️ Revo 2.0 enhanced caption failed, falling back to advanced system:', error);\r\n    }\r\n\r\n    // Step 3: Fallback to existing advanced system\r\n    console.log('🔄 Using existing advanced caption system as fallback...');\r\n\r\n    // Create generation parameters for the advanced system\r\n    const generationParams = {\r\n      // Required fields from schema\r\n      businessType: input.businessType,\r\n      location: input.brandProfile.location || '',\r\n      visualStyle: input.brandProfile.visualStyle || input.visualStyle || '',\r\n      writingTone: input.brandProfile.writingTone || '',\r\n      contentThemes: input.brandProfile.contentThemes || '',\r\n      logoDataUrl: input.brandProfile.logoDataUrl || '',\r\n\r\n      // Date fields\r\n      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n      currentDate: new Date().toLocaleDateString('en-US', {\r\n        year: 'numeric', month: 'long', day: 'numeric'\r\n      }),\r\n\r\n      // Platform variants\r\n      variants: [{\r\n        platform: input.platform,\r\n        aspectRatio: input.aspectRatio || '1:1',\r\n      }],\r\n\r\n      // Brand-specific fields\r\n      services: Array.isArray(input.brandProfile.services)\r\n        ? input.brandProfile.services.map(s => s.name || s).join(', ')\r\n        : (typeof input.brandProfile.services === 'string' ? input.brandProfile.services : ''),\r\n      targetAudience: input.brandProfile.targetAudience || '',\r\n      keyFeatures: input.brandProfile.keyFeatures || '',\r\n      competitiveAdvantages: input.brandProfile.competitiveAdvantages || '',\r\n\r\n      // Brand consistency\r\n      brandConsistency: {\r\n        strictConsistency: false,\r\n        followBrandColors: true\r\n      },\r\n\r\n      // Enhanced brand context\r\n      websiteUrl: input.brandProfile.websiteUrl || '',\r\n      description: input.brandProfile.description || '',\r\n      contactInfo: input.brandProfile.contactInfo || {},\r\n      socialMedia: input.brandProfile.socialMedia || {},\r\n\r\n      // Colors\r\n      primaryColor: input.brandProfile.primaryColor || '',\r\n      accentColor: input.brandProfile.accentColor || '',\r\n      backgroundColor: input.brandProfile.backgroundColor || '',\r\n\r\n      // Design examples\r\n      designExamples: input.brandProfile.designExamples || []\r\n    };\r\n\r\n    // Use the same advanced system as Revo 1.5/1.0 with enhanced logging\r\n    console.log('🚀 Calling generatePostFromProfile with RSS integration...');\r\n    const result = await generatePostFromProfile(generationParams);\r\n\r\n    console.log('✅ Advanced caption generation completed');\r\n    console.log(`📝 Generated caption length: ${result.content?.length || 0} characters`);\r\n    console.log(`🏷️ Generated hashtags: ${result.hashtags?.split(/[,\\s]+/).filter(tag => tag.startsWith('#')).length || 0}`);\r\n\r\n    // Extract caption and hashtags from the result (ensure exactly 10 hashtags)\r\n    const caption = result.content || `✨ Experience excellence with ${input.brandProfile.businessName || 'our brand'}! Quality you can trust.`;\r\n    let hashtags = result.hashtags ? result.hashtags.split(/[,\\s]+/).filter(tag => tag.startsWith('#')) : [];\r\n\r\n    // Ensure exactly 10 hashtags\r\n    if (hashtags.length < 10) {\r\n      const fallbackHashtags = ['#Quality', '#Professional', '#Excellence', '#Premium', '#Service', '#Business', '#Innovation', '#Success', '#Trusted', '#Experience'];\r\n      hashtags = [...hashtags, ...fallbackHashtags].slice(0, 10);\r\n      console.log(`⚠️ Used ${10 - (result.hashtags?.split(/[,\\s]+/).filter(tag => tag.startsWith('#')).length || 0)} fallback hashtags`);\r\n    } else {\r\n      hashtags = hashtags.slice(0, 10);\r\n    }\r\n\r\n    return { caption, hashtags };\r\n\r\n  } catch (error) {\r\n    console.error('❌ Advanced caption generation failed:', error);\r\n    console.log('🔄 Using enhanced fallback caption generation...');\r\n\r\n    // Enhanced fallback with AI-driven localization\r\n    const brandName = input.brandProfile.businessName || 'Our Brand';\r\n    const location = input.brandProfile.location || '';\r\n    const businessType = input.businessType;\r\n\r\n    // Create AI-driven contextual fallback caption\r\n    const fallbackCaption = `✨ Experience excellence with ${brandName}! Quality ${businessType.toLowerCase()} services you can trust. ${location ? `Proudly serving ${location}` : ''} 🌟`;\r\n    const fallbackHashtags = ['#Quality', '#Professional', '#Excellence', '#Business', '#Service', '#Trusted', '#Premium', '#Innovation', '#Success', '#Experience'];\r\n\r\n    return {\r\n      caption: fallbackCaption,\r\n      hashtags: fallbackHashtags\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Generate enhanced caption using Revo 2.0 system with trending topics\r\n */\r\nasync function generateRevo2EnhancedCaption(\r\n  input: Revo20GenerationInput,\r\n  trendingTopics: Array<{ topic: string; relevanceScore: number }>,\r\n  creativeIdeas?: CreativeIdeas\r\n): Promise<{ caption: string; hashtags: string[] }> {\r\n  try {\r\n    console.log('🎨 Generating Revo 2.0 enhanced caption...');\r\n\r\n    // Get API key\r\n    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY;\r\n    if (!apiKey) {\r\n      throw new Error('No Google AI API key found');\r\n    }\r\n\r\n    const genAI = new GoogleGenerativeAI(apiKey);\r\n    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });\r\n\r\n    // Generate enhanced caption prompt with GPT creative ideas and RSS data\r\n    const captionPrompt = generateHybridCaptionPrompt({\r\n      businessType: input.businessType,\r\n      location: input.brandProfile.location || '',\r\n      businessName: input.brandProfile.businessName || 'Our Brand',\r\n      platform: input.platform,\r\n      targetAudience: input.brandProfile.targetAudience,\r\n      trendingTopics: trendingTopics.slice(0, 5), // Top 5 trends\r\n      creativeIdeas: creativeIdeas,\r\n      useLocalLanguage: input.useLocalLanguage\r\n    });\r\n\r\n    console.log('📝 Sending enhanced caption generation request...');\r\n    const result = await model.generateContent(captionPrompt);\r\n    const response = await result.response;\r\n    const caption = response.text().trim();\r\n\r\n    // Generate hashtags based on the caption and context\r\n    const hashtagPrompt = `Based on this caption for a ${input.businessType} business in ${input.brandProfile.location}, generate exactly 10 relevant hashtags for ${input.platform}:\r\n\r\nCaption: \"${caption}\"\r\n\r\nBusiness: ${input.brandProfile.businessName || 'Business'}\r\nLocation: ${input.brandProfile.location}\r\nPlatform: ${input.platform}\r\n\r\nGenerate exactly 10 hashtags that are:\r\n- Mix of popular and niche hashtags\r\n- Platform-appropriate for ${input.platform}\r\n- Location-relevant for ${input.brandProfile.location}\r\n- Industry-specific for ${input.businessType}\r\n- Trending-aware\r\n\r\nFormat: #hashtag1 #hashtag2 #hashtag3 (etc.)`;\r\n\r\n    console.log('🏷️ Generating contextual hashtags...');\r\n    const hashtagResult = await model.generateContent(hashtagPrompt);\r\n    const hashtagResponse = await hashtagResult.response;\r\n    const hashtagText = hashtagResponse.text().trim();\r\n\r\n    // Extract hashtags\r\n    const hashtags = hashtagText.match(/#\\w+/g) || [];\r\n\r\n    // Ensure exactly 10 hashtags\r\n    const finalHashtags = hashtags.slice(0, 10);\r\n    if (finalHashtags.length < 10) {\r\n      const fallbackHashtags = ['#Quality', '#Professional', '#Excellence', '#Business', '#Service', '#Trusted', '#Premium', '#Innovation', '#Success', '#Experience'];\r\n      finalHashtags.push(...fallbackHashtags.slice(0, 10 - finalHashtags.length));\r\n    }\r\n\r\n    console.log(`✅ Generated enhanced caption (${caption.length} chars) and ${finalHashtags.length} hashtags`);\r\n\r\n    return {\r\n      caption,\r\n      hashtags: finalHashtags\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error('❌ Revo 2.0 enhanced caption generation failed:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content using Revo 2.0 (Gemini 2.5 Flash Image)\r\n */\r\nexport async function generateWithRevo20(\r\n  input: Revo20GenerationInput\r\n): Promise<Revo20GenerationResult> {\r\n  const startTime = Date.now();\r\n  console.log('🚀 Revo 2.0: Starting next-generation AI content creation...');\r\n  console.log('🚀 Using Next-Gen AI Engine');\r\n\r\n  try {\r\n    // 🧠 STEP 1: GPT Creative Ideation Layer\r\n    console.log('🧠 Revo 2.0: Starting GPT creative ideation layer...');\r\n\r\n    const creativeIdeas = await generateCreativeIdeasWithGPT({\r\n      businessType: input.brandProfile.businessType || 'Business',\r\n      businessName: input.brandProfile.businessName || 'Your Business',\r\n      location: input.brandProfile.location || 'Global',\r\n      platform: input.platform || 'instagram',\r\n      targetAudience: input.brandProfile.targetAudience || 'General audience',\r\n      brandVoice: input.brandProfile.brandVoice || 'Professional and engaging',\r\n      services: input.brandProfile.services || [],\r\n      brandColors: input.brandProfile.brandColors || []\r\n    });\r\n\r\n    console.log('✅ GPT Creative Ideas Generated:', {\r\n      concept: creativeIdeas.concept.substring(0, 100) + '...',\r\n      catchwords: creativeIdeas.catchwords,\r\n      visualDirection: creativeIdeas.visualDirection.substring(0, 100) + '...'\r\n    });\r\n\r\n    // 🎨 STEP 2: Build the revolutionary prompt for Revo 2.0 with GPT creative ideas\r\n    const { promptText, businessDNA, trendInstructions, contextData } = await buildRevo20Prompt(input, creativeIdeas);\r\n    console.log('📝 Revo 2.0 prompt:', promptText.substring(0, 200) + '...');\r\n\r\n    // Log context integration\r\n    if (contextData) {\r\n      console.log('🌍 Revo 2.0 Context Integration:');\r\n      if (contextData.trending && contextData.trending.length > 0) {\r\n        console.log(`   📈 Trending Topics: ${contextData.trending.length} topics integrated`);\r\n      }\r\n      if (contextData.local && Object.keys(contextData.local).length > 0) {\r\n        console.log(`   🏠 Local Context: ${Object.keys(contextData.local).join(', ')} data integrated`);\r\n      }\r\n    }\r\n\r\n    // Initialize enhancements array\r\n    const enhancementsApplied = [\r\n      'Revo 2.0 Next-Gen Engine',\r\n      'Advanced AI Generation',\r\n      'Native Image Generation',\r\n      'Perfect Text Rendering'\r\n    ];\r\n\r\n    // Prepare content array following official Node.js example\r\n    const prompt: any[] = [\r\n      { text: promptText }\r\n    ];\r\n\r\n    // Add brand logo if provided (CRITICAL for brand consistency)\r\n    if (input.brandProfile?.logoDataUrl) {\r\n      console.log('🎨 Adding brand logo for Revo 2.0 integration...');\r\n      const logoBase64Data = input.brandProfile.logoDataUrl.split(',')[1]; // Remove data:image/... prefix\r\n      const logoMimeType = input.brandProfile.logoDataUrl.split(';')[0].split(':')[1]; // Extract MIME type\r\n      prompt.push({\r\n        inlineData: {\r\n          mimeType: logoMimeType,\r\n          data: logoBase64Data\r\n        }\r\n      });\r\n      enhancementsApplied.push('Brand Logo Integration');\r\n    } else {\r\n      console.log('⚠️ No brand logo provided for Revo 2.0 generation');\r\n    }\r\n\r\n    // Add reference image for character consistency if provided (following official docs)\r\n    if (input.referenceImage && input.characterConsistency) {\r\n      console.log('👤 Adding reference image for character consistency...');\r\n      const base64Data = input.referenceImage.split(',')[1]; // Remove data:image/... prefix\r\n      prompt.push({\r\n        inlineData: {\r\n          mimeType: 'image/png',\r\n          data: base64Data\r\n        }\r\n      });\r\n    }\r\n\r\n    // Generate content with Revo 2.0 using official API with retry logic\r\n    console.log('🤖 Generating with Revo 2.0 revolutionary AI...');\r\n\r\n    let response;\r\n    let lastError;\r\n    const maxRetries = 3;\r\n\r\n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\r\n      try {\r\n        console.log(`🔄 Attempt ${attempt}/${maxRetries} for Revo 2.0 generation...`);\r\n        const model = ai.getGenerativeModel({ model: REVO_2_0_MODEL });\r\n        response = await model.generateContent(prompt);\r\n        console.log('✅ Revo 2.0 generation successful!');\r\n        break; // Success, exit retry loop\r\n      } catch (error: any) {\r\n        lastError = error;\r\n        console.log(`❌ Attempt ${attempt} failed:`, error?.message || error);\r\n\r\n        // If this is the last attempt, don't wait\r\n        if (attempt === maxRetries) {\r\n          break;\r\n        }\r\n\r\n        // Wait before retrying (exponential backoff)\r\n        const waitTime = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s\r\n        console.log(`⏳ Waiting ${waitTime / 1000}s before retry...`);\r\n        await new Promise(resolve => setTimeout(resolve, waitTime));\r\n      }\r\n    }\r\n\r\n    // If all retries failed, throw the last error\r\n    if (!response) {\r\n      console.error('❌ All retry attempts failed for Revo 2.0 generation');\r\n      throw lastError;\r\n    }\r\n\r\n    // Extract image and text content from response (following official Node.js example)\r\n    let imageUrl = '';\r\n    let textContent = '';\r\n\r\n    // Add feature-specific enhancements\r\n    if (input.characterConsistency) {\r\n      enhancementsApplied.push('Character Consistency');\r\n    }\r\n    if (input.intelligentEditing) {\r\n      enhancementsApplied.push('Intelligent Editing');\r\n    }\r\n\r\n    // Process response parts (following official Node.js example structure)\r\n    // The response might be nested under response.response\r\n    const actualResponse = response.response || response;\r\n    const parts = actualResponse.candidates?.[0]?.content?.parts || [];\r\n    console.log(`📊 Response contains ${parts.length} parts`);\r\n    console.log('🔍 Response candidates:', actualResponse.candidates?.length || 0);\r\n    console.log('🔍 First candidate:', actualResponse.candidates?.[0] ? 'exists' : 'missing');\r\n\r\n    for (const part of parts) {\r\n      if (part.text) {\r\n        console.log('📄 Revo 2.0 text response:', part.text.substring(0, 100) + '...');\r\n        textContent = part.text;\r\n      } else if (part.inlineData) {\r\n        console.log('🖼️ Revo 2.0 image generated successfully!');\r\n        console.log('📋 Image details:', {\r\n          mimeType: part.inlineData.mimeType,\r\n          dataLength: part.inlineData.data?.length || 0\r\n        });\r\n\r\n        // Create data URL (following official example)\r\n        imageUrl = `data:${part.inlineData.mimeType};base64,${part.inlineData.data}`;\r\n        enhancementsApplied.push(\r\n          'Ultra-High Quality Output',\r\n          'Perfect Brand Consistency',\r\n          'Platform Optimization',\r\n          'Multimodal Reasoning'\r\n        );\r\n        break;\r\n      }\r\n    }\r\n\r\n    if (!imageUrl) {\r\n      throw new Error('No image generated by Revo 2.0');\r\n    }\r\n\r\n    // Generate sophisticated captions and hashtags using GPT creative ideas and RSS data\r\n    const { caption, hashtags } = await generateAdvancedCaptionAndHashtags(input, creativeIdeas);\r\n\r\n    const processingTime = Date.now() - startTime;\r\n    console.log(`✅ Revo 2.0 generation completed in ${processingTime}ms`);\r\n\r\n    // Record design generation for analytics (Revo 2.0)\r\n    try {\r\n      const designId = `revo2_design_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n      recordDesignGeneration(\r\n        designId,\r\n        input.businessType,\r\n        input.platform,\r\n        input.visualStyle,\r\n        10, // Maximum quality score for Revo 2.0\r\n        {\r\n          colorPalette: input.brandProfile.primaryColor ? [input.brandProfile.primaryColor, input.brandProfile.accentColor, input.brandProfile.backgroundColor].filter(Boolean) : [],\r\n          typography: 'Revo 2.0 Premium Typography',\r\n          composition: input.aspectRatio || '1:1',\r\n          trends: ['revo-2.0-next-gen', 'ai-native-design'],\r\n          businessDNA: businessDNA.substring(0, 100) // Truncate for storage\r\n        },\r\n        {\r\n          engagement: 10,\r\n          brandAlignment: input.brandProfile.logoDataUrl ? 10 : 8,\r\n          technicalQuality: 10,\r\n          trendRelevance: trendInstructions ? 10 : 8\r\n        }\r\n      );\r\n    } catch (analyticsError) {\r\n      console.warn('Failed to record Revo 2.0 design analytics:', analyticsError);\r\n    }\r\n\r\n    return {\r\n      imageUrl,\r\n      model: 'Revo 2.0 (Next-Gen AI)',\r\n      processingTime,\r\n      qualityScore: 10, // Maximum quality for next-gen model\r\n      enhancementsApplied,\r\n      caption,\r\n      hashtags,\r\n      metadata: {\r\n        characterConsistency: input.characterConsistency || false,\r\n        intelligentEditing: input.intelligentEditing || false,\r\n        aspectRatio: input.aspectRatio || '1:1',\r\n        textRendering: 'perfect'\r\n      }\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error('❌ Revo 2.0 generation failed:', error);\r\n    throw new Error(`Revo 2.0 generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Build revolutionary prompt for Revo 2.0 with GPT creative ideas and real-time context\r\n */\r\nasync function buildRevo20Prompt(input: Revo20GenerationInput, creativeIdeas?: CreativeIdeas): Promise<{\r\n  promptText: string;\r\n  businessDNA: string;\r\n  trendInstructions: string;\r\n  contextData?: any;\r\n}> {\r\n  const { businessType, platform, visualStyle, imageText, brandProfile, aspectRatio = '1:1' } = input;\r\n\r\n  // Get platform-specific guidelines\r\n  const platformGuidelines = PLATFORM_SPECIFIC_GUIDELINES[platform as keyof typeof PLATFORM_SPECIFIC_GUIDELINES] || PLATFORM_SPECIFIC_GUIDELINES.instagram;\r\n\r\n  // Get business-specific design DNA\r\n  const businessDNA = BUSINESS_TYPE_DESIGN_DNA[businessType as keyof typeof BUSINESS_TYPE_DESIGN_DNA] || BUSINESS_TYPE_DESIGN_DNA.default;\r\n\r\n  // Step 1: Intelligent Context Analysis - Temporarily disabled for testing\r\n  // const contextRelevance = selectRelevantContext(\r\n  //   businessType,\r\n  //   brandProfile.location || '',\r\n  //   platform,\r\n  //   brandProfile.contentThemes || '',\r\n  //   new Date().getDay() // Current day of week\r\n  // );\r\n\r\n  console.log('🧠 Revo 2.0 Context Analysis for', businessType, 'in', brandProfile.location || 'Global');\r\n  console.log('   Note: Context analysis temporarily disabled for testing');\r\n\r\n  // Step 2-4: Context fetching temporarily disabled for testing\r\n  let trendingTopics: any[] = [];\r\n  let localContext: any = {};\r\n  const filteredContext: any = {\r\n    selectedTrends: [],\r\n    selectedWeather: null,\r\n    selectedEvents: [],\r\n    selectedCultural: null\r\n  };\r\n\r\n  console.log('🔍 Revo 2.0: Context integration temporarily disabled for testing');\r\n\r\n  // Get current design trends\r\n  let trendInstructions = '';\r\n  try {\r\n    const trends = await getCachedDesignTrends(\r\n      businessType,\r\n      platform,\r\n      brandProfile.targetAudience || '',\r\n      businessType\r\n    );\r\n    trendInstructions = generateTrendInstructions(trends, platform);\r\n  } catch (error) {\r\n    console.warn('Failed to get design trends for Revo 2.0, continuing without:', error);\r\n  }\r\n\r\n  // Get performance-optimized instructions (inline)\r\n  const performanceInstructions = `\r\n  **PERFORMANCE OPTIMIZATION (REVO 2.0):**\r\n  - Optimize for ${platform} platform specifications and user behavior\r\n  - Ensure fast loading and high engagement for ${businessType} audience\r\n  - Use ${visualStyle} aesthetic optimized for maximum visual impact\r\n  - Balance visual complexity with loading performance\r\n  - Prioritize mobile-first design for optimal user experience\r\n  `;\r\n\r\n  // Enhanced color instructions with psychology and usage guidelines\r\n  const colorInstructions = brandProfile.primaryColor ? `\r\n  **BRAND COLOR PALETTE (MANDATORY - REVO 2.0):**\r\n  - Primary Color: ${brandProfile.primaryColor} - Use for main elements, headers, and key focal points\r\n  - Accent Color: ${brandProfile.accentColor} - Use for highlights, buttons, and secondary elements\r\n  - Background Color: ${brandProfile.backgroundColor} - Use for backgrounds and neutral areas\r\n\r\n  **COLOR USAGE REQUIREMENTS:**\r\n  - Primary color should dominate the design (40-60% of color usage)\r\n  - Accent color for emphasis and call-to-action elements (20-30% of color usage)\r\n  - Background color for balance and readability (10-40% of color usage)\r\n  - Ensure high contrast ratios for text readability (minimum 4.5:1)\r\n  - Use color gradients and variations within the brand palette\r\n  - Avoid colors outside the brand palette unless absolutely necessary for contrast\r\n  ` : `\r\n  **COLOR GUIDANCE:**\r\n  - Brand colors available: Primary ${brandProfile.primaryColor}, Accent ${brandProfile.accentColor}, Background ${brandProfile.backgroundColor}\r\n  - Feel free to use complementary colors that work well with the brand palette\r\n  - Maintain visual harmony and professional appearance\r\n  `;\r\n\r\n  // Generate visual variation approach for diversity\r\n  const visualVariations = [\r\n    'minimalist_clean', 'bold_dynamic', 'elegant_sophisticated', 'playful_creative',\r\n    'modern_geometric', 'organic_natural', 'industrial_urban', 'artistic_abstract',\r\n    'photographic_realistic', 'illustrated_stylized', 'gradient_colorful', 'monochrome_accent',\r\n    'luxury_premium', 'tech_futuristic', 'warm_inviting', 'energetic_vibrant'\r\n  ];\r\n  const selectedVisualVariation = visualVariations[Math.floor(Math.random() * visualVariations.length)];\r\n  console.log(`🎨 Revo 2.0 Selected visual variation: ${selectedVisualVariation}`);\r\n\r\n  // Generate people/setting variety based on location and business type\r\n  const peopleSettingVariations = [\r\n    'professional_office', 'modern_lifestyle', 'community_gathering', 'creative_studio',\r\n    'retail_environment', 'outdoor_urban', 'cultural_celebration', 'tech_workspace',\r\n    'traditional_modern_blend', 'service_interaction', 'collaborative_space', 'premium_setting'\r\n  ];\r\n  const selectedPeopleSetting = peopleSettingVariations[Math.floor(Math.random() * peopleSettingVariations.length)];\r\n  console.log(`👥 Revo 2.0 Selected people setting: ${selectedPeopleSetting}`);\r\n\r\n  // Helper function to get content examples\r\n  const getContentExamples = (businessType: string, location?: string): string => {\r\n    const examples: Record<string, any> = {\r\n      'Food Production': {\r\n        headlines: ['Fresh From Farm to Table', 'Taste the Difference', 'Naturally Delicious', 'Crafted with Care', 'Pure Quality, Every Bite'],\r\n        subheadlines: ['Made with locally sourced ingredients', 'No preservatives, just pure goodness', 'Supporting local farmers since [year]', 'Your family deserves the best', 'Bringing nature to your kitchen'],\r\n        ctas: ['Order Fresh Today', 'Taste the Quality', 'Find in Stores', 'Try Our Selection', 'Visit Our Farm']\r\n      },\r\n      'Restaurant': {\r\n        headlines: ['Flavors That Tell Stories', 'Where Tradition Meets Taste', 'Authentic Cuisine Awaits', 'Made Fresh Daily', 'Your New Favorite Spot'],\r\n        subheadlines: ['Experience authentic flavors', 'Family recipes passed down generations', 'Fresh ingredients, bold flavors', 'Where every meal is special', 'Bringing people together through food'],\r\n        ctas: ['Book Your Table', 'Order for Delivery', 'View Our Menu', 'Make Reservation', 'Taste Today']\r\n      },\r\n      'Retail': {\r\n        headlines: ['Style That Speaks', 'Quality You Can Trust', 'Find Your Perfect Match', 'Curated Just for You', 'Discover Something Special'],\r\n        subheadlines: ['Handpicked for quality and style', 'Where fashion meets affordability', 'Your style, our passion', 'Quality that lasts', 'Trends that inspire'],\r\n        ctas: ['Shop the Collection', 'Explore New Arrivals', 'Find Your Style', 'Browse Catalog', 'Visit Our Store']\r\n      }\r\n    };\r\n\r\n    const businessExamples = examples[businessType] || examples['Retail'];\r\n    const locationNote = location ? `\\n- Adapt language and cultural references for ${location}` : '';\r\n\r\n    return `\r\n**Sample Headlines:** ${businessExamples.headlines.join(', ')}\r\n**Sample Sub-headlines:** ${businessExamples.subheadlines.join(', ')}\r\n**Sample CTAs:** ${businessExamples.ctas.join(', ')}${locationNote}\r\n**Note:** Use these as inspiration but create UNIQUE variations that fit the specific brand and context.`;\r\n  };\r\n\r\n  // Helper function to get industry-specific hashtags\r\n  const getIndustryHashtags = (businessType: string): string => {\r\n    const industryHashtags: Record<string, string[]> = {\r\n      'Food Production': ['#FoodProduction', '#FreshFood', '#LocalFarm'],\r\n      'Restaurant': ['#Restaurant', '#FoodLovers', '#Dining'],\r\n      'Retail': ['#Retail', '#Shopping', '#Fashion'],\r\n      'Technology': ['#Tech', '#Innovation', '#Digital'],\r\n      'Healthcare': ['#Healthcare', '#Wellness', '#Medical'],\r\n      'Education': ['#Education', '#Learning', '#Knowledge'],\r\n      'Fitness': ['#Fitness', '#Health', '#Workout'],\r\n      'Beauty': ['#Beauty', '#Skincare', '#Cosmetics'],\r\n      'Travel': ['#Travel', '#Adventure', '#Explore'],\r\n      'Real Estate': ['#RealEstate', '#Property', '#Homes']\r\n    };\r\n\r\n    const hashtags = industryHashtags[businessType] || ['#Business', '#Quality', '#Service'];\r\n    return hashtags.join(', ');\r\n  };\r\n\r\n  let prompt = `🎨 MASTER TEMPLATE PROMPT - REVO 2.0 ELITE DESIGN SYSTEM\r\n\r\n🚨 CRITICAL: NEVER USE GENERIC TEXT LIKE \"PREMIUM CONTENT\", \"QUALITY CONTENT\", OR \"[BUSINESS NAME] - [GENERIC PHRASE]\"\r\n🚨 EVERY DESIGN MUST BE COMPLETELY UNIQUE WITH SPECIFIC, BENEFIT-DRIVEN HEADLINES\r\n🚨 ABSOLUTELY NO WATERMARKS: Do not include any watermarks, \"Premium Content\", \"Paya\", or any overlay text that looks like watermarks\r\n\r\nYou are a world-class graphic designer creating scroll-stopping, modern social media content for ${businessType}.\r\nCreate a ${aspectRatio} design for ${platform} that people will absolutely LOVE and share.\r\n\r\n**🌟 MODERN DESIGN EXCELLENCE REQUIREMENTS:**\r\n- Create designs that feel CURRENT, FRESH, and ON-TREND for 2024-2025\r\n- Use contemporary visual language that resonates with modern audiences\r\n- Incorporate cutting-edge design aesthetics that feel premium and professional\r\n- Ensure the design would fit perfectly in a top-tier design portfolio\r\n- Make it so visually appealing that people stop scrolling immediately\r\n\r\n**🎯 BUSINESS CONTEXT:**\r\n- Business: ${brandProfile.businessName || businessType}\r\n- Type: ${businessType}\r\n- Platform: ${platform} (${aspectRatio} aspect ratio)\r\n- Location: ${brandProfile.location || 'Global'}\r\n- Target Audience: ${brandProfile.targetAudience || 'General audience'}\r\n${brandProfile.websiteUrl ? `- Website: ${brandProfile.websiteUrl}` : ''}\r\n\r\n**🎨 CREATIVE DIRECTION FOR THIS DESIGN:**\r\n- Visual Style: ${selectedVisualVariation.replace('_', ' ')} approach\r\n${input.includePeopleInDesigns === true ? `- People Setting: ${selectedPeopleSetting.replace('_', ' ')} context` : '- Focus: Product/service showcase without people'}\r\n\r\n${creativeIdeas ? `**🧠 GPT CREATIVE DIRECTION (FOLLOW EXACTLY):**\r\n- **Creative Concept:** ${creativeIdeas.concept}\r\n- **Key Catchwords:** ${creativeIdeas.catchwords.join(', ')}\r\n- **Visual Direction:** ${creativeIdeas.visualDirection}\r\n- **Design Elements:** ${creativeIdeas.designElements.join(', ')}\r\n- **Mood Keywords:** ${creativeIdeas.moodKeywords.join(', ')}\r\n- **Target Emotions:** ${creativeIdeas.targetEmotions.join(', ')}\r\n- **Color Suggestions:** ${creativeIdeas.colorSuggestions.join(', ')}\r\n\r\n🚨 CRITICAL: Use these GPT-generated creative ideas as your PRIMARY creative direction. This is the core concept that should drive your entire design.` : ''}\r\n\r\n**🌍 CULTURAL INTEGRATION & HUMAN ELEMENTS:**\r\n- Location Context: ${brandProfile.location || 'Global'}\r\n${input.includePeopleInDesigns === true ? `- CREATIVE VARIETY: Include diverse, authentic people in VARIED settings and contexts:\r\n  * Professional office environments (modern, clean, business-focused)\r\n  * Lifestyle settings (homes, cafes, outdoor spaces, community centers)\r\n  * Industry-specific environments (workshops, studios, retail spaces, service areas)\r\n  * Cultural celebrations and community gatherings\r\n  * Modern urban settings (co-working spaces, tech hubs, creative studios)\r\n  * Traditional meets modern (blend of cultural heritage with contemporary life)\r\n- Show real people in natural, engaging situations that vary by design\r\n- Ensure representation reflects the local demographic and cultural values\r\n- Use photography styles that range from candid to professional to artistic\r\n- Vary the mood: energetic, calm, celebratory, focused, collaborative` : `- FOCUS ON PRODUCTS/SERVICES: Emphasize products, services, and brand elements WITHOUT people\r\n- Use lifestyle imagery, product showcases, and brand-focused visuals\r\n- Create compelling designs through typography, graphics, and product photography\r\n- Maintain professional aesthetic through clean, modern design elements\r\n- NO PEOPLE in the design - focus purely on products, services, and brand elements`}\r\n- Respect and celebrate the local culture and aesthetic preferences of ${brandProfile.location || 'the target region'}\r\n- Use culturally appropriate imagery, colors, and design elements\r\n- Incorporate subtle cultural motifs or design elements that resonate locally\r\n\r\n**🚨 LANGUAGE INSTRUCTIONS FOR TEXT IN DESIGNS:**\r\n${input.useLocalLanguage === true ? `- You may use local language text when 100% certain of spelling, meaning, and cultural appropriateness\r\n- Mix local language with English naturally (1-2 local words maximum per text element)\r\n- Only use commonly known local words that add cultural connection\r\n- When uncertain about local language accuracy, use English instead\r\n- Better to use clear English than incorrect or garbled local language` : `- USE ONLY ENGLISH for all text in the design\r\n- Do not use any local language words or phrases\r\n- Keep all headlines, subheadlines, and call-to-actions in clear English\r\n- Focus on universal messaging that works across all markets\r\n- Maintain professional English-only communication`}\r\n\r\n**🎯 BRAND IDENTITY SYSTEM:**\r\n${colorInstructions}\r\n\r\n${brandProfile.logoDataUrl ? `\r\n🚨 CRITICAL LOGO INTEGRATION (REVO 2.0):\r\n- MANDATORY: Use the uploaded brand logo provided in the image inputs\r\n- DO NOT create, generate, or design a new logo - use ONLY the provided logo\r\n- The uploaded logo is the official brand logo and must be used exactly as provided\r\n- Integrate the logo naturally and prominently into the design (minimum 10% of design area)\r\n- Maintain logo's original proportions and readability - do not distort the logo\r\n- Position logo strategically for maximum brand recognition and visibility\r\n- Ensure sufficient contrast between logo and background for perfect readability\r\n- Logo should be one of the first elements viewers notice in the design\r\n` : ''}\r\n\r\n**📐 DYNAMIC CONTENT HIERARCHY SYSTEM:**\r\nYou are an experienced marketing expert with deep knowledge of ${businessType} industry in ${brandProfile.location || 'the target region'}.\r\nCreate compelling, culturally-aware content that resonates with local customers:\r\n\r\n**HEADLINE CREATION (PRIMARY) - STRATEGIC BUSINESS SELLING:**\r\n- NEVER use generic phrases like \"Premium Content\", \"Quality Content\", or \"[Business Name] - [Generic Text]\"\r\n- Create headlines that SELL the business value and relate directly to the caption content\r\n- Use specific benefit-driven language that answers \"Why should I choose this business?\"\r\n- Make it industry-specific and customer-focused, highlighting unique selling points\r\n- Keep it short (3-7 words) but pack in real business value\r\n- Must connect to the caption story and reinforce the main business message\r\n${input.useLocalLanguage === true ? `- PRIMARILY USE ENGLISH (60%+ of the time) - only use local language when extremely confident and impactful\r\n- Examples: \"Same Day Delivery\", \"24/7 Expert Support\", \"Karibu Nyumbani\", \"Local Since 1995\"` : `- USE ONLY ENGLISH for all headlines and text elements\r\n- Examples: \"Same Day Delivery\", \"24/7 Expert Support\", \"No Hidden Fees\", \"Local Since 1995\"`}\r\n\r\n${filteredContext.selectedTrends && filteredContext.selectedTrends.length > 0 ? `\r\n**🔥 TRENDING TOPICS INTEGRATION:**\r\nUse these current trending topics to make headlines more relevant and engaging:\r\n${filteredContext.selectedTrends.slice(0, 5).map((trend: any) => `- ${trend.title || trend.topic}: ${trend.description || trend.summary || ''}`).join('\\n')}\r\n- Subtly incorporate trending themes into headlines when contextually appropriate\r\n- Don't force trends - only use if they naturally fit the business message\r\n` : ''}\r\n\r\n${filteredContext.selectedWeather || (filteredContext.selectedEvents && filteredContext.selectedEvents.length > 0) ? `\r\n**🌍 LOCAL CONTEXT INTEGRATION:**\r\n${filteredContext.selectedWeather ? `- Current Weather: ${filteredContext.selectedWeather.condition || ''} ${filteredContext.selectedWeather.temperature || ''}° - Consider weather-relevant messaging when appropriate` : ''}\r\n${filteredContext.selectedEvents && filteredContext.selectedEvents.length > 0 ? `- Local Events: ${filteredContext.selectedEvents.slice(0, 2).map((event: any) => event.title || event.name).join(', ')} - Reference local happenings if relevant` : ''}\r\n- Use local insights to create more personally relevant headlines\r\n- Incorporate regional preferences and cultural nuances\r\n` : ''}\r\n\r\n**SUB-HEADLINE CREATION (SECONDARY) - BUSINESS VALUE REINFORCEMENT:**\r\n- Develop a supporting message that reinforces the main headline's business benefit\r\n- Must directly relate to and support the caption content and main headline\r\n- Address the specific problem your business solves or benefit you provide\r\n- Make it relevant to ${businessType} industry and what customers actually want\r\n- Keep it concise (8-15 words) but pack in concrete business value\r\n- PREFER ENGLISH (60%+ of the time) - only use 1-2 local words when absolutely confident and punchy\r\n${filteredContext.selectedTrends && filteredContext.selectedTrends.length > 0 ? `- Consider incorporating trending themes only if they strengthen your business message` : ''}\r\n${filteredContext.selectedWeather ? `- Reference current conditions (${filteredContext.selectedWeather.condition || ''}) only if relevant to your business value` : ''}\r\n- Create subheadlines that make customers think \"I need this business\"\r\n\r\n**CALL-TO-ACTION CREATION (TERTIARY):**\r\n- Generate contextually relevant CTAs based on the message\r\n- Include contact information ONLY when it makes contextual sense (e.g., \"Book online\", \"Visit us\", \"Get quote\")\r\n- Use ${brandProfile.websiteUrl ? `website: ${cleanWebsiteUrl(brandProfile.websiteUrl)}` : 'appropriate web reference'} SPARINGLY - only when CTA specifically mentions visiting website\r\n- Use ${brandProfile.contactInfo?.phone ? `phone: ${brandProfile.contactInfo.phone}` : 'contact information'} sparingly and contextually\r\n- Vary between action words: \"Discover\", \"Try Now\", \"Get Started\", \"Learn More\", \"Order Today\", etc.\r\n- STICK TO ENGLISH for CTAs (90%+ of the time) - only use local language when it's a perfect, punchy fit\r\n\r\n**🎨 CREATIVE DESIGN VARIETY & COMPOSITION:**\r\n- **DESIGN STYLES** (rotate for variety):\r\n  * Ultra-modern minimalist with bold typography and negative space\r\n  * Dynamic geometric patterns with vibrant brand colors\r\n  * Sophisticated gradient overlays with premium feel\r\n  * Clean photography-focused with subtle text overlays\r\n  * Artistic illustration style with contemporary elements\r\n  * Bold graphic design with strong visual hierarchy\r\n  * Elegant luxury aesthetic with refined typography\r\n  * Energetic and vibrant with dynamic compositions\r\n\r\n- **LAYOUT VARIATIONS** (mix these approaches):\r\n  * Split-screen compositions (text left, visual right)\r\n  * Centered hero with surrounding elements\r\n  * Asymmetrical modern layouts with visual balance\r\n  * Grid-based structured designs\r\n  * Organic flowing compositions\r\n  * Layered depth with foreground/background elements\r\n\r\n- **VISUAL ELEMENTS** (choose contextually):\r\n  * Hero Element: Product showcase, lifestyle photography, or thematic illustration\r\n  * Background: Modern gradients, textured patterns, or photographic backgrounds\r\n  * Accent Graphics: Contemporary icons, geometric shapes, or cultural elements\r\n  * Typography: Modern, readable fonts with strong hierarchy\r\n  * Safe Zones: Ensure text avoids bottom 15% for platform UI compatibility\r\n  * Accessibility: Maintain high contrast ratios and mobile readability\r\n\r\n**🎯 BRAND & MARKET INTELLIGENCE:**\r\n- Business: ${brandProfile.businessName || businessType}\r\n- Industry: ${businessType}\r\n- Location: ${brandProfile.location || 'Global'}\r\n- Colors: ${brandProfile.primaryColor ? `Primary: ${brandProfile.primaryColor}` : 'Use brand-appropriate colors'}${brandProfile.secondaryColor ? `, Secondary: ${brandProfile.secondaryColor}` : ''}\r\n- Tone: ${brandProfile.brandPersonality || 'Professional yet approachable'}\r\n\r\n**🧠 MARKETING EXPERT PERSONA:**\r\nYou are a seasoned marketing professional with 15+ years of experience in ${businessType} industry.\r\nYou understand:\r\n- Local market dynamics in ${brandProfile.location || 'the target region'}\r\n- Cultural nuances and communication preferences\r\n- Industry-specific customer pain points and desires\r\n- Seasonal trends and buying patterns\r\n- Competitive landscape and differentiation strategies\r\n- Local language patterns ONLY when 100% certain of accuracy (prefer English over incorrect local language)\r\n\r\n**💡 CONTENT VARIATION STRATEGY:**\r\nGenerate UNIQUE content every time by:\r\n- Rotating between different value propositions\r\n- Using varied emotional triggers (trust, excitement, urgency, curiosity)\r\n- Incorporating different benefit angles (quality, convenience, price, experience)\r\n- Adapting to cultural context and local preferences\r\n- Using industry-specific terminology and insights\r\n\r\n**🏢 BUSINESS DNA INTEGRATION:**\r\n${businessDNA}\r\n\r\n**📱 PLATFORM OPTIMIZATION:**\r\n${platformGuidelines.designGuidelines || `Optimize for ${platform} best practices with mobile-first approach`}\r\n\r\n**✨ ADVANCED DESIGN PRINCIPLES:**\r\n${ADVANCED_DESIGN_PRINCIPLES}\r\n\r\n**🔥 QUALITY ENHANCEMENT:**\r\n${QUALITY_ENHANCEMENT_INSTRUCTIONS}\r\n\r\n${trendInstructions ? `**📈 CURRENT DESIGN TRENDS:**\\n${trendInstructions}` : ''}\r\n\r\n${performanceInstructions ? `**⚡ PERFORMANCE OPTIMIZATION:**\\n${performanceInstructions}` : ''}\r\n\r\n**🎭 VISUAL APPROACH:** ${selectedVisualVariation} (MANDATORY: Use this specific visual style approach)\r\n\r\n**🚀 UNIQUENESS MANDATE (REVO 2.0):**\r\nThis design MUST be completely unique. Vary these elements:\r\n- Layout: ${['Grid-based', 'Asymmetrical', 'Centered', 'Diagonal', 'Circular', 'Organic flow'][Math.floor(Math.random() * 6)]}\r\n- Color Dominance: ${['Primary-heavy', 'Accent-heavy', 'Balanced palette'][Math.floor(Math.random() * 3)]}\r\n- Typography Style: ${['Bold headlines', 'Elegant serif', 'Modern sans-serif', 'Creative display'][Math.floor(Math.random() * 4)]}\r\n- Visual Elements: ${['Abstract shapes', 'Geometric patterns', 'Organic forms', 'Photographic blend'][Math.floor(Math.random() * 4)]}\r\n- Background Treatment: ${['Gradient blend', 'Textured overlay', 'Photographic base', 'Solid with accents'][Math.floor(Math.random() * 4)]}\r\n\r\n**💫 ENGAGEMENT FACTORS:**\r\n- Make it scroll-stopping and share-worthy\r\n- Ensure clear value proposition for viewers\r\n- Match current aesthetic preferences of the target audience\r\n- Create emotional connection through visual storytelling\r\n- Use contemporary color palettes and design trends\r\n\r\n**📝 TEXT REQUIREMENTS (PERFECT RENDERING):**\r\n\"${imageText}\"\r\n- Render this text with PERFECT clarity and readability\r\n- Use premium typography with excellent contrast\r\n- Ensure text is prominent and professionally integrated\r\n- NO additional text, placeholders, or random words\r\n\r\n🚨 **CRITICAL: NO TECHNICAL CODES OR IDs IN DESIGN:**\r\n- DO NOT include hex color codes (like #10BA5C, #5B82F6) anywhere in the design\r\n- DO NOT show generation IDs, technical identifiers, or system codes\r\n- DO NOT display any technical information, debugging text, or metadata\r\n- Keep the design clean and professional without any technical elements\r\n- Only show the intended business content and branding\r\n\r\n🚨 **ABSOLUTELY NO WATERMARKS OR OVERLAY TEXT:**\r\n- DO NOT add any watermarks like \"Paya - Premium Content\" or similar\r\n- DO NOT include any semi-transparent overlay text across the image\r\n- DO NOT add any branding watermarks, service names, or platform identifiers\r\n- DO NOT include any repeated text patterns or watermark-style overlays\r\n- Keep the image completely clean without any watermark elements\r\n- The only text should be the intended business content specified above\r\n\r\n**🎯 REVO 2.0 EXCELLENCE STANDARDS:**\r\n- Ultra-high quality, next-generation aesthetics\r\n- Perfect for ${platform} social media platform\r\n- Brand colors prominently and tastefully featured\r\n- Clean, modern layout with perfect spacing\r\n- ${visualStyle} aesthetic with revolutionary design elements\r\n- Optimized for mobile and desktop viewing\r\n- Professional typography with crystal-clear readability\r\n- Perfect brand consistency and visual harmony\r\n${brandProfile.logoDataUrl ? '- Logo integration is the TOP PRIORITY for brand recognition' : ''}\r\n\r\n**DESIGN UNIQUENESS:** Ensure this design is completely unique and different from any previous generation.`;\r\n\r\n  // Add character consistency instructions\r\n  if (input.characterConsistency && input.referenceImage) {\r\n    prompt += `\\n\\nCHARACTER CONSISTENCY:\r\n- Maintain the same character/subject appearance as shown in the reference image\r\n- Keep consistent facial features, clothing style, and overall appearance\r\n- Adapt the character to the new scene while preserving identity`;\r\n  }\r\n\r\n  // Add intelligent editing instructions\r\n  if (input.intelligentEditing && input.editingInstructions) {\r\n    prompt += `\\n\\nINTELLIGENT EDITING:\r\n- ${input.editingInstructions}\r\n- Perform precise, context-aware modifications\r\n- Maintain overall composition and quality`;\r\n  }\r\n\r\n  prompt += `\\n\\n🎨 CREATE A MASTERPIECE:\r\nCreate a stunning, masterpiece-quality design that represents the pinnacle of ${businessType} visual communication.\r\nMake it so visually appealing and modern that people will love, engage with, and remember this content.\r\nThis should be a design that stops scrolling, drives engagement, and showcases the absolute best of contemporary design trends.\r\n\r\n**FINAL REQUIREMENTS:**\r\n- Make it scroll-stopping and share-worthy\r\n- Ensure it feels current and on-trend for 2024-2025\r\n- Include human elements and cultural sensitivity when appropriate\r\n- Perfect text rendering with excellent hierarchy\r\n- Professional quality that matches top-tier design portfolios\r\n- Optimized for ${platform} platform specifications\r\n\r\n🚨 **ABSOLUTELY NO TECHNICAL ELEMENTS:**\r\n- NO hex codes, color codes, or technical identifiers visible in the design\r\n- NO generation IDs, system codes, or debugging information\r\n- Keep the design completely clean and professional\r\n- Only show business content, branding, and intended messaging\r\n\r\n**📝 CONTENT CREATION EXAMPLES FOR ${businessType}:**\r\n${getContentExamples(businessType, brandProfile.location)}\r\n\r\n**🎨 STRATEGIC HEADLINE TECHNIQUES:**\r\n❌ NEVER USE: \"Premium Content\", \"Quality Content\", \"[Business] - Premium\", \"[Business] - Quality\"\r\n✅ ALWAYS USE BUSINESS-SELLING HEADLINES THAT RELATE TO CAPTION:\r\n- Problem-Solution: \"No More Late Deliveries\" (if caption mentions delivery)\r\n- Benefit-Focused: \"Save 50% on Repairs\" (if caption talks about cost savings)\r\n- Unique Value: \"Only Local Organic Farm\" (if caption mentions local sourcing)\r\n- Convenience: \"Open 24/7\" (if caption mentions availability)\r\n- Quality Proof: \"5-Star Rated Service\" (if caption mentions customer satisfaction)\r\n- Urgency-Based: \"Limited time offer\" or \"While supplies last\"\r\n- Trust-Building: \"Trusted by thousands\" or \"Family-owned since X\"\r\n- Local Pride: \"Proudly serving ${brandProfile.location || 'our community'}\"\r\n- Specific Features: \"Baked Fresh Daily\", \"Made with Local Ingredients\", \"Handcrafted Since [Year]\"\r\n\r\n**💬 CTA INTELLIGENCE:**\r\nSmart contact integration rules:\r\n- Include website when saying \"Learn More\", \"Visit Us\", \"Check Out\"\r\n- Include phone when saying \"Call Now\", \"Book Today\", \"Get Quote\"\r\n- Use generic CTAs like \"Discover\", \"Try Now\" without contact info\r\n- Rotate between different action words for variety\r\n- Make culturally appropriate for ${brandProfile.location || 'the region'}\r\n\r\n**📝 CAPTION & HASHTAG SYSTEM:**\r\nGenerate engaging social media content with:\r\n\r\n**CAPTION CREATION:**\r\n- Write a compelling 2-3 sentence caption that tells a story\r\n- Use conversational tone that matches ${brandProfile.brandPersonality || 'the brand personality'}\r\n- Include emotional hooks and value propositions\r\n- Make it culturally relevant for ${brandProfile.location || 'the target audience'}\r\n- Incorporate industry insights and customer benefits\r\n- Use emojis strategically (2-4 per caption)\r\n- End with a clear call-to-action\r\n\r\n**HASHTAG STRATEGY (Maximum 10 hashtags):**\r\nCreate a strategic mix of:\r\n- 2-3 Brand/Business hashtags: #${brandProfile.businessName?.replace(/\\s+/g, '') || 'YourBrand'}, #${businessType.replace(/\\s+/g, '')}\r\n- 2-3 Industry-specific hashtags: ${getIndustryHashtags(businessType)}\r\n- 2-3 Location-based hashtags: ${brandProfile.location ? `#${brandProfile.location.replace(/\\s+/g, '')}, #Local${brandProfile.location.replace(/\\s+/g, '')}` : '#Local, #Community'}\r\n- 1-2 Trending/Popular hashtags: #Quality, #Fresh, #Authentic, #Handmade\r\n- 1-2 Platform-specific hashtags: #${platform}Ready, #SocialMedia\r\n\r\n**HASHTAG RULES:**\r\n- Maximum 10 hashtags total\r\n- Mix of popular and niche hashtags\r\n- Relevant to content and industry\r\n- Include location-based tags when applicable\r\n- Use trending hashtags when contextually appropriate\r\n\r\n🚨 **FINAL CONTENT REQUIREMENTS:**\r\n- HEADLINE must directly relate to caption content and sell real business value\r\n- SUB-HEADLINE must reinforce the headline and caption message (NOT generic descriptions)\r\n- Headlines should make customers think \"I want this\" or \"I need this business\"\r\n- Think like a strategic marketer who connects every element to drive business results\r\n- Every word should serve the purpose of attracting and converting customers\r\n\r\n🚨 **LANGUAGE USAGE RULES:**\r\n**FOR OVERLAY TEXT (Headlines, Subheadlines, CTAs):**\r\n- Use ENGLISH 60-90% of the time to avoid language mistakes\r\n- Only use local language when you're 100% confident it's accurate and punchy\r\n- Better clear English than incorrect local language\r\n\r\n**FOR CAPTIONS:**\r\n- Can freely use local languages and cultural references\r\n- Mix local terms naturally with English as appropriate\r\n- Focus on authentic, conversational tone\r\n\r\n**📱 FINAL DELIVERABLE:**\r\nCreate a complete social media package including:\r\n1. **Visual Design**: The scroll-stopping image with UNIQUE, SPECIFIC headlines\r\n2. **Caption**: Engaging 2-3 sentence story with emojis and CTA\r\n3. **Hashtags**: Strategic mix of 10 hashtags maximum for optimal reach\r\n\r\nGenerate a revolutionary, next-generation design with Revo 2.0 excellence standards.`;\r\n\r\n  return {\r\n    promptText: prompt,\r\n    businessDNA,\r\n    trendInstructions,\r\n    contextData: filteredContext\r\n  };\r\n}\r\n\r\n/**\r\n * Test Revo 2.0 availability following official Google AI docs\r\n */\r\nexport async function testRevo20Availability(): Promise<boolean> {\r\n  try {\r\n    console.log('🧪 Testing Revo 2.0 (Next-Gen AI) availability...');\r\n    console.log('📋 Using official AI package...');\r\n\r\n    // Test using official API structure (following Node.js example)\r\n    const response = await ai.models.generateContent({\r\n      model: REVO_2_0_MODEL,\r\n      contents: [\r\n        { text: 'Create a simple test image with the text \"Revo 2.0 Test\" on a modern gradient background' }\r\n      ]\r\n    });\r\n\r\n    // Check response structure following the docs\r\n    console.log('📄 Response structure:', {\r\n      candidates: response.candidates?.length || 0,\r\n      parts: response.candidates?.[0]?.content?.parts?.length || 0\r\n    });\r\n\r\n    // Look for image data in parts (following Python docs pattern)\r\n    const parts = response.candidates?.[0]?.content?.parts || [];\r\n    let hasImage = false;\r\n    let hasText = false;\r\n\r\n    for (const part of parts) {\r\n      if (part.text) {\r\n        console.log('📝 Text response found:', part.text.substring(0, 100) + '...');\r\n        hasText = true;\r\n      }\r\n      if (part.inlineData) {\r\n        console.log('🖼️ Image data found:', part.inlineData.mimeType);\r\n        hasImage = true;\r\n      }\r\n    }\r\n\r\n    if (hasImage) {\r\n      console.log('✅ Revo 2.0 is available and working perfectly!');\r\n      return true;\r\n    } else if (hasText) {\r\n      console.log('⚠️ Revo 2.0 responded with text but no image - model may not support image generation yet');\r\n      return false;\r\n    } else {\r\n      console.log('⚠️ Revo 2.0 responded but no content found');\r\n      return false;\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error('❌ Revo 2.0 test failed:', error);\r\n    console.error('Error details:', {\r\n      name: error instanceof Error ? error.name : 'Unknown',\r\n      message: error instanceof Error ? error.message : 'Unknown error'\r\n    });\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Character consistency generation\r\n */\r\nexport async function generateWithCharacterConsistency(\r\n  input: Revo20GenerationInput,\r\n  referenceImages: string[]\r\n): Promise<Revo20GenerationResult> {\r\n  console.log('👤 Revo 2.0: Generating with character consistency...');\r\n\r\n  const enhancedInput = {\r\n    ...input,\r\n    referenceImage: referenceImages[0],\r\n    characterConsistency: true,\r\n    editingInstructions: `Maintain the same character/subject appearance as shown in the reference image while creating: ${input.imageText}`\r\n  };\r\n\r\n  return generateWithRevo20(enhancedInput);\r\n}\r\n\r\n/**\r\n * Intelligent editing (inpainting/outpainting)\r\n */\r\nexport async function performIntelligentEditing(\r\n  baseImage: string,\r\n  editingPrompt: string,\r\n  brandProfile: BrandProfile\r\n): Promise<Revo20GenerationResult> {\r\n  console.log('✏️ Revo 2.0: Performing intelligent editing...');\r\n\r\n  const input: Revo20GenerationInput = {\r\n    businessType: brandProfile.businessType || 'Business',\r\n    platform: 'instagram',\r\n    visualStyle: brandProfile.visualStyle || 'modern',\r\n    imageText: editingPrompt,\r\n    brandProfile,\r\n    referenceImage: baseImage,\r\n    intelligentEditing: true,\r\n    editingInstructions: `Edit the provided image: ${editingPrompt}. Maintain overall composition while making precise modifications.`\r\n  };\r\n\r\n  return generateWithRevo20(input);\r\n}\r\n\r\n/**\r\n * Generate hybrid caption prompt that combines GPT creative ideas with RSS trending topics\r\n */\r\nfunction generateHybridCaptionPrompt(context: {\r\n  businessType: string;\r\n  location: string;\r\n  businessName: string;\r\n  platform: string;\r\n  targetAudience?: string;\r\n  trendingTopics?: Array<{ topic: string; relevanceScore: number }>;\r\n  creativeIdeas?: CreativeIdeas;\r\n  useLocalLanguage?: boolean;\r\n}): string {\r\n  const randomSeed = Math.random().toString(36).substring(7);\r\n\r\n  let prompt = `You are a LOCAL HUMAN social media manager who lives and works in ${context.location}. Write like a real person from this community - warm, authentic, and relatable.\r\n\r\n🎯 BUSINESS CONTEXT:\r\n- Business: ${context.businessName}\r\n- Type: ${context.businessType}\r\n- Location: ${context.location}\r\n- Platform: ${context.platform}\r\n- Target Audience: ${context.targetAudience || 'General audience'}\r\n\r\n${context.creativeIdeas ? `🧠 CREATIVE INSPIRATION:\r\n- **Core Concept**: ${context.creativeIdeas.concept}\r\n- **Key Catchwords**: ${context.creativeIdeas.catchwords.join(', ')}\r\n- **Target Emotions**: ${context.creativeIdeas.targetEmotions.join(', ')}\r\n- **Mood**: ${context.creativeIdeas.moodKeywords.join(', ')}` : ''}\r\n\r\n${context.trendingTopics && context.trendingTopics.length > 0 ? `📈 WHAT'S TRENDING LOCALLY:\r\n${context.trendingTopics.map(trend => `- ${trend.topic}`).join('\\n')}\r\n\r\n💡 Casually mention 1-2 relevant trends if they fit naturally.` : ''}\r\n\r\n🎨 WRITE LIKE A HUMAN:\r\n- Length: 60-100 words MAX (perfect for social scroll culture)\r\n- Format: Use line breaks for easy reading\r\n- Start: Hook with relatable question, story, or local observation\r\n- Voice: Warm, friendly, like texting a friend\r\n- Style: Contractions (we're, don't, can't), casual language\r\n- Local flavor: Only use local slang when it adds genuine value, not forced\r\n- Emojis: 4-6 emojis that feel natural\r\n- CTA: Fun, engaging invitation (not corporate)\r\n- Variation: Make this UNIQUE - use random seed: ${randomSeed}\r\n\r\n🌍 LANGUAGE INSTRUCTIONS:\r\n${context.useLocalLanguage === true ? `You are an expert in global cultures and local languages. Based on the business location \"${context.location}\", intelligently determine and use appropriate local expressions, slang, or cultural references that would resonate with the local audience.\r\n\r\n**DYNAMIC LANGUAGE INTELLIGENCE:**\r\n- Analyze the location and determine what local language, slang, or cultural references are commonly used there\r\n- Only use local expressions if you are 100% confident about their accuracy, meaning, and cultural appropriateness\r\n- Mix local language with English naturally (1-2 local words maximum per caption)\r\n- When uncertain about local language accuracy, use English instead` : `**ENGLISH-ONLY CONTENT:**\r\n- USE ONLY ENGLISH for all caption text\r\n- Do not use any local language words, slang, or phrases\r\n- Keep all content in clear, professional English\r\n- Focus on universal messaging that works across all markets\r\n- Maintain warm, friendly tone using English expressions only`}\r\n- Use 1-2 local words maximum per caption - keep it natural and authentic\r\n- Local language should enhance the business message, not distract from it\r\n- When uncertain about local language accuracy, always default to clear, engaging English\r\n- Consider local landmarks, cultural references, weather patterns, or lifestyle elements that locals would relate to\r\n- Make it feel like the content is created by someone who actually lives and works in that location\r\n\r\n**SAFETY RULES:**\r\n- NEVER use local language unless you are absolutely certain of its correctness\r\n- Avoid complex phrases, slang, or expressions you're uncertain about\r\n- Better to use engaging English than incorrect local language\r\n- Focus on authentic, location-relevant content rather than forced local language usage\r\n\r\n🚨 HUMAN WRITING STYLE:\r\n- No corporate jargon or marketing speak\r\n- No \"Imagine this...\" or \"Picture this...\" openings\r\n- Write like you're texting a friend about your business\r\n- Be conversational, not salesy\r\n\r\n💬 FUN CTA EXAMPLES:\r\n- \"DM us before your squad eats them all 🍪🔥\"\r\n- \"Halla at us, we got you 😉\"\r\n- \"Hit us up if you're interested 📱\"\r\n- \"Slide through our DMs 💬\"\r\n- \"Let's chat about it! 👇\"\r\n\r\n🌐 WEBSITE USAGE:\r\n- Only mention website when CTA specifically calls for it (e.g., \"check us out online\", \"visit our site\")\r\n- Use clean format without https:// or www.\r\n- Don't force website into every post - use contextually when it makes sense\r\n\r\nCreate a caption that sounds like it was written by a real person who cares about their community and business.\r\n\r\nReturn ONLY the caption text, no additional formatting or explanations.`;\r\n\r\n  return prompt;\r\n}\r\n\r\n// Export for global testing\r\nif (typeof window !== 'undefined') {\r\n  (window as any).testRevo20Availability = testRevo20Availability;\r\n  (window as any).generateWithRevo20 = generateWithRevo20;\r\n  (window as any).generateWithCharacterConsistency = generateWithCharacterConsistency;\r\n  (window as any).performIntelligentEditing = performIntelligentEditing;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;AAEA;AAYA;AAKA,kDAAkD;AAClD;AACA;AAEA;AACA,+EAA+E;AAC/E,sGAAsG;AACtG;AAAA;;;;;;;;AAEA,6DAA6D;AAC7D,MAAM,SACJ,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,IAChC,QAAQ,GAAG,CAAC,0BAA0B,IACtC,QAAQ,GAAG,CAAC,0BAA0B,IACtC,QAAQ,GAAG,CAAC,gCAAgC;AAE9C,MAAM,eACJ,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,0BAA0B;AAExC,IAAI,CAAC,QAAQ;IACX,QAAQ,KAAK,CAAC;IACd,QAAQ,KAAK,CAAC,uBAAuB;QACnC,QAAQ;YACN,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,cAAc;YAC5C,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,cAAc;YAC5C,sBAAsB,CAAC,CAAC,QAAQ,GAAG,CAAC,oBAAoB;QAC1D;QACA,QAAQ;YACN,4BAA4B,CAAC,CAAC,QAAQ,GAAG,CAAC,0BAA0B;YACpE,4BAA4B,CAAC,CAAC,QAAQ,GAAG,CAAC,0BAA0B;YACpE,kCAAkC,CAAC,CAAC,QAAQ,GAAG,CAAC,gCAAgC;QAClF;IACF;AACF;AAEA,sEAAsE;AACtE,MAAM,KAAK,IAAI,gKAAA,CAAA,qBAAkB,CAAC;AAElC,iDAAiD;AACjD,MAAM,SAAS,eAAe,IAAI,wKAAA,CAAA,UAAM,CAAC;IAAE,QAAQ;AAAa,KAAK;AAErE,IAAI,CAAC,cAAc;IACjB,QAAQ,IAAI,CAAC;AACf;AAEA;;CAEC,GACD,SAAS,gBAAgB,GAAW;IAClC,IAAI,CAAC,KAAK,OAAO;IACjB,OAAO,IACJ,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,UAAU,IAClB,OAAO,CAAC,OAAO,KAAK,wBAAwB;AACjD;AAwBA,iCAAiC;AACjC,eAAe,6BAA6B,KAA4B;IACtE,IAAI,CAAC,QAAQ;QACX,8DAA8D;QAC9D,QAAQ,GAAG,CAAC;QACZ,OAAO;YACL,SAAS,CAAC,aAAa,EAAE,MAAM,YAAY,CAAC,WAAW,GAAG,oBAAoB,EAAE,MAAM,YAAY,CAAC,YAAY,CAAC;YAChH,YAAY;gBAAC;gBAAgB;gBAAW;gBAAc;gBAAc;aAAQ;YAC5E,iBAAiB,CAAC,+DAA+D,EAAE,MAAM,QAAQ,EAAE;YACnG,gBAAgB;gBAAC;gBAAoB;gBAAwB;gBAAgB;aAAoB;YACjG,kBAAkB,MAAM,WAAW,CAAC,MAAM,GAAG,IAAI,MAAM,WAAW,GAAG;gBAAC;gBAAW;gBAAW;aAAU;YACtG,cAAc;gBAAC;gBAAgB;gBAAe;gBAAU;aAAQ;YAChE,gBAAgB;gBAAC;gBAAS;gBAAc;aAAkB;QAC5D;IACF;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,SAAS,CAAC,8DAA8D,EAAE,MAAM,QAAQ,CAAC;;;YAGvF,EAAE,MAAM,YAAY,CAAC;QACzB,EAAE,MAAM,YAAY,CAAC;YACjB,EAAE,MAAM,QAAQ,CAAC;YACjB,EAAE,MAAM,QAAQ,CAAC;mBACV,EAAE,MAAM,cAAc,CAAC;eAC3B,EAAE,MAAM,UAAU,CAAC;YACtB,EAAE,MAAM,OAAO,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,QAAQ,mBAAmB;gBAC7E,EAAE,MAAM,OAAO,CAAC,MAAM,WAAW,IAAI,MAAM,WAAW,CAAC,IAAI,CAAC,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkCnG,CAAC;QAEE,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO;YACP,UAAU;gBAAC;oBAAE,MAAM;oBAAQ,SAAS;gBAAO;aAAE;YAC7C,aAAa;YACb,YAAY;QACd;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAC9C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,mDAAmD;QACnD,IAAI,cAAc,QAAQ,IAAI;QAC9B,IAAI,YAAY,UAAU,CAAC,YAAY;YACrC,cAAc,YAAY,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,WAAW;QACzE;QACA,MAAM,gBAAgB,KAAK,KAAK,CAAC;QAEjC,QAAQ,GAAG,CAAC;QACZ,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,mCAAmC;QACnC,OAAO;YACL,SAAS,CAAC,SAAS,EAAE,MAAM,YAAY,CAAC,WAAW,GAAG,wBAAwB,EAAE,MAAM,YAAY,CAAC,eAAe,CAAC;YACnH,YAAY;gBAAC;gBAAc;gBAAc;gBAAW;gBAAgB;aAAU;YAC9E,iBAAiB,CAAC,6DAA6D,EAAE,MAAM,QAAQ,EAAE;YACjG,gBAAgB;gBAAC;gBAAmB;gBAAwB;gBAAqB;aAAoB;YACrG,kBAAkB,MAAM,WAAW,CAAC,MAAM,GAAG,IAAI,MAAM,WAAW,CAAC,KAAK,CAAC,GAAG,KAAK;gBAAC;gBAAW;gBAAW;aAAU;YAClH,cAAc;gBAAC;gBAAgB;gBAAc;gBAAe;aAAS;YACrE,gBAAgB;gBAAC;gBAAc;gBAAS;aAAa;QACvD;IACF;AACF;AAEA,uEAAuE;AACvE,MAAM,iBAAiB;AAiCvB;;;CAGC,GACD,eAAe,mCAAmC,KAA4B,EAAE,aAA6B;IAC3G,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,YAAY,CAAC,QAAQ,IAAI,iBAAiB;QAC5E,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,YAAY,EAAE;QAChD,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,QAAQ,EAAE;QAE5C,4CAA4C;QAC5C,QAAQ,GAAG,CAAC;QACZ,MAAM,iBAAiB,MAAM,CAAA,GAAA,0IAAA,CAAA,iCAA8B,AAAD,EACxD,MAAM,YAAY,EAClB,MAAM,YAAY,CAAC,QAAQ,IAAI,IAC/B,MAAM,QAAQ;QAEhB,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,eAAe,MAAM,CAAC,uCAAuC,CAAC;QAErF,yDAAyD;QACzD,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,gBAAgB,MAAM,6BAA6B,OAAO,gBAAgB;YAChF,IAAI,cAAc,OAAO,IAAI,cAAc,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC9D,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,yEAAyE;QACxF;QAEA,+CAA+C;QAC/C,QAAQ,GAAG,CAAC;QAEZ,uDAAuD;QACvD,MAAM,mBAAmB;YACvB,8BAA8B;YAC9B,cAAc,MAAM,YAAY;YAChC,UAAU,MAAM,YAAY,CAAC,QAAQ,IAAI;YACzC,aAAa,MAAM,YAAY,CAAC,WAAW,IAAI,MAAM,WAAW,IAAI;YACpE,aAAa,MAAM,YAAY,CAAC,WAAW,IAAI;YAC/C,eAAe,MAAM,YAAY,CAAC,aAAa,IAAI;YACnD,aAAa,MAAM,YAAY,CAAC,WAAW,IAAI;YAE/C,cAAc;YACd,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAAE,SAAS;YAAO;YACpE,aAAa,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAClD,MAAM;gBAAW,OAAO;gBAAQ,KAAK;YACvC;YAEA,oBAAoB;YACpB,UAAU;gBAAC;oBACT,UAAU,MAAM,QAAQ;oBACxB,aAAa,MAAM,WAAW,IAAI;gBACpC;aAAE;YAEF,wBAAwB;YACxB,UAAU,MAAM,OAAO,CAAC,MAAM,YAAY,CAAC,QAAQ,IAC/C,MAAM,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,QACtD,OAAO,MAAM,YAAY,CAAC,QAAQ,KAAK,WAAW,MAAM,YAAY,CAAC,QAAQ,GAAG;YACrF,gBAAgB,MAAM,YAAY,CAAC,cAAc,IAAI;YACrD,aAAa,MAAM,YAAY,CAAC,WAAW,IAAI;YAC/C,uBAAuB,MAAM,YAAY,CAAC,qBAAqB,IAAI;YAEnE,oBAAoB;YACpB,kBAAkB;gBAChB,mBAAmB;gBACnB,mBAAmB;YACrB;YAEA,yBAAyB;YACzB,YAAY,MAAM,YAAY,CAAC,UAAU,IAAI;YAC7C,aAAa,MAAM,YAAY,CAAC,WAAW,IAAI;YAC/C,aAAa,MAAM,YAAY,CAAC,WAAW,IAAI,CAAC;YAChD,aAAa,MAAM,YAAY,CAAC,WAAW,IAAI,CAAC;YAEhD,SAAS;YACT,cAAc,MAAM,YAAY,CAAC,YAAY,IAAI;YACjD,aAAa,MAAM,YAAY,CAAC,WAAW,IAAI;YAC/C,iBAAiB,MAAM,YAAY,CAAC,eAAe,IAAI;YAEvD,kBAAkB;YAClB,gBAAgB,MAAM,YAAY,CAAC,cAAc,IAAI,EAAE;QACzD;QAEA,qEAAqE;QACrE,QAAQ,GAAG,CAAC;QACZ,MAAM,SAAS,MAAM,CAAA,GAAA,2JAAA,CAAA,0BAAuB,AAAD,EAAE;QAE7C,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,OAAO,OAAO,EAAE,UAAU,EAAE,WAAW,CAAC;QACpF,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,OAAO,QAAQ,EAAE,MAAM,UAAU,OAAO,CAAA,MAAO,IAAI,UAAU,CAAC,MAAM,UAAU,GAAG;QAExH,4EAA4E;QAC5E,MAAM,UAAU,OAAO,OAAO,IAAI,CAAC,6BAA6B,EAAE,MAAM,YAAY,CAAC,YAAY,IAAI,YAAY,wBAAwB,CAAC;QAC1I,IAAI,WAAW,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,QAAQ,EAAE;QAExG,6BAA6B;QAC7B,IAAI,SAAS,MAAM,GAAG,IAAI;YACxB,MAAM,mBAAmB;gBAAC;gBAAY;gBAAiB;gBAAe;gBAAY;gBAAY;gBAAa;gBAAe;gBAAY;gBAAY;aAAc;YAChK,WAAW;mBAAI;mBAAa;aAAiB,CAAC,KAAK,CAAC,GAAG;YACvD,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,QAAQ,EAAE,MAAM,UAAU,OAAO,CAAA,MAAO,IAAI,UAAU,CAAC,MAAM,UAAU,CAAC,EAAE,kBAAkB,CAAC;QACnI,OAAO;YACL,WAAW,SAAS,KAAK,CAAC,GAAG;QAC/B;QAEA,OAAO;YAAE;YAAS;QAAS;IAE7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,QAAQ,GAAG,CAAC;QAEZ,gDAAgD;QAChD,MAAM,YAAY,MAAM,YAAY,CAAC,YAAY,IAAI;QACrD,MAAM,WAAW,MAAM,YAAY,CAAC,QAAQ,IAAI;QAChD,MAAM,eAAe,MAAM,YAAY;QAEvC,+CAA+C;QAC/C,MAAM,kBAAkB,CAAC,6BAA6B,EAAE,UAAU,UAAU,EAAE,aAAa,WAAW,GAAG,yBAAyB,EAAE,WAAW,CAAC,gBAAgB,EAAE,UAAU,GAAG,GAAG,GAAG,CAAC;QACtL,MAAM,mBAAmB;YAAC;YAAY;YAAiB;YAAe;YAAa;YAAY;YAAY;YAAY;YAAe;YAAY;SAAc;QAEhK,OAAO;YACL,SAAS;YACT,UAAU;QACZ;IACF;AACF;AAEA;;CAEC,GACD,eAAe,6BACb,KAA4B,EAC5B,cAAgE,EAChE,aAA6B;IAE7B,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,cAAc;QACd,MAAM,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,oBAAoB;QAC3G,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,QAAQ,IAAI,gKAAA,CAAA,qBAAkB,CAAC;QACrC,MAAM,QAAQ,MAAM,kBAAkB,CAAC;YAAE,OAAO;QAAmB;QAEnE,wEAAwE;QACxE,MAAM,gBAAgB,4BAA4B;YAChD,cAAc,MAAM,YAAY;YAChC,UAAU,MAAM,YAAY,CAAC,QAAQ,IAAI;YACzC,cAAc,MAAM,YAAY,CAAC,YAAY,IAAI;YACjD,UAAU,MAAM,QAAQ;YACxB,gBAAgB,MAAM,YAAY,CAAC,cAAc;YACjD,gBAAgB,eAAe,KAAK,CAAC,GAAG;YACxC,eAAe;YACf,kBAAkB,MAAM,gBAAgB;QAC1C;QAEA,QAAQ,GAAG,CAAC;QACZ,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;QAC3C,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,UAAU,SAAS,IAAI,GAAG,IAAI;QAEpC,qDAAqD;QACrD,MAAM,gBAAgB,CAAC,4BAA4B,EAAE,MAAM,YAAY,CAAC,aAAa,EAAE,MAAM,YAAY,CAAC,QAAQ,CAAC,4CAA4C,EAAE,MAAM,QAAQ,CAAC;;UAE1K,EAAE,QAAQ;;UAEV,EAAE,MAAM,YAAY,CAAC,YAAY,IAAI,WAAW;UAChD,EAAE,MAAM,YAAY,CAAC,QAAQ,CAAC;UAC9B,EAAE,MAAM,QAAQ,CAAC;;;;2BAIA,EAAE,MAAM,QAAQ,CAAC;wBACpB,EAAE,MAAM,YAAY,CAAC,QAAQ,CAAC;wBAC9B,EAAE,MAAM,YAAY,CAAC;;;4CAGD,CAAC;QAEzC,QAAQ,GAAG,CAAC;QACZ,MAAM,gBAAgB,MAAM,MAAM,eAAe,CAAC;QAClD,MAAM,kBAAkB,MAAM,cAAc,QAAQ;QACpD,MAAM,cAAc,gBAAgB,IAAI,GAAG,IAAI;QAE/C,mBAAmB;QACnB,MAAM,WAAW,YAAY,KAAK,CAAC,YAAY,EAAE;QAEjD,6BAA6B;QAC7B,MAAM,gBAAgB,SAAS,KAAK,CAAC,GAAG;QACxC,IAAI,cAAc,MAAM,GAAG,IAAI;YAC7B,MAAM,mBAAmB;gBAAC;gBAAY;gBAAiB;gBAAe;gBAAa;gBAAY;gBAAY;gBAAY;gBAAe;gBAAY;aAAc;YAChK,cAAc,IAAI,IAAI,iBAAiB,KAAK,CAAC,GAAG,KAAK,cAAc,MAAM;QAC3E;QAEA,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,QAAQ,MAAM,CAAC,YAAY,EAAE,cAAc,MAAM,CAAC,SAAS,CAAC;QAEzG,OAAO;YACL;YACA,UAAU;QACZ;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,MAAM;IACR;AACF;AAKO,eAAe,mBACpB,KAA4B;IAE5B,MAAM,YAAY,KAAK,GAAG;IAC1B,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IAEZ,IAAI;QACF,yCAAyC;QACzC,QAAQ,GAAG,CAAC;QAEZ,MAAM,gBAAgB,MAAM,6BAA6B;YACvD,cAAc,MAAM,YAAY,CAAC,YAAY,IAAI;YACjD,cAAc,MAAM,YAAY,CAAC,YAAY,IAAI;YACjD,UAAU,MAAM,YAAY,CAAC,QAAQ,IAAI;YACzC,UAAU,MAAM,QAAQ,IAAI;YAC5B,gBAAgB,MAAM,YAAY,CAAC,cAAc,IAAI;YACrD,YAAY,MAAM,YAAY,CAAC,UAAU,IAAI;YAC7C,UAAU,MAAM,YAAY,CAAC,QAAQ,IAAI,EAAE;YAC3C,aAAa,MAAM,YAAY,CAAC,WAAW,IAAI,EAAE;QACnD;QAEA,QAAQ,GAAG,CAAC,mCAAmC;YAC7C,SAAS,cAAc,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO;YACnD,YAAY,cAAc,UAAU;YACpC,iBAAiB,cAAc,eAAe,CAAC,SAAS,CAAC,GAAG,OAAO;QACrE;QAEA,iFAAiF;QACjF,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG,MAAM,kBAAkB,OAAO;QACnG,QAAQ,GAAG,CAAC,uBAAuB,WAAW,SAAS,CAAC,GAAG,OAAO;QAElE,0BAA0B;QAC1B,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC;YACZ,IAAI,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC3D,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,YAAY,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC;YACvF;YACA,IAAI,YAAY,KAAK,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,EAAE,MAAM,GAAG,GAAG;gBAClE,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,IAAI,CAAC,YAAY,KAAK,EAAE,IAAI,CAAC,MAAM,gBAAgB,CAAC;YACjG;QACF;QAEA,gCAAgC;QAChC,MAAM,sBAAsB;YAC1B;YACA;YACA;YACA;SACD;QAED,2DAA2D;QAC3D,MAAM,SAAgB;YACpB;gBAAE,MAAM;YAAW;SACpB;QAED,8DAA8D;QAC9D,IAAI,MAAM,YAAY,EAAE,aAAa;YACnC,QAAQ,GAAG,CAAC;YACZ,MAAM,iBAAiB,MAAM,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,+BAA+B;YACpG,MAAM,eAAe,MAAM,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,oBAAoB;YACrG,OAAO,IAAI,CAAC;gBACV,YAAY;oBACV,UAAU;oBACV,MAAM;gBACR;YACF;YACA,oBAAoB,IAAI,CAAC;QAC3B,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,sFAAsF;QACtF,IAAI,MAAM,cAAc,IAAI,MAAM,oBAAoB,EAAE;YACtD,QAAQ,GAAG,CAAC;YACZ,MAAM,aAAa,MAAM,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,+BAA+B;YACtF,OAAO,IAAI,CAAC;gBACV,YAAY;oBACV,UAAU;oBACV,MAAM;gBACR;YACF;QACF;QAEA,qEAAqE;QACrE,QAAQ,GAAG,CAAC;QAEZ,IAAI;QACJ,IAAI;QACJ,MAAM,aAAa;QAEnB,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,WAAW,2BAA2B,CAAC;gBAC5E,MAAM,QAAQ,GAAG,kBAAkB,CAAC;oBAAE,OAAO;gBAAe;gBAC5D,WAAW,MAAM,MAAM,eAAe,CAAC;gBACvC,QAAQ,GAAG,CAAC;gBACZ,OAAO,2BAA2B;YACpC,EAAE,OAAO,OAAY;gBACnB,YAAY;gBACZ,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,QAAQ,CAAC,EAAE,OAAO,WAAW;gBAE9D,0CAA0C;gBAC1C,IAAI,YAAY,YAAY;oBAC1B;gBACF;gBAEA,6CAA6C;gBAC7C,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,WAAW,MAAM,aAAa;gBAC3D,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,KAAK,iBAAiB,CAAC;gBAC3D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;QAEA,8CAA8C;QAC9C,IAAI,CAAC,UAAU;YACb,QAAQ,KAAK,CAAC;YACd,MAAM;QACR;QAEA,oFAAoF;QACpF,IAAI,WAAW;QACf,IAAI,cAAc;QAElB,oCAAoC;QACpC,IAAI,MAAM,oBAAoB,EAAE;YAC9B,oBAAoB,IAAI,CAAC;QAC3B;QACA,IAAI,MAAM,kBAAkB,EAAE;YAC5B,oBAAoB,IAAI,CAAC;QAC3B;QAEA,wEAAwE;QACxE,uDAAuD;QACvD,MAAM,iBAAiB,SAAS,QAAQ,IAAI;QAC5C,MAAM,QAAQ,eAAe,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,SAAS,EAAE;QAClE,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC;QACxD,QAAQ,GAAG,CAAC,2BAA2B,eAAe,UAAU,EAAE,UAAU;QAC5E,QAAQ,GAAG,CAAC,uBAAuB,eAAe,UAAU,EAAE,CAAC,EAAE,GAAG,WAAW;QAE/E,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,EAAE;gBACb,QAAQ,GAAG,CAAC,8BAA8B,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,OAAO;gBACxE,cAAc,KAAK,IAAI;YACzB,OAAO,IAAI,KAAK,UAAU,EAAE;gBAC1B,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,qBAAqB;oBAC/B,UAAU,KAAK,UAAU,CAAC,QAAQ;oBAClC,YAAY,KAAK,UAAU,CAAC,IAAI,EAAE,UAAU;gBAC9C;gBAEA,+CAA+C;gBAC/C,WAAW,CAAC,KAAK,EAAE,KAAK,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,UAAU,CAAC,IAAI,EAAE;gBAC5E,oBAAoB,IAAI,CACtB,6BACA,6BACA,yBACA;gBAEF;YACF;QACF;QAEA,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,qFAAqF;QACrF,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,mCAAmC,OAAO;QAE9E,MAAM,iBAAiB,KAAK,GAAG,KAAK;QACpC,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,eAAe,EAAE,CAAC;QAEpE,oDAAoD;QACpD,IAAI;YACF,MAAM,WAAW,CAAC,aAAa,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACxF,CAAA,GAAA,2IAAA,CAAA,yBAAsB,AAAD,EACnB,UACA,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,MAAM,WAAW,EACjB,IACA;gBACE,cAAc,MAAM,YAAY,CAAC,YAAY,GAAG;oBAAC,MAAM,YAAY,CAAC,YAAY;oBAAE,MAAM,YAAY,CAAC,WAAW;oBAAE,MAAM,YAAY,CAAC,eAAe;iBAAC,CAAC,MAAM,CAAC,WAAW,EAAE;gBAC1K,YAAY;gBACZ,aAAa,MAAM,WAAW,IAAI;gBAClC,QAAQ;oBAAC;oBAAqB;iBAAmB;gBACjD,aAAa,YAAY,SAAS,CAAC,GAAG,KAAK,uBAAuB;YACpE,GACA;gBACE,YAAY;gBACZ,gBAAgB,MAAM,YAAY,CAAC,WAAW,GAAG,KAAK;gBACtD,kBAAkB;gBAClB,gBAAgB,oBAAoB,KAAK;YAC3C;QAEJ,EAAE,OAAO,gBAAgB;YACvB,QAAQ,IAAI,CAAC,+CAA+C;QAC9D;QAEA,OAAO;YACL;YACA,OAAO;YACP;YACA,cAAc;YACd;YACA;YACA;YACA,UAAU;gBACR,sBAAsB,MAAM,oBAAoB,IAAI;gBACpD,oBAAoB,MAAM,kBAAkB,IAAI;gBAChD,aAAa,MAAM,WAAW,IAAI;gBAClC,eAAe;YACjB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC3G;AACF;AAEA;;CAEC,GACD,eAAe,kBAAkB,KAA4B,EAAE,aAA6B;IAM1F,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,cAAc,KAAK,EAAE,GAAG;IAE9F,mCAAmC;IACnC,MAAM,qBAAqB,uJAAA,CAAA,+BAA4B,CAAC,SAAsD,IAAI,uJAAA,CAAA,+BAA4B,CAAC,SAAS;IAExJ,mCAAmC;IACnC,MAAM,cAAc,uJAAA,CAAA,2BAAwB,CAAC,aAAsD,IAAI,uJAAA,CAAA,2BAAwB,CAAC,OAAO;IAEvI,0EAA0E;IAC1E,kDAAkD;IAClD,kBAAkB;IAClB,iCAAiC;IACjC,cAAc;IACd,sCAAsC;IACtC,+CAA+C;IAC/C,KAAK;IAEL,QAAQ,GAAG,CAAC,oCAAoC,cAAc,MAAM,aAAa,QAAQ,IAAI;IAC7F,QAAQ,GAAG,CAAC;IAEZ,8DAA8D;IAC9D,IAAI,iBAAwB,EAAE;IAC9B,IAAI,eAAoB,CAAC;IACzB,MAAM,kBAAuB;QAC3B,gBAAgB,EAAE;QAClB,iBAAiB;QACjB,gBAAgB,EAAE;QAClB,kBAAkB;IACpB;IAEA,QAAQ,GAAG,CAAC;IAEZ,4BAA4B;IAC5B,IAAI,oBAAoB;IACxB,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD,EACvC,cACA,UACA,aAAa,cAAc,IAAI,IAC/B;QAEF,oBAAoB,CAAA,GAAA,wIAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,iEAAiE;IAChF;IAEA,kDAAkD;IAClD,MAAM,0BAA0B,CAAC;;iBAElB,EAAE,SAAS;gDACoB,EAAE,aAAa;QACvD,EAAE,YAAY;;;EAGpB,CAAC;IAED,mEAAmE;IACnE,MAAM,oBAAoB,aAAa,YAAY,GAAG,CAAC;;mBAEtC,EAAE,aAAa,YAAY,CAAC;kBAC7B,EAAE,aAAa,WAAW,CAAC;sBACvB,EAAE,aAAa,eAAe,CAAC;;;;;;;;;EASnD,CAAC,GAAG,CAAC;;oCAE6B,EAAE,aAAa,YAAY,CAAC,SAAS,EAAE,aAAa,WAAW,CAAC,aAAa,EAAE,aAAa,eAAe,CAAC;;;EAG9I,CAAC;IAED,mDAAmD;IACnD,MAAM,mBAAmB;QACvB;QAAoB;QAAgB;QAAyB;QAC7D;QAAoB;QAAmB;QAAoB;QAC3D;QAA0B;QAAwB;QAAqB;QACvE;QAAkB;QAAmB;QAAiB;KACvD;IACD,MAAM,0BAA0B,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;IACrG,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,yBAAyB;IAE/E,sEAAsE;IACtE,MAAM,0BAA0B;QAC9B;QAAuB;QAAoB;QAAuB;QAClE;QAAsB;QAAiB;QAAwB;QAC/D;QAA4B;QAAuB;QAAuB;KAC3E;IACD,MAAM,wBAAwB,uBAAuB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,wBAAwB,MAAM,EAAE;IACjH,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,uBAAuB;IAE3E,0CAA0C;IAC1C,MAAM,qBAAqB,CAAC,cAAsB;QAChD,MAAM,WAAgC;YACpC,mBAAmB;gBACjB,WAAW;oBAAC;oBAA4B;oBAAwB;oBAAuB;oBAAqB;iBAA2B;gBACvI,cAAc;oBAAC;oBAAyC;oBAAwC;oBAAyC;oBAAiC;iBAAkC;gBAC5M,MAAM;oBAAC;oBAAqB;oBAAqB;oBAAkB;oBAAqB;iBAAiB;YAC3G;YACA,cAAc;gBACZ,WAAW;oBAAC;oBAA6B;oBAA+B;oBAA4B;oBAAoB;iBAAyB;gBACjJ,cAAc;oBAAC;oBAAgC;oBAA0C;oBAAmC;oBAA+B;iBAAwC;gBACnM,MAAM;oBAAC;oBAAmB;oBAAsB;oBAAiB;oBAAoB;iBAAc;YACrG;YACA,UAAU;gBACR,WAAW;oBAAC;oBAAqB;oBAAyB;oBAA2B;oBAAwB;iBAA6B;gBAC1I,cAAc;oBAAC;oBAAoC;oBAAqC;oBAA2B;oBAAsB;iBAAsB;gBAC/J,MAAM;oBAAC;oBAAuB;oBAAwB;oBAAmB;oBAAkB;iBAAkB;YAC/G;QACF;QAEA,MAAM,mBAAmB,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,SAAS;QACrE,MAAM,eAAe,WAAW,CAAC,+CAA+C,EAAE,UAAU,GAAG;QAE/F,OAAO,CAAC;sBACU,EAAE,iBAAiB,SAAS,CAAC,IAAI,CAAC,MAAM;0BACpC,EAAE,iBAAiB,YAAY,CAAC,IAAI,CAAC,MAAM;iBACpD,EAAE,iBAAiB,IAAI,CAAC,IAAI,CAAC,QAAQ,aAAa;wGACqC,CAAC;IACvG;IAEA,oDAAoD;IACpD,MAAM,sBAAsB,CAAC;QAC3B,MAAM,mBAA6C;YACjD,mBAAmB;gBAAC;gBAAmB;gBAAc;aAAa;YAClE,cAAc;gBAAC;gBAAe;gBAAe;aAAU;YACvD,UAAU;gBAAC;gBAAW;gBAAa;aAAW;YAC9C,cAAc;gBAAC;gBAAS;gBAAe;aAAW;YAClD,cAAc;gBAAC;gBAAe;gBAAa;aAAW;YACtD,aAAa;gBAAC;gBAAc;gBAAa;aAAa;YACtD,WAAW;gBAAC;gBAAY;gBAAW;aAAW;YAC9C,UAAU;gBAAC;gBAAW;gBAAa;aAAa;YAChD,UAAU;gBAAC;gBAAW;gBAAc;aAAW;YAC/C,eAAe;gBAAC;gBAAe;gBAAa;aAAS;QACvD;QAEA,MAAM,WAAW,gBAAgB,CAAC,aAAa,IAAI;YAAC;YAAa;YAAY;SAAW;QACxF,OAAO,SAAS,IAAI,CAAC;IACvB;IAEA,IAAI,SAAS,CAAC;;;;;;iGAMiF,EAAE,aAAa;SACvG,EAAE,YAAY,YAAY,EAAE,SAAS;;;;;;;;;;YAUlC,EAAE,aAAa,YAAY,IAAI,aAAa;QAChD,EAAE,aAAa;YACX,EAAE,SAAS,EAAE,EAAE,YAAY;YAC3B,EAAE,aAAa,QAAQ,IAAI,SAAS;mBAC7B,EAAE,aAAa,cAAc,IAAI,mBAAmB;AACvE,EAAE,aAAa,UAAU,GAAG,CAAC,WAAW,EAAE,aAAa,UAAU,EAAE,GAAG,GAAG;;;gBAGzD,EAAE,wBAAwB,OAAO,CAAC,KAAK,KAAK;AAC5D,EAAE,MAAM,sBAAsB,KAAK,OAAO,CAAC,kBAAkB,EAAE,sBAAsB,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,GAAG,mDAAmD;;AAEtK,EAAE,gBAAgB,CAAC;wBACK,EAAE,cAAc,OAAO,CAAC;sBAC1B,EAAE,cAAc,UAAU,CAAC,IAAI,CAAC,MAAM;wBACpC,EAAE,cAAc,eAAe,CAAC;uBACjC,EAAE,cAAc,cAAc,CAAC,IAAI,CAAC,MAAM;qBAC5C,EAAE,cAAc,YAAY,CAAC,IAAI,CAAC,MAAM;uBACtC,EAAE,cAAc,cAAc,CAAC,IAAI,CAAC,MAAM;yBACxC,EAAE,cAAc,gBAAgB,CAAC,IAAI,CAAC,MAAM;;sJAEiF,CAAC,GAAG,GAAG;;;oBAGzI,EAAE,aAAa,QAAQ,IAAI,SAAS;AACxD,EAAE,MAAM,sBAAsB,KAAK,OAAO,CAAC;;;;;;;;;;qEAU0B,CAAC,GAAG,CAAC;;;;kFAIQ,CAAC,CAAC;uEACb,EAAE,aAAa,QAAQ,IAAI,oBAAoB;;;;;AAKtH,EAAE,MAAM,gBAAgB,KAAK,OAAO,CAAC;;;;sEAIiC,CAAC,GAAG,CAAC;;;;kDAIzB,CAAC,CAAC;;;AAGpD,EAAE,kBAAkB;;AAEpB,EAAE,aAAa,WAAW,GAAG,CAAC;;;;;;;;;;AAU9B,CAAC,GAAG,GAAG;;;+DAGwD,EAAE,aAAa,aAAa,EAAE,aAAa,QAAQ,IAAI,oBAAoB;;;;;;;;;;AAU1I,EAAE,MAAM,gBAAgB,KAAK,OAAO,CAAC;6FACwD,CAAC,GAAG,CAAC;4FACN,CAAC,CAAC;;AAE9F,EAAE,gBAAgB,cAAc,IAAI,gBAAgB,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC;;;AAGjF,EAAE,gBAAgB,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAe,CAAC,EAAE,EAAE,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,WAAW,IAAI,MAAM,OAAO,IAAI,IAAI,EAAE,IAAI,CAAC,MAAM;;;AAG5J,CAAC,GAAG,GAAG;;AAEP,EAAE,gBAAgB,eAAe,IAAK,gBAAgB,cAAc,IAAI,gBAAgB,cAAc,CAAC,MAAM,GAAG,IAAK,CAAC;;AAEtH,EAAE,gBAAgB,eAAe,GAAG,CAAC,mBAAmB,EAAE,gBAAgB,eAAe,CAAC,SAAS,IAAI,GAAG,CAAC,EAAE,gBAAgB,eAAe,CAAC,WAAW,IAAI,GAAG,wDAAwD,CAAC,GAAG,GAAG;AAC9N,EAAE,gBAAgB,cAAc,IAAI,gBAAgB,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAe,MAAM,KAAK,IAAI,MAAM,IAAI,EAAE,IAAI,CAAC,MAAM,yCAAyC,CAAC,GAAG,GAAG;;;AAGxP,CAAC,GAAG,GAAG;;;;;;sBAMe,EAAE,aAAa;;;AAGrC,EAAE,gBAAgB,cAAc,IAAI,gBAAgB,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,sFAAsF,CAAC,GAAG,GAAG;AAC9K,EAAE,gBAAgB,eAAe,GAAG,CAAC,gCAAgC,EAAE,gBAAgB,eAAe,CAAC,SAAS,IAAI,GAAG,yCAAyC,CAAC,GAAG,GAAG;;;;;;MAMjK,EAAE,aAAa,UAAU,GAAG,CAAC,SAAS,EAAE,gBAAgB,aAAa,UAAU,GAAG,GAAG,4BAA4B;MACjH,EAAE,aAAa,WAAW,EAAE,QAAQ,CAAC,OAAO,EAAE,aAAa,WAAW,CAAC,KAAK,EAAE,GAAG,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAgCjG,EAAE,aAAa,YAAY,IAAI,aAAa;YAC5C,EAAE,aAAa;YACf,EAAE,aAAa,QAAQ,IAAI,SAAS;UACtC,EAAE,aAAa,YAAY,GAAG,CAAC,SAAS,EAAE,aAAa,YAAY,EAAE,GAAG,iCAAiC,aAAa,cAAc,GAAG,CAAC,aAAa,EAAE,aAAa,cAAc,EAAE,GAAG,GAAG;QAC5L,EAAE,aAAa,gBAAgB,IAAI,gCAAgC;;;0EAGD,EAAE,aAAa;;2BAE9D,EAAE,aAAa,QAAQ,IAAI,oBAAoB;;;;;;;;;;;;;;;;AAgB1E,EAAE,YAAY;;;AAGd,EAAE,mBAAmB,gBAAgB,IAAI,CAAC,aAAa,EAAE,SAAS,0CAA0C,CAAC,CAAC;;;AAG9G,EAAE,uJAAA,CAAA,6BAA0B,CAAC;;;AAG7B,EAAE,uJAAA,CAAA,mCAAgC,CAAC;;AAEnC,EAAE,oBAAoB,CAAC,+BAA+B,EAAE,mBAAmB,GAAG,GAAG;;AAEjF,EAAE,uCAA0B,CAAC,iCAAiC,EAAE,yBAAyB,wCAAM;;wBAEvE,EAAE,wBAAwB;;;;UAIxC,EAAE;QAAC;QAAc;QAAgB;QAAY;QAAY;QAAY;KAAe,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC;mBAC3G,EAAE;QAAC;QAAiB;QAAgB;KAAmB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC;oBACtF,EAAE;QAAC;QAAkB;QAAiB;QAAqB;KAAmB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC;mBAC/G,EAAE;QAAC;QAAmB;QAAsB;QAAiB;KAAqB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC;wBAC7G,EAAE;QAAC;QAAkB;QAAoB;QAAqB;KAAqB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC;;;;;;;;;;CAU1I,EAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;cAuBC,EAAE,SAAS;;;EAGvB,EAAE,YAAY;;;;AAIhB,EAAE,aAAa,WAAW,GAAG,iEAAiE,GAAG;;0GAES,CAAC;IAEzG,yCAAyC;IACzC,IAAI,MAAM,oBAAoB,IAAI,MAAM,cAAc,EAAE;QACtD,UAAU,CAAC;;;gEAGiD,CAAC;IAC/D;IAEA,uCAAuC;IACvC,IAAI,MAAM,kBAAkB,IAAI,MAAM,mBAAmB,EAAE;QACzD,UAAU,CAAC;EACb,EAAE,MAAM,mBAAmB,CAAC;;0CAEY,CAAC;IACzC;IAEA,UAAU,CAAC;8EACiE,EAAE,aAAa;;;;;;;;;;gBAU7E,EAAE,SAAS;;;;;;;;mCAQQ,EAAE,aAAa;AAClD,EAAE,mBAAmB,cAAc,aAAa,QAAQ,EAAE;;;;;;;;;;;;gCAY1B,EAAE,aAAa,QAAQ,IAAI,gBAAgB;;;;;;;;;kCASzC,EAAE,aAAa,QAAQ,IAAI,aAAa;;;;;;;uCAOnC,EAAE,aAAa,gBAAgB,IAAI,wBAAwB;;kCAEhE,EAAE,aAAa,QAAQ,IAAI,sBAAsB;;;;;;;gCAOnD,EAAE,aAAa,YAAY,EAAE,QAAQ,QAAQ,OAAO,YAAY,GAAG,EAAE,aAAa,OAAO,CAAC,QAAQ,IAAI;kCACpG,EAAE,oBAAoB,cAAc;+BACvC,EAAE,aAAa,QAAQ,GAAG,CAAC,CAAC,EAAE,aAAa,QAAQ,CAAC,OAAO,CAAC,QAAQ,IAAI,QAAQ,EAAE,aAAa,QAAQ,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,qBAAqB;;mCAEjJ,EAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oFAiCsC,CAAC;IAEnF,OAAO;QACL,YAAY;QACZ;QACA;QACA,aAAa;IACf;AACF;AAKO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QAEZ,gEAAgE;QAChE,MAAM,WAAW,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC;YAC/C,OAAO;YACP,UAAU;gBACR;oBAAE,MAAM;gBAA2F;aACpG;QACH;QAEA,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,0BAA0B;YACpC,YAAY,SAAS,UAAU,EAAE,UAAU;YAC3C,OAAO,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,OAAO,UAAU;QAC7D;QAEA,+DAA+D;QAC/D,MAAM,QAAQ,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,SAAS,EAAE;QAC5D,IAAI,WAAW;QACf,IAAI,UAAU;QAEd,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,EAAE;gBACb,QAAQ,GAAG,CAAC,2BAA2B,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,OAAO;gBACrE,UAAU;YACZ;YACA,IAAI,KAAK,UAAU,EAAE;gBACnB,QAAQ,GAAG,CAAC,yBAAyB,KAAK,UAAU,CAAC,QAAQ;gBAC7D,WAAW;YACb;QACF;QAEA,IAAI,UAAU;YACZ,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,OAAO,IAAI,SAAS;YAClB,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,QAAQ,KAAK,CAAC,kBAAkB;YAC9B,MAAM,iBAAiB,QAAQ,MAAM,IAAI,GAAG;YAC5C,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD;QACA,OAAO;IACT;AACF;AAKO,eAAe,iCACpB,KAA4B,EAC5B,eAAyB;IAEzB,QAAQ,GAAG,CAAC;IAEZ,MAAM,gBAAgB;QACpB,GAAG,KAAK;QACR,gBAAgB,eAAe,CAAC,EAAE;QAClC,sBAAsB;QACtB,qBAAqB,CAAC,+FAA+F,EAAE,MAAM,SAAS,EAAE;IAC1I;IAEA,OAAO,mBAAmB;AAC5B;AAKO,eAAe,0BACpB,SAAiB,EACjB,aAAqB,EACrB,YAA0B;IAE1B,QAAQ,GAAG,CAAC;IAEZ,MAAM,QAA+B;QACnC,cAAc,aAAa,YAAY,IAAI;QAC3C,UAAU;QACV,aAAa,aAAa,WAAW,IAAI;QACzC,WAAW;QACX;QACA,gBAAgB;QAChB,oBAAoB;QACpB,qBAAqB,CAAC,yBAAyB,EAAE,cAAc,kEAAkE,CAAC;IACpI;IAEA,OAAO,mBAAmB;AAC5B;AAEA;;CAEC,GACD,SAAS,4BAA4B,OASpC;IACC,MAAM,aAAa,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC;IAExD,IAAI,SAAS,CAAC,kEAAkE,EAAE,QAAQ,QAAQ,CAAC;;;YAGzF,EAAE,QAAQ,YAAY,CAAC;QAC3B,EAAE,QAAQ,YAAY,CAAC;YACnB,EAAE,QAAQ,QAAQ,CAAC;YACnB,EAAE,QAAQ,QAAQ,CAAC;mBACZ,EAAE,QAAQ,cAAc,IAAI,mBAAmB;;AAElE,EAAE,QAAQ,aAAa,GAAG,CAAC;oBACP,EAAE,QAAQ,aAAa,CAAC,OAAO,CAAC;sBAC9B,EAAE,QAAQ,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM;uBAC7C,EAAE,QAAQ,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM;YAC7D,EAAE,QAAQ,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG;;AAEnE,EAAE,QAAQ,cAAc,IAAI,QAAQ,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC;AACjE,EAAE,QAAQ,cAAc,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC,EAAE,EAAE,MAAM,KAAK,EAAE,EAAE,IAAI,CAAC,MAAM;;8DAEP,CAAC,GAAG,GAAG;;;;;;;;;;;iDAWpB,EAAE,WAAW;;;AAG9D,EAAE,QAAQ,gBAAgB,KAAK,OAAO,CAAC,0FAA0F,EAAE,QAAQ,QAAQ,CAAC;;;;;;mEAMjF,CAAC,GAAG,CAAC;;;;;6DAKX,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uEAiCQ,CAAC;IAEtE,OAAO;AACT;AAEA,4BAA4B;AAC5B,uCAAmC;;AAKnC", "debugId": null}}, {"offset": {"line": 7046, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/api/generate-revo-2.0/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { generateWithRevo20 } from '@/ai/revo-2.0-service';\r\nimport { BrandProfile } from '@/lib/types';\r\n\r\n/**\r\n * API route for Revo 2.0 content generation\r\n * This keeps server-side modules on the server\r\n */\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    const {\r\n      businessType,\r\n      platform,\r\n      visualStyle,\r\n      imageText,\r\n      brandProfile,\r\n      aspectRatio = '1:1',\r\n      includePeopleInDesigns = true,\r\n      useLocalLanguage = false\r\n    } = body;\r\n\r\n    console.log('🚀 API: Generating content with Revo 2.0...');\r\n\r\n    // Validate required fields\r\n    if (!businessType || !platform || !brandProfile) {\r\n      return NextResponse.json({\r\n        success: false,\r\n        error: 'Missing required fields: businessType, platform, or brandProfile'\r\n      }, { status: 400 });\r\n    }\r\n\r\n    // Generate content with Revo 2.0\r\n    const result = await generateWithRevo20({\r\n      businessType,\r\n      platform,\r\n      visualStyle: visualStyle || 'modern',\r\n      imageText: imageText || `${brandProfile.businessName || businessType} - Premium Content`,\r\n      brandProfile,\r\n      aspectRatio,\r\n      includePeopleInDesigns,\r\n      useLocalLanguage\r\n    });\r\n\r\n    console.log('✅ API: Revo 2.0 generation completed successfully');\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      imageUrl: result.imageUrl,\r\n      model: result.model,\r\n      qualityScore: result.qualityScore,\r\n      processingTime: result.processingTime,\r\n      enhancementsApplied: result.enhancementsApplied,\r\n      caption: result.caption,\r\n      hashtags: result.hashtags,\r\n      message: 'Revo 2.0 content generated successfully'\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('❌ API: Revo 2.0 generation error:', error);\r\n\r\n    return NextResponse.json({\r\n      success: false,\r\n      error: error instanceof Error ? error.message : 'Unknown error',\r\n      message: 'Revo 2.0 generation failed'\r\n    }, { status: 500 });\r\n  }\r\n}\r\n\r\nexport async function GET() {\r\n  return NextResponse.json({\r\n    message: 'Revo 2.0 Generation API',\r\n    description: 'Use POST method to generate content with Revo 2.0',\r\n    requiredFields: ['businessType', 'platform', 'brandProfile'],\r\n    optionalFields: ['visualStyle', 'imageText', 'aspectRatio']\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAOO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,cAAc,KAAK,EACnB,yBAAyB,IAAI,EAC7B,mBAAmB,KAAK,EACzB,GAAG;QAEJ,QAAQ,GAAG,CAAC;QAEZ,2BAA2B;QAC3B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,cAAc;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;YACtC;YACA;YACA,aAAa,eAAe;YAC5B,WAAW,aAAa,GAAG,aAAa,YAAY,IAAI,aAAa,kBAAkB,CAAC;YACxF;YACA;YACA;YACA;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,UAAU,OAAO,QAAQ;YACzB,OAAO,OAAO,KAAK;YACnB,cAAc,OAAO,YAAY;YACjC,gBAAgB,OAAO,cAAc;YACrC,qBAAqB,OAAO,mBAAmB;YAC/C,SAAS,OAAO,OAAO;YACvB,UAAU,OAAO,QAAQ;YACzB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QAEnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,aAAa;QACb,gBAAgB;YAAC;YAAgB;YAAY;SAAe;QAC5D,gBAAgB;YAAC;YAAe;YAAa;SAAc;IAC7D;AACF", "debugId": null}}]}