{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "default-service-name.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/platform/node/default-service-name.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function defaultServiceName(): string {\n  return `unknown_service:${process.argv0}`;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEG,SAAU,kBAAkB;IAChC,OAAO,qBAAmB,OAAO,CAAC,KAAO,CAAC;AAC5C,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "file": "Resource.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/Resource.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport {\n  SEMRESATTRS_SERVICE_NAME,\n  SEMRESATTRS_TELEMETRY_SDK_LANGUAGE,\n  SEMRESATTRS_TELEMETRY_SDK_NAME,\n  SEMRESATTRS_TELEMETRY_SDK_VERSION,\n} from '@opentelemetry/semantic-conventions';\nimport { SDK_INFO } from '@opentelemetry/core';\nimport { ResourceAttributes } from './types';\nimport { defaultServiceName } from './platform';\nimport { IResource } from './IResource';\n\n/**\n * A Resource describes the entity for which a signals (metrics or trace) are\n * collected.\n */\nexport class Resource implements IResource {\n  static readonly EMPTY = new Resource({});\n  private _syncAttributes?: ResourceAttributes;\n  private _asyncAttributesPromise?: Promise<ResourceAttributes>;\n  private _attributes?: ResourceAttributes;\n\n  /**\n   * Check if async attributes have resolved. This is useful to avoid awaiting\n   * waitForAsyncAttributes (which will introduce asynchronous behavior) when not necessary.\n   *\n   * @returns true if the resource \"attributes\" property is not yet settled to its final value\n   */\n  public asyncAttributesPending?: boolean;\n\n  /**\n   * Returns an empty Resource\n   */\n  static empty(): IResource {\n    return Resource.EMPTY;\n  }\n\n  /**\n   * Returns a Resource that identifies the SDK in use.\n   */\n  static default(): IResource {\n    return new Resource({\n      [SEMRESATTRS_SERVICE_NAME]: defaultServiceName(),\n      [SEMRESATTRS_TELEMETRY_SDK_LANGUAGE]:\n        SDK_INFO[SEMRESATTRS_TELEMETRY_SDK_LANGUAGE],\n      [SEMRESATTRS_TELEMETRY_SDK_NAME]:\n        SDK_INFO[SEMRESATTRS_TELEMETRY_SDK_NAME],\n      [SEMRESATTRS_TELEMETRY_SDK_VERSION]:\n        SDK_INFO[SEMRESATTRS_TELEMETRY_SDK_VERSION],\n    });\n  }\n\n  constructor(\n    /**\n     * A dictionary of attributes with string keys and values that provide\n     * information about the entity as numbers, strings or booleans\n     * TODO: Consider to add check/validation on attributes.\n     */\n    attributes: ResourceAttributes,\n    asyncAttributesPromise?: Promise<ResourceAttributes>\n  ) {\n    this._attributes = attributes;\n    this.asyncAttributesPending = asyncAttributesPromise != null;\n    this._syncAttributes = this._attributes ?? {};\n    this._asyncAttributesPromise = asyncAttributesPromise?.then(\n      asyncAttributes => {\n        this._attributes = Object.assign({}, this._attributes, asyncAttributes);\n        this.asyncAttributesPending = false;\n        return asyncAttributes;\n      },\n      err => {\n        diag.debug(\"a resource's async attributes promise rejected: %s\", err);\n        this.asyncAttributesPending = false;\n        return {};\n      }\n    );\n  }\n\n  get attributes(): ResourceAttributes {\n    if (this.asyncAttributesPending) {\n      diag.error(\n        'Accessing resource attributes before async attributes settled'\n      );\n    }\n\n    return this._attributes ?? {};\n  }\n\n  /**\n   * Returns a promise that will never be rejected. Resolves when all async attributes have finished being added to\n   * this Resource's attributes. This is useful in exporters to block until resource detection\n   * has finished.\n   */\n  async waitForAsyncAttributes?(): Promise<void> {\n    if (this.asyncAttributesPending) {\n      await this._asyncAttributesPromise;\n    }\n  }\n\n  /**\n   * Returns a new, merged {@link Resource} by merging the current Resource\n   * with the other Resource. In case of a collision, other Resource takes\n   * precedence.\n   *\n   * @param other the Resource that will be merged with this.\n   * @returns the newly merged Resource.\n   */\n  merge(other: IResource | null): IResource {\n    if (!other) return this;\n\n    // SpanAttributes from other resource overwrite attributes from this resource.\n    const mergedSyncAttributes = {\n      ...this._syncAttributes,\n      //Support for old resource implementation where _syncAttributes is not defined\n      ...((other as Resource)._syncAttributes ?? other.attributes),\n    };\n\n    if (\n      !this._asyncAttributesPromise &&\n      !(other as Resource)._asyncAttributesPromise\n    ) {\n      return new Resource(mergedSyncAttributes);\n    }\n\n    const mergedAttributesPromise = Promise.all([\n      this._asyncAttributesPromise,\n      (other as Resource)._asyncAttributesPromise,\n    ]).then(([thisAsyncAttributes, otherAsyncAttributes]) => {\n      return {\n        ...this._syncAttributes,\n        ...thisAsyncAttributes,\n        //Support for old resource implementation where _syncAttributes is not defined\n        ...((other as Resource)._syncAttributes ?? other.attributes),\n        ...otherAsyncAttributes,\n      };\n    });\n\n    return new Resource(mergedSyncAttributes, mergedAttributesPromise);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EACL,wBAAwB,EACxB,kCAAkC,EAClC,8BAA8B,EAC9B,iCAAiC,GAClC,MAAM,qCAAqC,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAE/C,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGhD;;;GAGG,CACH,IAAA,WAAA;IAoCE,SAAA,SACE;;;;OAIG,CACH,UAA8B,EAC9B,sBAAoD;QAPtD,IAAA,QAAA,IAAA,CAwBC;;QAfC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,IAAI,IAAI,CAAC;QAC7D,IAAI,CAAC,eAAe,GAAG,CAAA,KAAA,IAAI,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAA,CAAE,CAAC;QAC9C,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,KAAA,QAAtB,sBAAsB,KAAA,KAAA,IAAA,KAAA,IAAtB,sBAAsB,CAAE,IAAI,CACzD,SAAA,eAAe;YACb,KAAI,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,KAAI,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YACxE,KAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;YACpC,OAAO,eAAe,CAAC;QACzB,CAAC,EACD,SAAA,GAAG;kJACD,OAAI,CAAC,KAAK,CAAC,oDAAoD,EAAE,GAAG,CAAC,CAAC;YACtE,KAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;YACpC,OAAO,CAAA,CAAE,CAAC;QACZ,CAAC,CACF,CAAC;IACJ,CAAC;IA9CD;;OAEG,CACI,SAAA,KAAK,GAAZ;QACE,OAAO,QAAQ,CAAC,KAAK,CAAC;IACxB,CAAC;IAED;;OAEG,CACI,SAAA,OAAO,GAAd;;QACE,OAAO,IAAI,QAAQ,CAAA,CAAA,KAAA,CAAA,GACjB,EAAA,wNAAC,2BAAwB,CAAA,wNAAG,qBAAA,AAAkB,EAAE,GAChD,EAAA,uNAAC,sCAAkC,CAAA,gMACjC,WAAQ,wNAAC,qCAAkC,CAAC,EAC9C,EAAA,wNAAC,iCAA8B,CAAA,gMAC7B,WAAQ,wNAAC,iCAA8B,CAAC,EAC1C,EAAA,CAAC,2PAAiC,CAAA,gMAChC,WAAQ,wNAAC,oCAAiC,CAAC,MAC7C,CAAC;IACL,CAAC;IA4BD,OAAA,cAAA,CAAI,SAAA,SAAA,EAAA,YAAU,EAAA;aAAd;;YACE,IAAI,IAAI,CAAC,sBAAsB,EAAE;sJAC/B,OAAI,CAAC,KAAK,CACR,+DAA+D,CAChE,CAAC;aACH;YAED,OAAO,CAAA,KAAA,IAAI,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAA,CAAE,CAAC;QAChC,CAAC;;;OAAA;IAED;;;;OAIG,CACG,SAAA,SAAA,CAAA,sBAAsB,GAA5B;;;;;6BACM,IAAI,CAAC,sBAAsB,EAA3B,OAAA;4BAAA,EAAA,OAAA;4BAAA;yBAAA,CAA2B;wBAC7B,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,uBAAuB;yBAAA,CAAA;;wBAAlC,GAAA,IAAA,EAAkC,CAAC;;;;;;;;;KAEtC;IAED;;;;;;;OAOG,CACH,SAAA,SAAA,CAAA,KAAK,GAAL,SAAM,KAAuB;QAA7B,IAAA,QAAA,IAAA,CA+BC;;QA9BC,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC;QAExB,8EAA8E;QAC9E,IAAM,oBAAoB,GAAA,SAAA,SAAA,CAAA,GACrB,IAAI,CAAC,eAAe,GAEpB,AAAC,CAAA,KAAC,KAAkB,CAAC,eAAe,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,CAAC,UAAU,CAAC,CAC7D,CAAC;QAEF,IACE,CAAC,IAAI,CAAC,uBAAuB,IAC7B,CAAE,KAAkB,CAAC,uBAAuB,EAC5C;YACA,OAAO,IAAI,QAAQ,CAAC,oBAAoB,CAAC,CAAC;SAC3C;QAED,IAAM,uBAAuB,GAAG,OAAO,CAAC,GAAG,CAAC;YAC1C,IAAI,CAAC,uBAAuB;YAC3B,KAAkB,CAAC,uBAAuB;SAC5C,CAAC,CAAC,IAAI,CAAC,SAAC,EAA2C;;gBAA3C,KAAA,OAAA,IAAA,EAA2C,EAA1C,mBAAmB,GAAA,EAAA,CAAA,EAAA,EAAE,oBAAoB,GAAA,EAAA,CAAA,EAAA;YACjD,OAAA,SAAA,SAAA,SAAA,SAAA,CAAA,GACK,KAAI,CAAC,eAAe,GACpB,mBAAmB,GAEnB,AAAC,CAAA,KAAC,KAAkB,CAAC,eAAe,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,CAAC,UAAU,CAAC,EACzD,oBAAoB,EACvB;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,QAAQ,CAAC,oBAAoB,EAAE,uBAAuB,CAAC,CAAC;IACrE,CAAC;IAzHe,SAAA,KAAK,GAAG,IAAI,QAAQ,CAAC,CAAA,CAAE,CAAC,CAAC;IA0H3C,OAAA,QAAC;CAAA,AA3HD,IA2HC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Resource } from './Resource';\nexport { IResource } from './IResource';\nexport { defaultServiceName } from './platform';\nexport { DetectorSync, ResourceAttributes, Detector } from './types';\nexport { ResourceDetectionConfig } from './config';\nexport {\n  browserDetector,\n  browserDetectorSync,\n  envDetector,\n  envDetectorSync,\n  hostDetector,\n  hostDetectorSync,\n  osDetector,\n  osDetectorSync,\n  processDetector,\n  processDetectorSync,\n  serviceInstanceIdDetectorSync,\n} from './detectors';\nexport { detectResourcesSync, detectResources } from './detect-resources';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "file": "BrowserDetectorSync.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/BrowserDetectorSync.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION,\n  SEMRESATTRS_PROCESS_RUNTIME_NAME,\n  SEMRESATTRS_PROCESS_RUNTIME_VERSION,\n} from '@opentelemetry/semantic-conventions';\nimport { DetectorSync, ResourceAttributes } from '../types';\nimport { diag } from '@opentelemetry/api';\nimport { ResourceDetectionConfig } from '../config';\nimport { IResource } from '../IResource';\nimport { Resource } from '../Resource';\n\n/**\n * BrowserDetectorSync will be used to detect the resources related to browser.\n */\nclass BrowserDetectorSync implements DetectorSync {\n  detect(config?: ResourceDetectionConfig): IResource {\n    const isBrowser =\n      typeof navigator !== 'undefined' &&\n      global.process?.versions?.node === undefined && // Node.js v21 adds `navigator`\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore don't have Bun types\n      global.Bun?.version === undefined; // Bun (bun.sh) defines `navigator`\n    if (!isBrowser) {\n      return Resource.empty();\n    }\n    const browserResource: ResourceAttributes = {\n      [SEMRESATTRS_PROCESS_RUNTIME_NAME]: 'browser',\n      [SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION]: 'Web Browser',\n      [SEMRESATTRS_PROCESS_RUNTIME_VERSION]: navigator.userAgent,\n    };\n    return this._getResourceAttributes(browserResource, config);\n  }\n  /**\n   * Validates process resource attribute map from process variables\n   *\n   * @param browserResource The un-sanitized resource attributes from process as key/value pairs.\n   * @param config: Config\n   * @returns The sanitized resource attributes.\n   */\n  private _getResourceAttributes(\n    browserResource: ResourceAttributes,\n    _config?: ResourceDetectionConfig\n  ) {\n    if (browserResource[SEMRESATTRS_PROCESS_RUNTIME_VERSION] === '') {\n      diag.debug(\n        'BrowserDetector failed: Unable to find required browser resources. '\n      );\n      return Resource.empty();\n    } else {\n      return new Resource({\n        ...browserResource,\n      });\n    }\n  }\n}\n\nexport const browserDetectorSync = new BrowserDetectorSync();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,uCAAuC,EACvC,gCAAgC,EAChC,mCAAmC,GACpC,MAAM,qCAAqC,CAAC;AAE7C,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAG1C,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;;;;;;;;;;;;;;AAEvC;;GAEG,CACH,IAAA,sBAAA;IAAA,SAAA,uBAwCA,CAAC;IAvCC,oBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,MAAgC;;;QACrC,IAAM,SAAS,GACb,OAAO,SAAS,KAAK,WAAW,IAChC,CAAA,CAAA,KAAA,CAAA,KAAA,MAAM,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,MAAK,SAAS,IAAI,+BAA+B;QAC/E,6DAA6D;QAC7D,kCAAkC;QAClC,CAAA,CAAA,KAAA,MAAM,CAAC,GAAG,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,OAAO,MAAK,SAAS,CAAC,CAAC,mCAAmC;QACxE,IAAI,CAAC,SAAS,EAAE;YACd,kLAAO,WAAQ,CAAC,KAAK,EAAE,CAAC;SACzB;QACD,IAAM,eAAe,GAAA,CAAA,KAAA,CAAA,GACnB,EAAA,wNAAC,mCAAgC,CAAA,GAAG,SAAS,EAC7C,EAAA,wNAAC,0CAAuC,CAAA,GAAG,aAAa,EACxD,EAAA,wNAAC,sCAAmC,CAAA,GAAG,SAAS,CAAC,SAAS,KAC3D,CAAC;QACF,OAAO,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;IACD;;;;;;OAMG,CACK,oBAAA,SAAA,CAAA,sBAAsB,GAA9B,SACE,eAAmC,EACnC,OAAiC;QAEjC,IAAI,eAAe,wNAAC,sCAAmC,CAAC,KAAK,EAAE,EAAE;kJAC/D,OAAI,CAAC,KAAK,CACR,qEAAqE,CACtE,CAAC;YACF,kLAAO,WAAQ,CAAC,KAAK,EAAE,CAAC;SACzB,MAAM;YACL,OAAO,+KAAI,WAAQ,CAAA,SAAA,CAAA,GACd,eAAe,EAClB,CAAC;SACJ;IACH,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAxCD,IAwCC;AAEM,IAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "file": "BrowserDetector.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/BrowserDetector.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { IResource } from '../IResource';\nimport { ResourceDetectionConfig } from '../config';\nimport { Detector } from '../types';\nimport { browserDetectorSync } from './BrowserDetectorSync';\n\n/**\n * BrowserDetector will be used to detect the resources related to browser.\n */\nclass BrowserDetector implements Detector {\n  detect(config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(browserDetectorSync.detect(config));\n  }\n}\n\nexport const browserDetector = new BrowserDetector();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;;AAE5D;;GAEG,CACH,IAAA,kBAAA;IAAA,SAAA,mBAIA,CAAC;IAHC,gBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,MAAgC;QACrC,OAAO,OAAO,CAAC,OAAO,oMAAC,sBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7D,CAAC;IACH,OAAA,eAAC;AAAD,CAAC,AAJD,IAIC;AAEM,IAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "file": "EnvDetectorSync.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/EnvDetectorSync.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { getEnv } from '@opentelemetry/core';\nimport { SEMRESATTRS_SERVICE_NAME } from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../Resource';\nimport { DetectorSync, ResourceAttributes } from '../types';\nimport { ResourceDetectionConfig } from '../config';\nimport { IResource } from '../IResource';\n\n/**\n * EnvDetectorSync can be used to detect the presence of and create a Resource\n * from the OTEL_RESOURCE_ATTRIBUTES environment variable.\n */\nclass EnvDetectorSync implements DetectorSync {\n  // Type, attribute keys, and attribute values should not exceed 256 characters.\n  private readonly _MAX_LENGTH = 255;\n\n  // OTEL_RESOURCE_ATTRIBUTES is a comma-separated list of attributes.\n  private readonly _COMMA_SEPARATOR = ',';\n\n  // OTEL_RESOURCE_ATTRIBUTES contains key value pair separated by '='.\n  private readonly _LABEL_KEY_VALUE_SPLITTER = '=';\n\n  private readonly _ERROR_MESSAGE_INVALID_CHARS =\n    'should be a ASCII string with a length greater than 0 and not exceed ' +\n    this._MAX_LENGTH +\n    ' characters.';\n\n  private readonly _ERROR_MESSAGE_INVALID_VALUE =\n    'should be a ASCII string with a length not exceed ' +\n    this._MAX_LENGTH +\n    ' characters.';\n\n  /**\n   * Returns a {@link Resource} populated with attributes from the\n   * OTEL_RESOURCE_ATTRIBUTES environment variable. Note this is an async\n   * function to conform to the Detector interface.\n   *\n   * @param config The resource detection config\n   */\n  detect(_config?: ResourceDetectionConfig): IResource {\n    const attributes: ResourceAttributes = {};\n    const env = getEnv();\n\n    const rawAttributes = env.OTEL_RESOURCE_ATTRIBUTES;\n    const serviceName = env.OTEL_SERVICE_NAME;\n\n    if (rawAttributes) {\n      try {\n        const parsedAttributes = this._parseResourceAttributes(rawAttributes);\n        Object.assign(attributes, parsedAttributes);\n      } catch (e) {\n        diag.debug(`EnvDetector failed: ${e.message}`);\n      }\n    }\n\n    if (serviceName) {\n      attributes[SEMRESATTRS_SERVICE_NAME] = serviceName;\n    }\n\n    return new Resource(attributes);\n  }\n\n  /**\n   * Creates an attribute map from the OTEL_RESOURCE_ATTRIBUTES environment\n   * variable.\n   *\n   * OTEL_RESOURCE_ATTRIBUTES: A comma-separated list of attributes describing\n   * the source in more detail, e.g. “key1=val1,key2=val2”. Domain names and\n   * paths are accepted as attribute keys. Values may be quoted or unquoted in\n   * general. If a value contains whitespace, =, or \" characters, it must\n   * always be quoted.\n   *\n   * @param rawEnvAttributes The resource attributes as a comma-separated list\n   * of key/value pairs.\n   * @returns The sanitized resource attributes.\n   */\n  private _parseResourceAttributes(\n    rawEnvAttributes?: string\n  ): ResourceAttributes {\n    if (!rawEnvAttributes) return {};\n\n    const attributes: ResourceAttributes = {};\n    const rawAttributes: string[] = rawEnvAttributes.split(\n      this._COMMA_SEPARATOR,\n      -1\n    );\n    for (const rawAttribute of rawAttributes) {\n      const keyValuePair: string[] = rawAttribute.split(\n        this._LABEL_KEY_VALUE_SPLITTER,\n        -1\n      );\n      if (keyValuePair.length !== 2) {\n        continue;\n      }\n      let [key, value] = keyValuePair;\n      // Leading and trailing whitespaces are trimmed.\n      key = key.trim();\n      value = value.trim().split(/^\"|\"$/).join('');\n      if (!this._isValidAndNotEmpty(key)) {\n        throw new Error(`Attribute key ${this._ERROR_MESSAGE_INVALID_CHARS}`);\n      }\n      if (!this._isValid(value)) {\n        throw new Error(`Attribute value ${this._ERROR_MESSAGE_INVALID_VALUE}`);\n      }\n      attributes[key] = decodeURIComponent(value);\n    }\n    return attributes;\n  }\n\n  /**\n   * Determines whether the given String is a valid printable ASCII string with\n   * a length not exceed _MAX_LENGTH characters.\n   *\n   * @param str The String to be validated.\n   * @returns Whether the String is valid.\n   */\n  private _isValid(name: string): boolean {\n    return name.length <= this._MAX_LENGTH && this._isBaggageOctetString(name);\n  }\n\n  // https://www.w3.org/TR/baggage/#definition\n  private _isBaggageOctetString(str: string): boolean {\n    for (let i = 0; i < str.length; i++) {\n      const ch = str.charCodeAt(i);\n      if (ch < 0x21 || ch === 0x2c || ch === 0x3b || ch === 0x5c || ch > 0x7e) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  /**\n   * Determines whether the given String is a valid printable ASCII string with\n   * a length greater than 0 and not exceed _MAX_LENGTH characters.\n   *\n   * @param str The String to be validated.\n   * @returns Whether the String is valid and not empty.\n   */\n  private _isValidAndNotEmpty(str: string): boolean {\n    return str.length > 0 && this._isValid(str);\n  }\n}\n\nexport const envDetectorSync = new EnvDetectorSync();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,wBAAwB,EAAE,MAAM,qCAAqC,CAAC;AAC/E,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKvC;;;GAGG,CACH,IAAA,kBAAA;IAAA,SAAA;QACE,+EAA+E;QAC9D,IAAA,CAAA,WAAW,GAAG,GAAG,CAAC;QAEnC,oEAAoE;QACnD,IAAA,CAAA,gBAAgB,GAAG,GAAG,CAAC;QAExC,qEAAqE;QACpD,IAAA,CAAA,yBAAyB,GAAG,GAAG,CAAC;QAEhC,IAAA,CAAA,4BAA4B,GAC3C,uEAAuE,GACvE,IAAI,CAAC,WAAW,GAChB,cAAc,CAAC;QAEA,IAAA,CAAA,4BAA4B,GAC3C,oDAAoD,GACpD,IAAI,CAAC,WAAW,GAChB,cAAc,CAAC;IA+GnB,CAAC;IA7GC;;;;;;OAMG,CACH,gBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAiC;QACtC,IAAM,UAAU,GAAuB,CAAA,CAAE,CAAC;QAC1C,IAAM,GAAG,OAAG,sMAAA,AAAM,EAAE,CAAC;QAErB,IAAM,aAAa,GAAG,GAAG,CAAC,wBAAwB,CAAC;QACnD,IAAM,WAAW,GAAG,GAAG,CAAC,iBAAiB,CAAC;QAE1C,IAAI,aAAa,EAAE;YACjB,IAAI;gBACF,IAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;gBACtE,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;aAC7C,CAAC,OAAO,CAAC,EAAE;sJACV,OAAI,CAAC,KAAK,CAAC,yBAAuB,CAAC,CAAC,OAAS,CAAC,CAAC;aAChD;SACF;QAED,IAAI,WAAW,EAAE;YACf,UAAU,wNAAC,2BAAwB,CAAC,GAAG,WAAW,CAAC;SACpD;QAED,OAAO,+KAAI,WAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IAED;;;;;;;;;;;;;OAaG,CACK,gBAAA,SAAA,CAAA,wBAAwB,GAAhC,SACE,gBAAyB;;QAEzB,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAA,CAAE,CAAC;QAEjC,IAAM,UAAU,GAAuB,CAAA,CAAE,CAAC;QAC1C,IAAM,aAAa,GAAa,gBAAgB,CAAC,KAAK,CACpD,IAAI,CAAC,gBAAgB,EACrB,CAAC,CAAC,CACH,CAAC;;YACF,IAA2B,IAAA,kBAAA,SAAA,aAAa,CAAA,EAAA,oBAAA,gBAAA,IAAA,EAAA,EAAA,CAAA,kBAAA,IAAA,EAAA,oBAAA,gBAAA,IAAA,GAAE;gBAArC,IAAM,YAAY,GAAA,kBAAA,KAAA;gBACrB,IAAM,YAAY,GAAa,YAAY,CAAC,KAAK,CAC/C,IAAI,CAAC,yBAAyB,EAC9B,CAAC,CAAC,CACH,CAAC;gBACF,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC7B,SAAS;iBACV;gBACG,IAAA,KAAA,OAAe,YAAY,EAAA,EAAA,EAA1B,GAAG,GAAA,EAAA,CAAA,EAAA,EAAE,KAAK,GAAA,EAAA,CAAA,EAAgB,CAAC;gBAChC,gDAAgD;gBAChD,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;gBACjB,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC7C,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE;oBAClC,MAAM,IAAI,KAAK,CAAC,mBAAiB,IAAI,CAAC,4BAA8B,CAAC,CAAC;iBACvE;gBACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;oBACzB,MAAM,IAAI,KAAK,CAAC,qBAAmB,IAAI,CAAC,4BAA8B,CAAC,CAAC;iBACzE;gBACD,UAAU,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;aAC7C;;;;;;;;;;;;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;;;OAMG,CACK,gBAAA,SAAA,CAAA,QAAQ,GAAhB,SAAiB,IAAY;QAC3B,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAC7E,CAAC;IAED,4CAA4C;IACpC,gBAAA,SAAA,CAAA,qBAAqB,GAA7B,SAA8B,GAAW;QACvC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YACnC,IAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,EAAE,GAAG,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,GAAG,IAAI,EAAE;gBACvE,OAAO,KAAK,CAAC;aACd;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG,CACK,gBAAA,SAAA,CAAA,mBAAmB,GAA3B,SAA4B,GAAW;QACrC,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;IACH,OAAA,eAAC;AAAD,CAAC,AAjID,IAiIC;AAEM,IAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "file": "EnvDetector.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/EnvDetector.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Detector } from '../types';\nimport { ResourceDetectionConfig } from '../config';\nimport { IResource } from '../IResource';\nimport { envDetectorSync } from './EnvDetectorSync';\n\n/**\n * EnvDetector can be used to detect the presence of and create a Resource\n * from the OTEL_RESOURCE_ATTRIBUTES environment variable.\n */\nclass EnvDetector implements Detector {\n  /**\n   * Returns a {@link Resource} populated with attributes from the\n   * OTEL_RESOURCE_ATTRIBUTES environment variable. Note this is an async\n   * function to conform to the Detector interface.\n   *\n   * @param config The resource detection config\n   */\n  detect(config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(envDetectorSync.detect(config));\n  }\n}\n\nexport const envDetector = new EnvDetector();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;;AAEpD;;;GAGG,CACH,IAAA,cAAA;IAAA,SAAA,eAWA,CAAC;IAVC;;;;;;OAMG,CACH,YAAA,SAAA,CAAA,MAAM,GAAN,SAAO,MAAgC;QACrC,OAAO,OAAO,CAAC,OAAO,gMAAC,kBAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IACzD,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAXD,IAWC;AAEM,IAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport const normalizeArch = (nodeArchString: string): string => {\n  // Maps from https://nodejs.org/api/os.html#osarch to arch values in spec:\n  // https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/resource/semantic_conventions/host.md\n  switch (nodeArchString) {\n    case 'arm':\n      return 'arm32';\n    case 'ppc':\n      return 'ppc32';\n    case 'x64':\n      return 'amd64';\n    default:\n      return nodeArchString;\n  }\n};\n\nexport const normalizeType = (nodePlatform: string): string => {\n  // Maps from https://nodejs.org/api/os.html#osplatform to arch values in spec:\n  // https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/resource/semantic_conventions/os.md\n  switch (nodePlatform) {\n    case 'sunos':\n      return 'solaris';\n    case 'win32':\n      return 'windows';\n    default:\n      return nodePlatform;\n  }\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AACI,IAAM,aAAa,GAAG,SAAC,cAAsB;IAClD,0EAA0E;IAC1E,8HAA8H;IAC9H,OAAQ,cAAc,EAAE;QACtB,KAAK,KAAK;YACR,OAAO,OAAO,CAAC;QACjB,KAAK,KAAK;YACR,OAAO,OAAO,CAAC;QACjB,KAAK,KAAK;YACR,OAAO,OAAO,CAAC;QACjB;YACE,OAAO,cAAc,CAAC;KACzB;AACH,CAAC,CAAC;AAEK,IAAM,aAAa,GAAG,SAAC,YAAoB;IAChD,8EAA8E;IAC9E,4HAA4H;IAC5H,OAAQ,YAAY,EAAE;QACpB,KAAK,OAAO;YACV,OAAO,SAAS,CAAC;QACnB,KAAK,OAAO;YACV,OAAO,SAAS,CAAC;QACnB;YACE,OAAO,YAAY,CAAC;KACvB;AACH,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "file": "execAsync.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/machine-id/execAsync.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as child_process from 'child_process';\nimport * as util from 'util';\n\nexport const execAsync = util.promisify(child_process.exec);\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,KAAK,aAAa,MAAM,eAAe,CAAC;AAC/C,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;;;AAEtB,IAAM,SAAS,yGAAG,IAAI,CAAC,OAAA,AAAS,sHAAC,OAAkB,CAAC,CAAC,IAAP,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "file": "getMachineId-darwin.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/machine-id/getMachineId-darwin.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { execAsync } from './execAsync';\nimport { diag } from '@opentelemetry/api';\n\nexport async function getMachineId(): Promise<string> {\n  try {\n    const result = await execAsync('ioreg -rd1 -c \"IOPlatformExpertDevice\"');\n\n    const idLine = result.stdout\n      .split('\\n')\n      .find(line => line.includes('IOPlatformUUID'));\n\n    if (!idLine) {\n      return '';\n    }\n\n    const parts = idLine.split('\" = \"');\n    if (parts.length === 2) {\n      return parts[1].slice(0, -1);\n    }\n  } catch (e) {\n    diag.debug(`error reading machine id: ${e}`);\n  }\n\n  return '';\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpC,SAAgB,YAAY;;;;;;;;;;;;oBAEf,OAAA;wBAAA,EAAA,OAAA;0PAAM,YAAA,AAAS,EAAC,wCAAwC,CAAC;qBAAA,CAAA;;oBAAlE,MAAM,GAAG,GAAA,IAAA,EAAyD;oBAElE,MAAM,GAAG,MAAM,CAAC,MAAM,CACzB,KAAK,CAAC,IAAI,CAAC,CACX,IAAI,CAAC,SAAA,IAAI;wBAAI,OAAA,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;oBAA/B,CAA+B,CAAC,CAAC;oBAEjD,IAAI,CAAC,MAAM,EAAE;wBACX,OAAA;4BAAA,EAAA,QAAA;4BAAO,EAAE;yBAAA,CAAC;qBACX;oBAEK,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;oBACpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;wBACtB,OAAA;4BAAA,EAAA,QAAA;4BAAO,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;yBAAA,CAAC;qBAC9B;;;;;;;oBAED,6IAAI,CAAC,KAAK,CAAC,+BAA6B,GAAG,CAAC,CAAC;;;;;;oBAG/C,OAAA;wBAAA,EAAA,QAAA;wBAAO,EAAE;qBAAA,CAAC;;;;CACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 970, "column": 0}, "map": {"version": 3, "file": "getMachineId-linux.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/machine-id/getMachineId-linux.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { promises as fs } from 'fs';\nimport { diag } from '@opentelemetry/api';\n\nexport async function getMachineId(): Promise<string> {\n  const paths = ['/etc/machine-id', '/var/lib/dbus/machine-id'];\n\n  for (const path of paths) {\n    try {\n      const result = await fs.readFile(path, { encoding: 'utf8' });\n      return result.trim();\n    } catch (e) {\n      diag.debug(`error reading machine id: ${e}`);\n    }\n  }\n\n  return '';\n}\n"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG,CACH,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpC,SAAgB,YAAY;;;;;;;oBAC1B,KAAK,GAAG;wBAAC,iBAAiB;wBAAE,0BAA0B;qBAAC,CAAC;;;;;;;;;oBAE3C,UAAA,SAAA,KAAK,CAAA,EAAA,YAAA,QAAA,IAAA;;;;;;;oBAAb,IAAI,GAAA,UAAA,KAAA;;;;;;;;;oBAEI,OAAA;wBAAA,EAAA,OAAA;sHAAM,WAAE,CAAC,QAAQ,CAAC,IAAI,EAAE;4BAAE,QAAQ,EAAE,MAAM;wBAAA,CAAE,CAAC;qBAAA,CAAA;;oBAAtD,MAAM,GAAG,GAAA,IAAA,EAA6C;oBAC5D,OAAA;wBAAA,EAAA,QAAA;wBAAO,MAAM,CAAC,IAAI,EAAE;qBAAA,CAAC;;;0JAErB,OAAI,CAAC,KAAK,CAAC,+BAA6B,GAAG,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAIjD,OAAA;wBAAA,EAAA,QAAA;wBAAO,EAAE;qBAAA,CAAC;;;;CACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1226, "column": 0}, "map": {"version": 3, "file": "getMachineId-bsd.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/machine-id/getMachineId-bsd.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { promises as fs } from 'fs';\nimport { execAsync } from './execAsync';\nimport { diag } from '@opentelemetry/api';\n\nexport async function getMachineId(): Promise<string> {\n  try {\n    const result = await fs.readFile('/etc/hostid', { encoding: 'utf8' });\n    return result.trim();\n  } catch (e) {\n    diag.debug(`error reading machine id: ${e}`);\n  }\n\n  try {\n    const result = await execAsync('kenv -q smbios.system.uuid');\n    return result.stdout.trim();\n  } catch (e) {\n    diag.debug(`error reading machine id: ${e}`);\n  }\n\n  return '';\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpC,SAAgB,YAAY;;;;;;;;;;;;oBAEf,OAAA;wBAAA,EAAA,OAAA;sHAAM,WAAE,CAAC,QAAQ,CAAC,aAAa,EAAE;4BAAE,QAAQ,EAAE,MAAM;wBAAA,CAAE,CAAC;qBAAA,CAAA;;oBAA/D,MAAM,GAAG,GAAA,IAAA,EAAsD;oBACrE,OAAA;wBAAA,EAAA,QAAA;wBAAO,MAAM,CAAC,IAAI,EAAE;qBAAA,CAAC;;;0JAErB,OAAI,CAAC,KAAK,CAAC,+BAA6B,GAAG,CAAC,CAAC;;;;;;;;;;;;oBAI9B,OAAA;wBAAA,EAAA,OAAA;wBAAM,8OAAA,AAAS,EAAC,4BAA4B,CAAC;qBAAA,CAAA;;oBAAtD,MAAM,GAAG,GAAA,IAAA,EAA6C;oBAC5D,OAAA;wBAAA,EAAA,QAAA;wBAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;qBAAA,CAAC;;;0JAE5B,OAAI,CAAC,KAAK,CAAC,+BAA6B,GAAG,CAAC,CAAC;;;;;;oBAG/C,OAAA;wBAAA,EAAA,QAAA;wBAAO,EAAE;qBAAA,CAAC;;;;CACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1442, "column": 0}, "map": {"version": 3, "file": "getMachineId-win.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/machine-id/getMachineId-win.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as process from 'process';\nimport { execAsync } from './execAsync';\nimport { diag } from '@opentelemetry/api';\n\nexport async function getMachineId(): Promise<string> {\n  const args =\n    'QUERY HKEY_LOCAL_MACHINE\\\\SOFTWARE\\\\Microsoft\\\\Cryptography /v MachineGuid';\n  let command = '%windir%\\\\System32\\\\REG.exe';\n  if (process.arch === 'ia32' && 'PROCESSOR_ARCHITEW6432' in process.env) {\n    command = '%windir%\\\\sysnative\\\\cmd.exe /c ' + command;\n  }\n\n  try {\n    const result = await execAsync(`${command} ${args}`);\n    const parts = result.stdout.split('REG_SZ');\n    if (parts.length === 2) {\n      return parts[1].trim();\n    }\n  } catch (e) {\n    diag.debug(`error reading machine id: ${e}`);\n  }\n\n  return '';\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,KAAK,OAAO,MAAM,SAAS,CAAC;AACnC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpC,SAAgB,YAAY;;;;;;oBAC1B,IAAI,GACR,4EAA4E,CAAC;oBAC3E,OAAO,GAAG,6BAA6B,CAAC;oBAC5C,4GAAI,OAAO,AAAK,CAAJ,IAAS,MAAM,IAAI,wBAAwB,4GAAI,MAAW,CAAJ,CAAC,AAAK;wBACtE,OAAO,GAAG,kCAAkC,GAAG,OAAO,CAAC;qBACxD;;;;;;;;;oBAGgB,OAAA;wBAAA,EAAA,OAAA;yBAAM,6OAAA,AAAS,EAAI,OAAO,GAAA,MAAI,IAAM,CAAC;qBAAA,CAAA;;oBAA9C,MAAM,GAAG,GAAA,IAAA,EAAqC;oBAC9C,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBAC5C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;wBACtB,OAAA;4BAAA,EAAA,QAAA;4BAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;yBAAA,CAAC;qBACxB;;;;;;;oBAED,6IAAI,CAAC,KAAK,CAAC,+BAA6B,GAAG,CAAC,CAAC;;;;;;oBAG/C,OAAA;wBAAA,EAAA,QAAA;wBAAO,EAAE;qBAAA,CAAC;;;;CACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1646, "column": 0}, "map": {"version": 3, "file": "getMachineId-unsupported.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/machine-id/getMachineId-unsupported.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\n\nexport async function getMachineId(): Promise<string> {\n  diag.debug('could not read machine-id: unsupported platform');\n  return '';\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpC,SAAgB,YAAY;;;kJAChC,OAAI,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;YAC9D,OAAA;gBAAA,EAAA,QAAA;gBAAO,EAAE;aAAA,CAAC;;;CACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1805, "column": 0}, "map": {"version": 3, "file": "getMachineId.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/machine-id/getMachineId.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as process from 'process';\n\nlet getMachineId: () => Promise<string>;\n\nswitch (process.platform) {\n  case 'darwin':\n    ({ getMachineId } = require('./getMachineId-darwin'));\n    break;\n  case 'linux':\n    ({ getMachineId } = require('./getMachineId-linux'));\n    break;\n  case 'freebsd':\n    ({ getMachineId } = require('./getMachineId-bsd'));\n    break;\n  case 'win32':\n    ({ getMachineId } = require('./getMachineId-win'));\n    break;\n  default:\n    ({ getMachineId } = require('./getMachineId-unsupported'));\n}\n\nexport { getMachineId };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AACH,OAAO,KAAK,OAAO,MAAM,SAAS,CAAC;;AAEnC,IAAI,YAAmC,CAAC;AAExC,+GAAQ,OAAO,CAAC,GAAQ,EAAE;IACxB,KAAK,QAAQ;QACR,YAAY,GAAK,OAAO,CAAC,uBAAuB,CAAC,wIAAA,YAArC,CAAsC,CAAC;QACtD,MAAM;IACR,KAAK,OAAO;QACP,YAAY,GAAK,OAAO,CAAC,sBAAsB,CAAC,wIAAA,YAApC,CAAqC,CAAC;QACrD,MAAM;IACR,KAAK,SAAS;QACT,YAAY,GAAK,OAAO,CAAC,oBAAoB,CAAC,wIAAA,YAAlC,CAAmC,CAAC;QACnD,MAAM;IACR,KAAK,OAAO;QACP,YAAY,GAAK,OAAO,CAAC,oBAAoB,CAAC,wIAAA,YAAlC,CAAmC,CAAC;QACnD,MAAM;IACR;QACK,YAAY,GAAK,OAAO,CAAC,4BAA4B,CAAC,wIAAA,YAA1C,CAA2C,CAAC;CAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1849, "column": 0}, "map": {"version": 3, "file": "HostDetectorSync.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/HostDetectorSync.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  SEMRESATTRS_HOST_ARCH,\n  SEMRESATTRS_HOST_ID,\n  SEMRESATTRS_HOST_NAME,\n} from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../../../Resource';\nimport { DetectorSync, ResourceAttributes } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { arch, hostname } from 'os';\nimport { normalizeArch } from './utils';\nimport { getMachineId } from './machine-id/getMachineId';\n\n/**\n * HostDetectorSync detects the resources related to the host current process is\n * running on. Currently only non-cloud-based attributes are included.\n */\nclass HostDetectorSync implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): Resource {\n    const attributes: ResourceAttributes = {\n      [SEMRESATTRS_HOST_NAME]: hostname(),\n      [SEMRESATTRS_HOST_ARCH]: normalizeArch(arch()),\n    };\n\n    return new Resource(attributes, this._getAsyncAttributes());\n  }\n\n  private _getAsyncAttributes(): Promise<ResourceAttributes> {\n    return getMachineId().then(machineId => {\n      const attributes: ResourceAttributes = {};\n      if (machineId) {\n        attributes[SEMRESATTRS_HOST_ID] = machineId;\n      }\n      return attributes;\n    });\n  }\n}\n\nexport const hostDetectorSync = new HostDetectorSync();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,qBAAqB,EACrB,mBAAmB,EACnB,qBAAqB,GACtB,MAAM,qCAAqC,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAG7C,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;;;;;;AAEzD;;;GAGG,CACH,IAAA,mBAAA;IAAA,SAAA,oBAmBA,CAAC;IAlBC,iBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAiC;;QACtC,IAAM,UAAU,GAAA,CAAA,KAAA,CAAA,GACd,EAAA,wNAAC,wBAAqB,CAAA,GAAG,6GAAA,AAAQ,EAAE,GACnC,EAAA,wNAAC,wBAAqB,CAAA,gNAAG,gBAAA,AAAa,oGAAC,OAAA,AAAI,EAAE,CAAC,MAC/C,CAAC;QAEF,OAAO,8KAAI,YAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;IAC9D,CAAC;IAEO,iBAAA,SAAA,CAAA,mBAAmB,GAA3B;QACE,4OAAO,eAAA,AAAY,EAAE,EAAC,IAAI,CAAC,SAAA,SAAS;YAClC,IAAM,UAAU,GAAuB,CAAA,CAAE,CAAC;YAC1C,IAAI,SAAS,EAAE;gBACb,UAAU,wNAAC,sBAAmB,CAAC,GAAG,SAAS,CAAC;aAC7C;YACD,OAAO,UAAU,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AAnBD,IAmBC;AAEM,IAAM,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1904, "column": 0}, "map": {"version": 3, "file": "HostDetector.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/HostDetector.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Detector } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { IResource } from '../../../IResource';\nimport { hostDetectorSync } from './HostDetectorSync';\n\n/**\n * HostDetector detects the resources related to the host current process is\n * running on. Currently only non-cloud-based attributes are included.\n */\nclass HostDetector implements Detector {\n  detect(_config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(hostDetectorSync.detect(_config));\n  }\n}\n\nexport const hostDetector = new HostDetector();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;;AAEtD;;;GAGG,CACH,IAAA,eAAA;IAAA,SAAA,gBAIA,CAAC;IAHC,aAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAiC;QACtC,OAAO,OAAO,CAAC,OAAO,qNAAC,mBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,YAAC;AAAD,CAAC,AAJD,IAIC;AAEM,IAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1940, "column": 0}, "map": {"version": 3, "file": "OSDetectorSync.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/OSDetectorSync.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  SEMRESATTRS_OS_TYPE,\n  SEMRESATTRS_OS_VERSION,\n} from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../../../Resource';\nimport { DetectorSync, ResourceAttributes } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { platform, release } from 'os';\nimport { normalizeType } from './utils';\n\n/**\n * OSDetectorSync detects the resources related to the operating system (OS) on\n * which the process represented by this resource is running.\n */\nclass OSDetectorSync implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): Resource {\n    const attributes: ResourceAttributes = {\n      [SEMRESATTRS_OS_TYPE]: normalizeType(platform()),\n      [SEMRESATTRS_OS_VERSION]: release(),\n    };\n    return new Resource(attributes);\n  }\n}\n\nexport const osDetectorSync = new OSDetectorSync();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mBAAmB,EACnB,sBAAsB,GACvB,MAAM,qCAAqC,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAG7C,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC;AACvC,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;;;;;AAExC;;;GAGG,CACH,IAAA,iBAAA;IAAA,SAAA,kBAQA,CAAC;IAPC,eAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAiC;;QACtC,IAAM,UAAU,GAAA,CAAA,KAAA,CAAA,GACd,EAAA,wNAAC,sBAAmB,CAAA,gNAAG,gBAAA,AAAa,GAAC,4GAAA,AAAQ,EAAE,CAAC,GAChD,EAAA,wNAAC,yBAAsB,CAAA,qGAAG,UAAA,AAAO,EAAE,MACpC,CAAC;QACF,OAAO,+KAAI,WAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IACH,OAAA,cAAC;AAAD,CAAC,AARD,IAQC;AAEM,IAAM,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1984, "column": 0}, "map": {"version": 3, "file": "OSDetector.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/OSDetector.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Detector } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { IResource } from '../../../IResource';\nimport { osDetectorSync } from './OSDetectorSync';\n\n/**\n * OSDetector detects the resources related to the operating system (OS) on\n * which the process represented by this resource is running.\n */\nclass OSDetector implements Detector {\n  detect(_config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(osDetectorSync.detect(_config));\n  }\n}\n\nexport const osDetector = new OSDetector();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;;AAElD;;;GAGG,CACH,IAAA,aAAA;IAAA,SAAA,cAIA,CAAC;IAHC,WAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAiC;QACtC,OAAO,OAAO,CAAC,OAAO,mNAAC,iBAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IACzD,CAAC;IACH,OAAA,UAAC;AAAD,CAAC,AAJD,IAIC;AAEM,IAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2020, "column": 0}, "map": {"version": 3, "file": "ProcessDetectorSync.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/ProcessDetectorSync.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport {\n  SEMRESATTRS_PROCESS_COMMAND,\n  SEMRESATTRS_PROCESS_COMMAND_ARGS,\n  SEMRESATTRS_PROCESS_EXECUTABLE_NAME,\n  SEMRESATTRS_PROCESS_EXECUTABLE_PATH,\n  SEMRESATTRS_PROCESS_OWNER,\n  SEMRESATTRS_PROCESS_PID,\n  SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION,\n  SEMRESATTRS_PROCESS_RUNTIME_NAME,\n  SEMRESATTRS_PROCESS_RUNTIME_VERSION,\n} from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../../../Resource';\nimport { DetectorSync, ResourceAttributes } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { IResource } from '../../../IResource';\nimport * as os from 'os';\n\n/**\n * ProcessDetectorSync will be used to detect the resources related current process running\n * and being instrumented from the NodeJS Process module.\n */\nclass ProcessDetectorSync implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): IResource {\n    const attributes: ResourceAttributes = {\n      [SEMRESATTRS_PROCESS_PID]: process.pid,\n      [SEMRESATTRS_PROCESS_EXECUTABLE_NAME]: process.title,\n      [SEMRESATTRS_PROCESS_EXECUTABLE_PATH]: process.execPath,\n      [SEMRESATTRS_PROCESS_COMMAND_ARGS]: [\n        process.argv[0],\n        ...process.execArgv,\n        ...process.argv.slice(1),\n      ],\n      [SEMRESATTRS_PROCESS_RUNTIME_VERSION]: process.versions.node,\n      [SEMRESATTRS_PROCESS_RUNTIME_NAME]: 'nodejs',\n      [SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION]: 'Node.js',\n    };\n\n    if (process.argv.length > 1) {\n      attributes[SEMRESATTRS_PROCESS_COMMAND] = process.argv[1];\n    }\n\n    try {\n      const userInfo = os.userInfo();\n      attributes[SEMRESATTRS_PROCESS_OWNER] = userInfo.username;\n    } catch (e) {\n      diag.debug(`error obtaining process owner: ${e}`);\n    }\n\n    return new Resource(attributes);\n  }\n}\n\nexport const processDetectorSync = new ProcessDetectorSync();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EACL,2BAA2B,EAC3B,gCAAgC,EAChC,mCAAmC,EACnC,mCAAmC,EACnC,yBAAyB,EACzB,uBAAuB,EACvB,uCAAuC,EACvC,gCAAgC,EAChC,mCAAmC,GACpC,MAAM,qCAAqC,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAI7C,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzB;;;GAGG,CACH,IAAA,sBAAA;IAAA,SAAA,uBA6BA,CAAC;IA5BC,oBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAiC;;QACtC,IAAM,UAAU,GAAA,CAAA,KAAA,CAAA,GACd,EAAA,wNAAC,0BAAuB,CAAA,GAAG,OAAO,CAAC,GAAG,EACtC,EAAA,wNAAC,sCAAmC,CAAA,GAAG,OAAO,CAAC,KAAK,EACpD,EAAA,wNAAC,sCAAmC,CAAA,GAAG,OAAO,CAAC,QAAQ,EACvD,EAAA,wNAAC,mCAAgC,CAAA,GAAA,cAAA,cAAA;YAC/B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;kBACZ,OAAO,CAAC,QAAQ,GAAA,QAAA,OAChB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAA,MACzB,EACD,EAAA,uNAAC,uCAAmC,CAAA,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,EAC5D,EAAA,wNAAC,mCAAgC,CAAA,GAAG,QAAQ,EAC5C,EAAA,CAAC,iQAAuC,CAAA,GAAG,SAAS,KACrD,CAAC;QAEF,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,UAAU,wNAAC,8BAA2B,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAC3D;QAED,IAAI;YACF,IAAM,QAAQ,qGAAG,EAAE,CAAC,QAAA,AAAQ,EAAE,CAAC;YAC/B,UAAU,CAAC,mPAAyB,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC;SAC3D,CAAC,OAAO,CAAC,EAAE;kJACV,OAAI,CAAC,KAAK,CAAC,oCAAkC,CAAG,CAAC,CAAC;SACnD;QAED,OAAO,+KAAI,WAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AA7BD,IA6BC;AAEM,IAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2103, "column": 0}, "map": {"version": 3, "file": "ProcessDetector.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/ProcessDetector.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Detector } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { IResource } from '../../../IResource';\nimport { processDetectorSync } from './ProcessDetectorSync';\n\n/**\n * ProcessDetector will be used to detect the resources related current process running\n * and being instrumented from the NodeJS Process module.\n */\nclass ProcessDetector implements Detector {\n  detect(config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(processDetectorSync.detect(config));\n  }\n}\n\nexport const processDetector = new ProcessDetector();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;;AAE5D;;;GAGG,CACH,IAAA,kBAAA;IAAA,SAAA,mBAIA,CAAC;IAHC,gBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,MAAgC;QACrC,OAAO,OAAO,CAAC,OAAO,wNAAC,sBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7D,CAAC;IACH,OAAA,eAAC;AAAD,CAAC,AAJD,IAIC;AAEM,IAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2139, "column": 0}, "map": {"version": 3, "file": "ServiceInstanceIdDetectorSync.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detectors/platform/node/ServiceInstanceIdDetectorSync.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SEMRESATTRS_SERVICE_INSTANCE_ID } from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../../../Resource';\nimport { DetectorSync, ResourceAttributes } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { randomUUID } from 'crypto';\n\n/**\n * ServiceInstanceIdDetectorSync detects the resources related to the service instance ID.\n */\nclass ServiceInstanceIdDetectorSync implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): Resource {\n    const attributes: ResourceAttributes = {\n      [SEMRESATTRS_SERVICE_INSTANCE_ID]: randomUUID(),\n    };\n\n    return new Resource(attributes);\n  }\n}\n\n/**\n * @experimental\n */\nexport const serviceInstanceIdDetectorSync =\n  new ServiceInstanceIdDetectorSync();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,+BAA+B,EAAE,MAAM,qCAAqC,CAAC;AACtF,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAG7C,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;;;;AAEpC;;GAEG,CACH,IAAA,gCAAA;IAAA,SAAA,iCAQA,CAAC;IAPC,8BAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAiC;;QACtC,IAAM,UAAU,GAAA,CAAA,KAAA,CAAA,GACd,EAAA,wNAAC,kCAA+B,CAAA,6GAAG,aAAA,AAAU,EAAE,MAChD,CAAC;QAEF,OAAO,+KAAI,WAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IACH,OAAA,6BAAC;AAAD,CAAC,AARD,IAQC;AAKM,IAAM,6BAA6B,GACxC,IAAI,6BAA6B,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2180, "column": 0}, "map": {"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const isPromiseLike = <R>(val: any): val is PromiseLike<R> => {\n  return (\n    val !== null && typeof val === 'object' && typeof val.then === 'function'\n  );\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEI,IAAM,aAAa,GAAG,SAAI,GAAQ;IACvC,OAAO,AACL,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,UAAU,CAC1E,CAAC;AACJ,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2206, "column": 0}, "map": {"version": 3, "file": "detect-resources.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/node_modules/%40opentelemetry/resources/src/detect-resources.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Resource } from './Resource';\nimport { ResourceDetectionConfig } from './config';\nimport { diag } from '@opentelemetry/api';\nimport { isPromiseLike } from './utils';\nimport { Detector, DetectorSync } from './types';\nimport { IResource } from './IResource';\n\n/**\n * Runs all resource detectors and returns the results merged into a single Resource. Promise\n * does not resolve until all the underlying detectors have resolved, unlike\n * detectResourcesSync.\n *\n * @deprecated use detectResourcesSync() instead.\n * @param config Configuration for resource detection\n */\nexport const detectResources = async (\n  config: ResourceDetectionConfig = {}\n): Promise<IResource> => {\n  const resources: IResource[] = await Promise.all(\n    (config.detectors || []).map(async d => {\n      try {\n        const resource = await d.detect(config);\n        diag.debug(`${d.constructor.name} found resource.`, resource);\n        return resource;\n      } catch (e) {\n        diag.debug(`${d.constructor.name} failed: ${e.message}`);\n        return Resource.empty();\n      }\n    })\n  );\n\n  // Future check if verbose logging is enabled issue #1903\n  logResources(resources);\n\n  return resources.reduce(\n    (acc, resource) => acc.merge(resource),\n    Resource.empty()\n  );\n};\n\n/**\n * Runs all resource detectors synchronously, merging their results. In case of attribute collision later resources will take precedence.\n *\n * @param config Configuration for resource detection\n */\nexport const detectResourcesSync = (\n  config: ResourceDetectionConfig = {}\n): IResource => {\n  const resources: IResource[] = (config.detectors ?? []).map(\n    (d: Detector | DetectorSync) => {\n      try {\n        const resourceOrPromise = d.detect(config);\n        let resource: IResource;\n        if (isPromiseLike<Resource>(resourceOrPromise)) {\n          const createPromise = async () => {\n            const resolvedResource = await resourceOrPromise;\n            return resolvedResource.attributes;\n          };\n          resource = new Resource({}, createPromise());\n        } else {\n          resource = resourceOrPromise as IResource;\n        }\n\n        if (resource.waitForAsyncAttributes) {\n          void resource\n            .waitForAsyncAttributes()\n            .then(() =>\n              diag.debug(`${d.constructor.name} found resource.`, resource)\n            );\n        } else {\n          diag.debug(`${d.constructor.name} found resource.`, resource);\n        }\n\n        return resource;\n      } catch (e) {\n        diag.error(`${d.constructor.name} failed: ${e.message}`);\n        return Resource.empty();\n      }\n    }\n  );\n\n  const mergedResources = resources.reduce(\n    (acc, resource) => acc.merge(resource),\n    Resource.empty()\n  );\n\n  if (mergedResources.waitForAsyncAttributes) {\n    void mergedResources.waitForAsyncAttributes().then(() => {\n      // Future check if verbose logging is enabled issue #1903\n      logResources(resources);\n    });\n  }\n\n  return mergedResources;\n};\n\n/**\n * Writes debug information about the detected resources to the logger defined in the resource detection config, if one is provided.\n *\n * @param resources The array of {@link Resource} that should be logged. Empty entries will be ignored.\n */\nconst logResources = (resources: Array<IResource>) => {\n  resources.forEach(resource => {\n    // Print only populated resources\n    if (Object.keys(resource.attributes).length > 0) {\n      const resourceDebugString = JSON.stringify(resource.attributes, null, 4);\n      diag.verbose(resourceDebugString);\n    }\n  });\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAEtC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYjC,IAAM,eAAe,GAAG,SAC7B,MAAoC;IAApC,IAAA,WAAA,KAAA,GAAA;QAAA,SAAA,CAAA,CAAoC;IAAA;;;;;;oBAEL,OAAA;wBAAA,EAAA,OAAA;wBAAM,OAAO,CAAC,GAAG,CAC9C,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,SAAM,CAAC;4BAAA,OAAA,UAAA,KAAA,GAAA,KAAA,GAAA,KAAA,GAAA;;;;;;;;;;;4CAEf,OAAA;gDAAA,EAAA,OAAA;gDAAM,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;6CAAA,CAAA;;4CAAjC,QAAQ,GAAG,GAAA,IAAA,EAAsB;kLACvC,OAAI,CAAC,KAAK,CAAI,CAAC,CAAC,WAAW,CAAC,IAAI,GAAA,kBAAkB,EAAE,QAAQ,CAAC,CAAC;4CAC9D,OAAA;gDAAA,EAAA,QAAA;gDAAO,QAAQ;6CAAA,CAAC;;;kLAEhB,OAAI,CAAC,KAAK,CAAI,CAAC,CAAC,WAAW,CAAC,IAAI,GAAA,cAAY,GAAC,CAAC,OAAS,CAAC,CAAC;4CACzD,OAAA;gDAAA,EAAA,QAAA;2NAAO,WAAQ,CAAC,KAAK,EAAE;6CAAA,CAAC;;;;;;;;yBAE3B,CAAC,CACH;qBAAA,CAAA;;oBAXK,SAAS,GAAgB,GAAA,IAAA,EAW9B;oBAED,yDAAyD;oBACzD,YAAY,CAAC,SAAS,CAAC,CAAC;oBAExB,OAAA;wBAAA,EAAA,QAAA;wBAAO,SAAS,CAAC,MAAM,CACrB,SAAC,GAAG,EAAE,QAAQ;4BAAK,OAAA,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC;wBAAnB,CAAmB,6KACtC,WAAQ,CAAC,KAAK,EAAE,CACjB;qBAAA,CAAC;;;;CACH,CAAC;AAOK,IAAM,mBAAmB,GAAG,SACjC,MAAoC;;IAApC,IAAA,WAAA,KAAA,GAAA;QAAA,SAAA,CAAA,CAAoC;IAAA;IAEpC,IAAM,SAAS,GAAgB,CAAC,CAAA,KAAA,MAAM,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC,CAAC,GAAG,CACzD,SAAC,CAA0B;QACzB,IAAI;YACF,IAAM,mBAAiB,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAI,UAAmB,CAAC;YACxB,gLAAI,gBAAA,AAAa,EAAW,mBAAiB,CAAC,EAAE;gBAC9C,IAAM,aAAa,GAAG;oBAAA,OAAA,UAAA,KAAA,GAAA,KAAA,GAAA,KAAA,GAAA;;;;;oCACK,OAAA;wCAAA,EAAA,OAAA;wCAAM,mBAAiB;qCAAA,CAAA;;oCAA1C,gBAAgB,GAAG,GAAA,IAAA,EAAuB;oCAChD,OAAA;wCAAA,EAAA,QAAA;wCAAO,gBAAgB,CAAC,UAAU;qCAAA,CAAC;;;;iBACpC,CAAC;gBACF,UAAQ,GAAG,+KAAI,WAAQ,CAAC,CAAA,CAAE,EAAE,aAAa,EAAE,CAAC,CAAC;aAC9C,MAAM;gBACL,UAAQ,GAAG,mBAA8B,CAAC;aAC3C;YAED,IAAI,UAAQ,CAAC,sBAAsB,EAAE;gBACnC,KAAK,UAAQ,CACV,sBAAsB,EAAE,CACxB,IAAI,CAAC;oBACJ,6IAAA,OAAI,CAAC,KAAK,CAAI,CAAC,CAAC,WAAW,CAAC,IAAI,GAAA,kBAAkB,EAAE,UAAQ,CAAC;gBAA7D,CAA6D,CAC9D,CAAC;aACL,MAAM;sJACL,OAAI,CAAC,KAAK,CAAI,CAAC,CAAC,WAAW,CAAC,IAAI,GAAA,kBAAkB,EAAE,UAAQ,CAAC,CAAC;aAC/D;YAED,OAAO,UAAQ,CAAC;SACjB,CAAC,OAAO,CAAC,EAAE;iJACV,QAAI,CAAC,KAAK,CAAI,CAAC,CAAC,WAAW,CAAC,IAAI,GAAA,cAAY,CAAC,CAAC,OAAS,CAAC,CAAC;YACzD,kLAAO,WAAQ,CAAC,KAAK,EAAE,CAAC;SACzB;IACH,CAAC,CACF,CAAC;IAEF,IAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CACtC,SAAC,GAAG,EAAE,QAAQ;QAAK,OAAA,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC;IAAnB,CAAmB,6KACtC,WAAQ,CAAC,KAAK,EAAE,CACjB,CAAC;IAEF,IAAI,eAAe,CAAC,sBAAsB,EAAE;QAC1C,KAAK,eAAe,CAAC,sBAAsB,EAAE,CAAC,IAAI,CAAC;YACjD,yDAAyD;YACzD,YAAY,CAAC,SAAS,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,eAAe,CAAC;AACzB,CAAC,CAAC;AAEF;;;;GAIG,CACH,IAAM,YAAY,GAAG,SAAC,SAA2B;IAC/C,SAAS,CAAC,OAAO,CAAC,SAAA,QAAQ;QACxB,iCAAiC;QACjC,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/C,IAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;kJACzE,OAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;SACnC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "ignoreList": [0], "debugId": null}}]}