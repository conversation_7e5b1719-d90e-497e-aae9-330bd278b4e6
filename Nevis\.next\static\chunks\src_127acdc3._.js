(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/firebase/services/generated-post-service.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/lib/firebase/services/generated-post-service.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/components/app-route/AppRoute.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_app_adff3dbc._.js",
  "static/chunks/src_components_app-route_AppRoute_tsx_ff968e62._.js",
  "static/chunks/src_components_app-route_AppRoute_tsx_6e776e7b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/app-route/AppRoute.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/lib/test-database.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_38a6758c._.js",
  "static/chunks/src_lib_9775bc79._.js",
  "static/chunks/src_lib_test-database_ts_6e776e7b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/test-database.ts [app-client] (ecmascript)");
    });
});
}}),
}]);