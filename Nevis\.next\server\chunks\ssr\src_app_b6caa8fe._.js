module.exports = {

"[project]/src/app/actions.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/app/actions.ts
__turbopack_context__.s({});
"use turbopack no side effects";
;
;
;
;
;
;
;
}}),
"[project]/src/app/actions.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/app/data:8fd537 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"60d74caca804adb2b547ce0cf49c6b4a219392377c":"analyzeBrandAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "analyzeBrandAction": (()=>analyzeBrandAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var analyzeBrandAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("60d74caca804adb2b547ce0cf49c6b4a219392377c", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "analyzeBrandAction"); //# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4vYWN0aW9ucy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvYXBwL2FjdGlvbnMudHNcclxuXCJ1c2Ugc2VydmVyXCI7XHJcblxyXG5pbXBvcnQgeyBhbmFseXplQnJhbmQgYXMgYW5hbHl6ZUJyYW5kRmxvdywgQnJhbmRBbmFseXNpc1Jlc3VsdCB9IGZyb20gXCJAL2FpL2Zsb3dzL2FuYWx5emUtYnJhbmRcIjtcclxuaW1wb3J0IHsgZ2VuZXJhdGVQb3N0RnJvbVByb2ZpbGUgYXMgZ2VuZXJhdGVQb3N0RnJvbVByb2ZpbGVGbG93IH0gZnJvbSBcIkAvYWkvZmxvd3MvZ2VuZXJhdGUtcG9zdC1mcm9tLXByb2ZpbGVcIjtcclxuaW1wb3J0IHsgZ2VuZXJhdGVWaWRlb1Bvc3QgYXMgZ2VuZXJhdGVWaWRlb1Bvc3RGbG93IH0gZnJvbSBcIkAvYWkvZmxvd3MvZ2VuZXJhdGUtdmlkZW8tcG9zdFwiO1xyXG5pbXBvcnQgeyBnZW5lcmF0ZUNyZWF0aXZlQXNzZXQgYXMgZ2VuZXJhdGVDcmVhdGl2ZUFzc2V0RmxvdyB9IGZyb20gXCJAL2FpL2Zsb3dzL2dlbmVyYXRlLWNyZWF0aXZlLWFzc2V0XCI7XHJcbmltcG9ydCB0eXBlIHsgQnJhbmRQcm9maWxlLCBHZW5lcmF0ZWRQb3N0LCBQbGF0Zm9ybSwgQ3JlYXRpdmVBc3NldCB9IGZyb20gXCJAL2xpYi90eXBlc1wiO1xyXG5pbXBvcnQgeyBhcnRpZmFjdHNTZXJ2aWNlIH0gZnJvbSBcIkAvbGliL3NlcnZpY2VzL2FydGlmYWN0cy1zZXJ2aWNlXCI7XHJcbmltcG9ydCB0eXBlIHsgQXJ0aWZhY3QgfSBmcm9tIFwiQC9saWIvdHlwZXMvYXJ0aWZhY3RzXCI7XHJcbmltcG9ydCB7IGdlbmVyYXRlRW5oYW5jZWREZXNpZ24gfSBmcm9tIFwiQC9haS9nZW1pbmktMi41LWRlc2lnblwiO1xyXG5pbXBvcnQgeyBnZW5lcmF0ZVJldm8yQ29udGVudEFjdGlvbiwgZ2VuZXJhdGVSZXZvMkNyZWF0aXZlQXNzZXRBY3Rpb24gfSBmcm9tIFwiQC9hcHAvYWN0aW9ucy9yZXZvLTItYWN0aW9uc1wiO1xyXG5cclxuXHJcbi8vIC0tLSBBSSBGbG93IEFjdGlvbnMgLS0tXHJcblxyXG50eXBlIEFuYWx5c2lzUmVzdWx0ID0ge1xyXG4gIHN1Y2Nlc3M6IHRydWU7XHJcbiAgZGF0YTogQnJhbmRBbmFseXNpc1Jlc3VsdDtcclxufSB8IHtcclxuICBzdWNjZXNzOiBmYWxzZTtcclxuICBlcnJvcjogc3RyaW5nO1xyXG4gIGVycm9yVHlwZTogJ2Jsb2NrZWQnIHwgJ3RpbWVvdXQnIHwgJ2Vycm9yJztcclxufTtcclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBhbmFseXplQnJhbmRBY3Rpb24oXHJcbiAgd2Vic2l0ZVVybDogc3RyaW5nLFxyXG4gIGRlc2lnbkltYWdlVXJpczogc3RyaW5nW10sXHJcbik6IFByb21pc2U8QW5hbHlzaXNSZXN1bHQ+IHtcclxuICB0cnkge1xyXG4gICAgY29uc29sZS5sb2coXCLwn5SNIFN0YXJ0aW5nIGJyYW5kIGFuYWx5c2lzIGZvciBVUkw6XCIsIHdlYnNpdGVVcmwpO1xyXG4gICAgY29uc29sZS5sb2coXCLwn5a877iPIERlc2lnbiBpbWFnZXMgY291bnQ6XCIsIGRlc2lnbkltYWdlVXJpcy5sZW5ndGgpO1xyXG5cclxuICAgIC8vIFZhbGlkYXRlIFVSTCBmb3JtYXRcclxuICAgIGlmICghd2Vic2l0ZVVybCB8fCAhd2Vic2l0ZVVybC50cmltKCkpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgICBlcnJvcjogXCJXZWJzaXRlIFVSTCBpcyByZXF1aXJlZFwiLFxyXG4gICAgICAgIGVycm9yVHlwZTogJ2Vycm9yJ1xyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEVuc3VyZSBVUkwgaGFzIHByb3RvY29sXHJcbiAgICBsZXQgdmFsaWRVcmwgPSB3ZWJzaXRlVXJsLnRyaW0oKTtcclxuICAgIGlmICghdmFsaWRVcmwuc3RhcnRzV2l0aCgnaHR0cDovLycpICYmICF2YWxpZFVybC5zdGFydHNXaXRoKCdodHRwczovLycpKSB7XHJcbiAgICAgIHZhbGlkVXJsID0gJ2h0dHBzOi8vJyArIHZhbGlkVXJsO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGFuYWx5emVCcmFuZEZsb3coe1xyXG4gICAgICB3ZWJzaXRlVXJsOiB2YWxpZFVybCxcclxuICAgICAgZGVzaWduSW1hZ2VVcmlzOiBkZXNpZ25JbWFnZVVyaXMgfHwgW11cclxuICAgIH0pO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKFwi4pyFIEJyYW5kIGFuYWx5c2lzIHJlc3VsdDpcIiwgSlNPTi5zdHJpbmdpZnkocmVzdWx0LCBudWxsLCAyKSk7XHJcbiAgICBjb25zb2xlLmxvZyhcIvCflI0gUmVzdWx0IHR5cGU6XCIsIHR5cGVvZiByZXN1bHQpO1xyXG4gICAgY29uc29sZS5sb2coXCLwn5SNIFJlc3VsdCBrZXlzOlwiLCByZXN1bHQgPyBPYmplY3Qua2V5cyhyZXN1bHQpIDogXCJObyByZXN1bHRcIik7XHJcblxyXG4gICAgaWYgKCFyZXN1bHQpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgICBlcnJvcjogXCJBbmFseXNpcyByZXR1cm5lZCBlbXB0eSByZXN1bHRcIixcclxuICAgICAgICBlcnJvclR5cGU6ICdlcnJvcidcclxuICAgICAgfTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICBkYXRhOiByZXN1bHRcclxuICAgIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCLinYwgRXJyb3IgYW5hbHl6aW5nIGJyYW5kOlwiLCBlcnJvcik7XHJcblxyXG4gICAgLy8gUmV0dXJuIHN0cnVjdHVyZWQgZXJyb3IgcmVzcG9uc2UgaW5zdGVhZCBvZiB0aHJvd2luZ1xyXG4gICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBcIlVua25vd24gZXJyb3Igb2NjdXJyZWRcIjtcclxuXHJcbiAgICBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdmZXRjaCcpIHx8IGVycm9yTWVzc2FnZS5pbmNsdWRlcygnNDAzJykgfHwgZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdibG9ja2VkJykpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgICBlcnJvcjogXCJXZWJzaXRlIGJsb2NrcyBhdXRvbWF0ZWQgYWNjZXNzLiBUaGlzIGlzIGNvbW1vbiBmb3Igc2VjdXJpdHkgcmVhc29ucy5cIixcclxuICAgICAgICBlcnJvclR5cGU6ICdibG9ja2VkJ1xyXG4gICAgICB9O1xyXG4gICAgfSBlbHNlIGlmIChlcnJvck1lc3NhZ2UuaW5jbHVkZXMoJ3RpbWVvdXQnKSkge1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxyXG4gICAgICAgIGVycm9yOiBcIldlYnNpdGUgYW5hbHlzaXMgdGltZWQgb3V0LiBQbGVhc2UgdHJ5IGFnYWluIG9yIGNoZWNrIGlmIHRoZSB3ZWJzaXRlIGlzIGFjY2Vzc2libGUuXCIsXHJcbiAgICAgICAgZXJyb3JUeXBlOiAndGltZW91dCdcclxuICAgICAgfTtcclxuICAgIH0gZWxzZSBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdDT1JTJykpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgICBlcnJvcjogXCJXZWJzaXRlIGJsb2NrcyBhdXRvbWF0ZWQgYWNjZXNzLiBUaGlzIGlzIGNvbW1vbiBmb3Igc2VjdXJpdHkgcmVhc29ucy5cIixcclxuICAgICAgICBlcnJvclR5cGU6ICdibG9ja2VkJ1xyXG4gICAgICB9O1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgICBlcnJvcjogYEFuYWx5c2lzIGZhaWxlZDogJHtlcnJvck1lc3NhZ2V9YCxcclxuICAgICAgICBlcnJvclR5cGU6ICdlcnJvcidcclxuICAgICAgfTtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbmNvbnN0IGdldEFzcGVjdFJhdGlvRm9yUGxhdGZvcm0gPSAocGxhdGZvcm06IFBsYXRmb3JtKTogc3RyaW5nID0+IHtcclxuICBzd2l0Y2ggKHBsYXRmb3JtKSB7XHJcbiAgICBjYXNlICdJbnN0YWdyYW0nOlxyXG4gICAgICByZXR1cm4gJzE6MSc7IC8vIFNxdWFyZVxyXG4gICAgY2FzZSAnRmFjZWJvb2snOlxyXG4gICAgICByZXR1cm4gJzE2OjknOyAvLyBMYW5kc2NhcGUgLSBGYWNlYm9vayBwb3N0cyBhcmUgbGFuZHNjYXBlIGZvcm1hdFxyXG4gICAgY2FzZSAnVHdpdHRlcic6XHJcbiAgICAgIHJldHVybiAnMTY6OSc7IC8vIExhbmRzY2FwZVxyXG4gICAgY2FzZSAnTGlua2VkSW4nOlxyXG4gICAgICByZXR1cm4gJzE2OjknOyAvLyBMYW5kc2NhcGUgLSBMaW5rZWRJbiBwb3N0cyBhcmUgbGFuZHNjYXBlIGZvcm1hdFxyXG4gICAgZGVmYXVsdDpcclxuICAgICAgcmV0dXJuICcxOjEnO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdlbmVyYXRlQ29udGVudEFjdGlvbihcclxuICBwcm9maWxlOiBCcmFuZFByb2ZpbGUsXHJcbiAgcGxhdGZvcm06IFBsYXRmb3JtLFxyXG4gIGJyYW5kQ29uc2lzdGVuY3k/OiB7IHN0cmljdENvbnNpc3RlbmN5OiBib29sZWFuOyBmb2xsb3dCcmFuZENvbG9yczogYm9vbGVhbiB9LFxyXG4gIHVzZUxvY2FsTGFuZ3VhZ2U6IGJvb2xlYW4gPSBmYWxzZVxyXG4pOiBQcm9taXNlPEdlbmVyYXRlZFBvc3Q+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpO1xyXG4gICAgY29uc3QgZGF5T2ZXZWVrID0gdG9kYXkudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHsgd2Vla2RheTogJ2xvbmcnIH0pO1xyXG4gICAgY29uc3QgY3VycmVudERhdGUgPSB0b2RheS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywgeyB5ZWFyOiAnbnVtZXJpYycsIG1vbnRoOiAnbG9uZycsIGRheTogJ251bWVyaWMnIH0pO1xyXG5cclxuICAgIC8vIEFwcGx5IGJyYW5kIGNvbnNpc3RlbmN5IGxvZ2ljXHJcbiAgICBjb25zdCBlZmZlY3RpdmVEZXNpZ25FeGFtcGxlcyA9IGJyYW5kQ29uc2lzdGVuY3k/LnN0cmljdENvbnNpc3RlbmN5XHJcbiAgICAgID8gKHByb2ZpbGUuZGVzaWduRXhhbXBsZXMgfHwgW10pXHJcbiAgICAgIDogW107IC8vIERvbid0IHVzZSBkZXNpZ24gZXhhbXBsZXMgaWYgbm90IHN0cmljdCBjb25zaXN0ZW5jeVxyXG5cclxuICAgIC8vIEVuaGFuY2VkIGJyYW5kIHByb2ZpbGUgZGF0YSBleHRyYWN0aW9uXHJcbiAgICBjb25zdCBlbmhhbmNlZFByb2ZpbGUgPSB7XHJcbiAgICAgIC4uLnByb2ZpbGUsXHJcbiAgICAgIC8vIEVuc3VyZSBicmFuZCBjb2xvcnMgYXJlIGF2YWlsYWJsZVxyXG4gICAgICBwcmltYXJ5Q29sb3I6IHByb2ZpbGUucHJpbWFyeUNvbG9yIHx8ICcjM0I4MkY2JyxcclxuICAgICAgYWNjZW50Q29sb3I6IHByb2ZpbGUuYWNjZW50Q29sb3IgfHwgJyMxMEI5ODEnLFxyXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IHByb2ZpbGUuYmFja2dyb3VuZENvbG9yIHx8ICcjRjhGQUZDJyxcclxuICAgICAgLy8gRXh0cmFjdCBzZXJ2aWNlcyBpbmZvcm1hdGlvblxyXG4gICAgICBzZXJ2aWNlc0FycmF5OiB0eXBlb2YgcHJvZmlsZS5zZXJ2aWNlcyA9PT0gJ3N0cmluZydcclxuICAgICAgICA/IHByb2ZpbGUuc2VydmljZXMuc3BsaXQoJ1xcbicpLmZpbHRlcihzID0+IHMudHJpbSgpKVxyXG4gICAgICAgIDogQXJyYXkuaXNBcnJheShwcm9maWxlLnNlcnZpY2VzKVxyXG4gICAgICAgICAgPyBwcm9maWxlLnNlcnZpY2VzLm1hcChzID0+IHR5cGVvZiBzID09PSAnc3RyaW5nJyA/IHMgOiBzLm5hbWUgfHwgcy5kZXNjcmlwdGlvbiB8fCAnJylcclxuICAgICAgICAgIDogW10sXHJcbiAgICAgIC8vIEV4dHJhY3QgY29udGFjdCBpbmZvcm1hdGlvbiBmb3IgYnJhbmQgY29udGV4dFxyXG4gICAgICBjb250YWN0SW5mbzogcHJvZmlsZS5jb250YWN0SW5mbyB8fCB7fSxcclxuICAgICAgc29jaWFsTWVkaWE6IHByb2ZpbGUuc29jaWFsTWVkaWEgfHwge30sXHJcbiAgICB9O1xyXG5cclxuICAgIC8vIENvbnZlcnQgYXJyYXlzIHRvIG5ld2xpbmUtc2VwYXJhdGVkIHN0cmluZ3MgZm9yIEFJIHByb2Nlc3NpbmdcclxuICAgIGNvbnN0IGtleUZlYXR1cmVzU3RyaW5nID0gQXJyYXkuaXNBcnJheShwcm9maWxlLmtleUZlYXR1cmVzKVxyXG4gICAgICA/IHByb2ZpbGUua2V5RmVhdHVyZXMuam9pbignXFxuJylcclxuICAgICAgOiBwcm9maWxlLmtleUZlYXR1cmVzIHx8ICcnO1xyXG5cclxuICAgIGNvbnN0IGNvbXBldGl0aXZlQWR2YW50YWdlc1N0cmluZyA9IEFycmF5LmlzQXJyYXkocHJvZmlsZS5jb21wZXRpdGl2ZUFkdmFudGFnZXMpXHJcbiAgICAgID8gcHJvZmlsZS5jb21wZXRpdGl2ZUFkdmFudGFnZXMuam9pbignXFxuJylcclxuICAgICAgOiBwcm9maWxlLmNvbXBldGl0aXZlQWR2YW50YWdlcyB8fCAnJztcclxuXHJcbiAgICAvLyBDb252ZXJ0IHNlcnZpY2VzIGFycmF5IHRvIG5ld2xpbmUtc2VwYXJhdGVkIHN0cmluZ1xyXG4gICAgY29uc3Qgc2VydmljZXNTdHJpbmcgPSBBcnJheS5pc0FycmF5KHByb2ZpbGUuc2VydmljZXMpXHJcbiAgICAgID8gcHJvZmlsZS5zZXJ2aWNlcy5tYXAoc2VydmljZSA9PlxyXG4gICAgICAgIHR5cGVvZiBzZXJ2aWNlID09PSAnb2JqZWN0JyAmJiBzZXJ2aWNlLm5hbWVcclxuICAgICAgICAgID8gYCR7c2VydmljZS5uYW1lfTogJHtzZXJ2aWNlLmRlc2NyaXB0aW9uIHx8ICcnfWBcclxuICAgICAgICAgIDogc2VydmljZVxyXG4gICAgICApLmpvaW4oJ1xcbicpXHJcbiAgICAgIDogcHJvZmlsZS5zZXJ2aWNlcyB8fCAnJztcclxuXHJcblxyXG5cclxuICAgIGNvbnN0IHBvc3REZXRhaWxzID0gYXdhaXQgZ2VuZXJhdGVQb3N0RnJvbVByb2ZpbGVGbG93KHtcclxuICAgICAgYnVzaW5lc3NOYW1lOiBlbmhhbmNlZFByb2ZpbGUuYnVzaW5lc3NOYW1lLFxyXG4gICAgICBidXNpbmVzc1R5cGU6IGVuaGFuY2VkUHJvZmlsZS5idXNpbmVzc1R5cGUsXHJcbiAgICAgIGxvY2F0aW9uOiBlbmhhbmNlZFByb2ZpbGUubG9jYXRpb24sXHJcbiAgICAgIHdyaXRpbmdUb25lOiBlbmhhbmNlZFByb2ZpbGUud3JpdGluZ1RvbmUsXHJcbiAgICAgIGNvbnRlbnRUaGVtZXM6IGVuaGFuY2VkUHJvZmlsZS5jb250ZW50VGhlbWVzLFxyXG4gICAgICB2aXN1YWxTdHlsZTogZW5oYW5jZWRQcm9maWxlLnZpc3VhbFN0eWxlLFxyXG4gICAgICBsb2dvRGF0YVVybDogZW5oYW5jZWRQcm9maWxlLmxvZ29EYXRhVXJsLFxyXG4gICAgICBkZXNpZ25FeGFtcGxlczogZWZmZWN0aXZlRGVzaWduRXhhbXBsZXMsIC8vIFVzZSBkZXNpZ24gZXhhbXBsZXMgYmFzZWQgb24gY29uc2lzdGVuY3kgcHJlZmVyZW5jZVxyXG4gICAgICBwcmltYXJ5Q29sb3I6IGVuaGFuY2VkUHJvZmlsZS5wcmltYXJ5Q29sb3IsXHJcbiAgICAgIGFjY2VudENvbG9yOiBlbmhhbmNlZFByb2ZpbGUuYWNjZW50Q29sb3IsXHJcbiAgICAgIGJhY2tncm91bmRDb2xvcjogZW5oYW5jZWRQcm9maWxlLmJhY2tncm91bmRDb2xvcixcclxuICAgICAgZGF5T2ZXZWVrLFxyXG4gICAgICBjdXJyZW50RGF0ZSxcclxuICAgICAgdmFyaWFudHM6IFt7XHJcbiAgICAgICAgcGxhdGZvcm06IHBsYXRmb3JtLFxyXG4gICAgICAgIGFzcGVjdFJhdGlvOiBnZXRBc3BlY3RSYXRpb0ZvclBsYXRmb3JtKHBsYXRmb3JtKSxcclxuICAgICAgfV0sXHJcbiAgICAgIC8vIFBhc3MgbmV3IGRldGFpbGVkIGZpZWxkc1xyXG4gICAgICBzZXJ2aWNlczogc2VydmljZXNTdHJpbmcsXHJcbiAgICAgIHRhcmdldEF1ZGllbmNlOiBlbmhhbmNlZFByb2ZpbGUudGFyZ2V0QXVkaWVuY2UsXHJcbiAgICAgIGtleUZlYXR1cmVzOiBrZXlGZWF0dXJlc1N0cmluZyxcclxuICAgICAgY29tcGV0aXRpdmVBZHZhbnRhZ2VzOiBjb21wZXRpdGl2ZUFkdmFudGFnZXNTdHJpbmcsXHJcbiAgICAgIC8vIFBhc3MgYnJhbmQgY29uc2lzdGVuY3kgcHJlZmVyZW5jZXNcclxuICAgICAgYnJhbmRDb25zaXN0ZW5jeTogYnJhbmRDb25zaXN0ZW5jeSB8fCB7IHN0cmljdENvbnNpc3RlbmN5OiBmYWxzZSwgZm9sbG93QnJhbmRDb2xvcnM6IHRydWUgfSxcclxuICAgICAgLy8gRW5oYW5jZWQgYnJhbmQgY29udGV4dFxyXG4gICAgICB3ZWJzaXRlVXJsOiBlbmhhbmNlZFByb2ZpbGUud2Vic2l0ZVVybCxcclxuICAgICAgZGVzY3JpcHRpb246IGVuaGFuY2VkUHJvZmlsZS5kZXNjcmlwdGlvbixcclxuICAgICAgY29udGFjdEluZm86IGVuaGFuY2VkUHJvZmlsZS5jb250YWN0SW5mbyxcclxuICAgICAgc29jaWFsTWVkaWE6IGVuaGFuY2VkUHJvZmlsZS5zb2NpYWxNZWRpYSxcclxuICAgICAgLy8gTGFuZ3VhZ2UgcHJlZmVyZW5jZXNcclxuICAgICAgdXNlTG9jYWxMYW5ndWFnZTogdXNlTG9jYWxMYW5ndWFnZSxcclxuICAgIH0pO1xyXG5cclxuICAgIGNvbnN0IG5ld1Bvc3Q6IEdlbmVyYXRlZFBvc3QgPSB7XHJcbiAgICAgIGlkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIGRhdGU6IHRvZGF5LnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIGNvbnRlbnQ6IHBvc3REZXRhaWxzLmNvbnRlbnQsXHJcbiAgICAgIGhhc2h0YWdzOiBwb3N0RGV0YWlscy5oYXNodGFncyxcclxuICAgICAgc3RhdHVzOiAnZ2VuZXJhdGVkJyxcclxuICAgICAgdmFyaWFudHM6IHBvc3REZXRhaWxzLnZhcmlhbnRzLFxyXG4gICAgICBjYXRjaHlXb3JkczogcG9zdERldGFpbHMuY2F0Y2h5V29yZHMsXHJcbiAgICAgIHN1YmhlYWRsaW5lOiBwb3N0RGV0YWlscy5zdWJoZWFkbGluZSxcclxuICAgICAgY2FsbFRvQWN0aW9uOiBwb3N0RGV0YWlscy5jYWxsVG9BY3Rpb24sXHJcbiAgICAgIC8vIEluY2x1ZGUgZW5oYW5jZWQgQUkgZmVhdHVyZXNcclxuICAgICAgY29udGVudFZhcmlhbnRzOiBwb3N0RGV0YWlscy5jb250ZW50VmFyaWFudHMsXHJcbiAgICAgIGhhc2h0YWdBbmFseXNpczogcG9zdERldGFpbHMuaGFzaHRhZ0FuYWx5c2lzLFxyXG4gICAgICAvLyBJbmNsdWRlIGFkdmFuY2VkIEFJIGZlYXR1cmVzXHJcbiAgICAgIG1hcmtldEludGVsbGlnZW5jZTogcG9zdERldGFpbHMubWFya2V0SW50ZWxsaWdlbmNlLFxyXG4gICAgICAvLyBJbmNsdWRlIGxvY2FsIGNvbnRleHQgZmVhdHVyZXNcclxuICAgICAgbG9jYWxDb250ZXh0OiBwb3N0RGV0YWlscy5sb2NhbENvbnRleHQsXHJcbiAgICB9O1xyXG5cclxuICAgIHJldHVybiBuZXdQb3N0O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZ2VuZXJhdGluZyBjb250ZW50OlwiLCBlcnJvcik7XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZ2VuZXJhdGUgY29udGVudC4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci5cIik7XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVWaWRlb0NvbnRlbnRBY3Rpb24oXHJcbiAgcHJvZmlsZTogQnJhbmRQcm9maWxlLFxyXG4gIGNhdGNoeVdvcmRzOiBzdHJpbmcsXHJcbiAgcG9zdENvbnRlbnQ6IHN0cmluZyxcclxuKTogUHJvbWlzZTx7IHZpZGVvVXJsOiBzdHJpbmcgfT4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBnZW5lcmF0ZVZpZGVvUG9zdEZsb3coe1xyXG4gICAgICBidXNpbmVzc1R5cGU6IHByb2ZpbGUuYnVzaW5lc3NUeXBlLFxyXG4gICAgICBsb2NhdGlvbjogcHJvZmlsZS5sb2NhdGlvbixcclxuICAgICAgdmlzdWFsU3R5bGU6IHByb2ZpbGUudmlzdWFsU3R5bGUsXHJcbiAgICAgIGltYWdlVGV4dDogY2F0Y2h5V29yZHMsIC8vIFVzZSBjYXRjaHlXb3JkcyBhcyBpbWFnZVRleHQgZm9yIHZpZGVvIGdlbmVyYXRpb25cclxuICAgICAgcG9zdENvbnRlbnQ6IHBvc3RDb250ZW50LFxyXG4gICAgfSk7XHJcbiAgICByZXR1cm4geyB2aWRlb1VybDogcmVzdWx0LnZpZGVvVXJsIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZW5lcmF0aW5nIHZpZGVvIGNvbnRlbnQ6XCIsIGVycm9yKTtcclxuICAgIC8vIFBhc3MgdGhlIHNwZWNpZmljIGVycm9yIG1lc3NhZ2UgZnJvbSB0aGUgZmxvdyB0byB0aGUgY2xpZW50XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlKTtcclxuICB9XHJcbn1cclxuXHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVDcmVhdGl2ZUFzc2V0QWN0aW9uKFxyXG4gIHByb21wdDogc3RyaW5nLFxyXG4gIG91dHB1dFR5cGU6ICdpbWFnZScgfCAndmlkZW8nLFxyXG4gIHJlZmVyZW5jZUFzc2V0VXJsOiBzdHJpbmcgfCBudWxsLFxyXG4gIHVzZUJyYW5kUHJvZmlsZTogYm9vbGVhbixcclxuICBicmFuZFByb2ZpbGU6IEJyYW5kUHJvZmlsZSB8IG51bGwsXHJcbiAgbWFza0RhdGFVcmw6IHN0cmluZyB8IG51bGwgfCB1bmRlZmluZWQsXHJcbiAgYXNwZWN0UmF0aW86ICcxNjo5JyB8ICc5OjE2JyB8IHVuZGVmaW5lZFxyXG4pOiBQcm9taXNlPENyZWF0aXZlQXNzZXQ+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZ2VuZXJhdGVDcmVhdGl2ZUFzc2V0Rmxvdyh7XHJcbiAgICAgIHByb21wdCxcclxuICAgICAgb3V0cHV0VHlwZSxcclxuICAgICAgcmVmZXJlbmNlQXNzZXRVcmwsXHJcbiAgICAgIHVzZUJyYW5kUHJvZmlsZSxcclxuICAgICAgYnJhbmRQcm9maWxlOiB1c2VCcmFuZFByb2ZpbGUgPyBicmFuZFByb2ZpbGUgOiBudWxsLFxyXG4gICAgICBtYXNrRGF0YVVybCxcclxuICAgICAgYXNwZWN0UmF0aW8sXHJcbiAgICB9KTtcclxuICAgIHJldHVybiByZXN1bHQ7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZW5lcmF0aW5nIGNyZWF0aXZlIGFzc2V0OlwiLCBlcnJvcik7XHJcbiAgICAvLyBBbHdheXMgcGFzcyB0aGUgc3BlY2lmaWMgZXJyb3IgbWVzc2FnZSBmcm9tIHRoZSBmbG93IHRvIHRoZSBjbGllbnQuXHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlKTtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZW5lcmF0ZUVuaGFuY2VkRGVzaWduQWN0aW9uKFxyXG4gIGJ1c2luZXNzVHlwZTogc3RyaW5nLFxyXG4gIHBsYXRmb3JtOiBzdHJpbmcsXHJcbiAgdmlzdWFsU3R5bGU6IHN0cmluZyxcclxuICBpbWFnZVRleHQ6IHN0cmluZyB8IHsgY2F0Y2h5V29yZHM6IHN0cmluZzsgc3ViaGVhZGxpbmU/OiBzdHJpbmc7IGNhbGxUb0FjdGlvbj86IHN0cmluZyB9LFxyXG4gIGJyYW5kUHJvZmlsZT86IEJyYW5kUHJvZmlsZSxcclxuICBlbmFibGVFbmhhbmNlbWVudHM6IGJvb2xlYW4gPSB0cnVlLFxyXG4gIGJyYW5kQ29uc2lzdGVuY3k/OiB7IHN0cmljdENvbnNpc3RlbmN5OiBib29sZWFuOyBmb2xsb3dCcmFuZENvbG9yczogYm9vbGVhbiB9LFxyXG4gIGFydGlmYWN0SW5zdHJ1Y3Rpb25zPzogc3RyaW5nLFxyXG4gIGluY2x1ZGVQZW9wbGVJbkRlc2lnbnM6IGJvb2xlYW4gPSB0cnVlLFxyXG4gIHVzZUxvY2FsTGFuZ3VhZ2U6IGJvb2xlYW4gPSBmYWxzZVxyXG4pOiBQcm9taXNlPHtcclxuICBpbWFnZVVybDogc3RyaW5nO1xyXG4gIHF1YWxpdHlTY29yZTogbnVtYmVyO1xyXG4gIGVuaGFuY2VtZW50c0FwcGxpZWQ6IHN0cmluZ1tdO1xyXG4gIHByb2Nlc3NpbmdUaW1lOiBudW1iZXI7XHJcbn0+IHtcclxuICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xyXG4gIGNvbnN0IGVuaGFuY2VtZW50c0FwcGxpZWQ6IHN0cmluZ1tdID0gW107XHJcblxyXG4gIHRyeSB7XHJcbiAgICBpZiAoIWJyYW5kUHJvZmlsZSkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0JyYW5kIHByb2ZpbGUgaXMgcmVxdWlyZWQgZm9yIGVuaGFuY2VkIGRlc2lnbiBnZW5lcmF0aW9uJyk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gSGFuZGxlIGJvdGggb2xkIHN0cmluZyBmb3JtYXQgYW5kIG5ldyBvYmplY3QgZm9ybWF0XHJcbiAgICBsZXQgZmluYWxJbWFnZVRleHQ6IHN0cmluZztcclxuICAgIGlmICh0eXBlb2YgaW1hZ2VUZXh0ID09PSAnc3RyaW5nJykge1xyXG4gICAgICBmaW5hbEltYWdlVGV4dCA9IGltYWdlVGV4dDtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIENvbWJpbmUgY2F0Y2h5IHdvcmRzLCBzdWJoZWFkbGluZSwgYW5kIGNhbGwtdG8tYWN0aW9uXHJcbiAgICAgIGNvbnN0IGNvbXBvbmVudHMgPSBbaW1hZ2VUZXh0LmNhdGNoeVdvcmRzXTtcclxuICAgICAgaWYgKGltYWdlVGV4dC5zdWJoZWFkbGluZSAmJiBpbWFnZVRleHQuc3ViaGVhZGxpbmUudHJpbSgpKSB7XHJcbiAgICAgICAgY29tcG9uZW50cy5wdXNoKGltYWdlVGV4dC5zdWJoZWFkbGluZS50cmltKCkpO1xyXG4gICAgICB9XHJcbiAgICAgIGlmIChpbWFnZVRleHQuY2FsbFRvQWN0aW9uICYmIGltYWdlVGV4dC5jYWxsVG9BY3Rpb24udHJpbSgpKSB7XHJcbiAgICAgICAgY29tcG9uZW50cy5wdXNoKGltYWdlVGV4dC5jYWxsVG9BY3Rpb24udHJpbSgpKTtcclxuICAgICAgfVxyXG4gICAgICBmaW5hbEltYWdlVGV4dCA9IGNvbXBvbmVudHMuam9pbignXFxuJyk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coJ/CfjqggRW5oYW5jZWQgRGVzaWduIEdlbmVyYXRpb24gU3RhcnRlZCcpO1xyXG4gICAgY29uc29sZS5sb2coJy0gQnVzaW5lc3MgVHlwZTonLCBidXNpbmVzc1R5cGUpO1xyXG4gICAgY29uc29sZS5sb2coJy0gUGxhdGZvcm06JywgcGxhdGZvcm0pO1xyXG4gICAgY29uc29sZS5sb2coJy0gVmlzdWFsIFN0eWxlOicsIHZpc3VhbFN0eWxlKTtcclxuICAgIGNvbnNvbGUubG9nKCctIEltYWdlIFRleHQ6JywgZmluYWxJbWFnZVRleHQpO1xyXG4gICAgY29uc29sZS5sb2coJy0gQnJhbmQgUHJvZmlsZTonLCBicmFuZFByb2ZpbGUuYnVzaW5lc3NOYW1lKTtcclxuICAgIGNvbnNvbGUubG9nKCctIEVuaGFuY2VtZW50cyBFbmFibGVkOicsIGVuYWJsZUVuaGFuY2VtZW50cyk7XHJcblxyXG4gICAgLy8gVHJ5IEdlbWluaSAyLjUgZmlyc3QgKGJlc3QgcXVhbGl0eSksIHRoZW4gZmFsbGJhY2sgdG8gT3BlbkFJLCB0aGVuIEdlbWluaSAyLjAgSERcclxuICAgIGxldCByZXN1bHQ7XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coJ/CfmoAgVXNpbmcgR2VtaW5pIDIuNSBQcm8gZm9yIHN1cGVyaW9yIGRlc2lnbiBnZW5lcmF0aW9uLi4uJyk7XHJcblxyXG4gICAgICByZXN1bHQgPSBhd2FpdCBnZW5lcmF0ZUVuaGFuY2VkRGVzaWduKHtcclxuICAgICAgICBidXNpbmVzc1R5cGUsXHJcbiAgICAgICAgcGxhdGZvcm0sXHJcbiAgICAgICAgdmlzdWFsU3R5bGUsXHJcbiAgICAgICAgaW1hZ2VUZXh0OiBmaW5hbEltYWdlVGV4dCxcclxuICAgICAgICBicmFuZFByb2ZpbGUsXHJcbiAgICAgICAgYnJhbmRDb25zaXN0ZW5jeSxcclxuICAgICAgICBhcnRpZmFjdEluc3RydWN0aW9ucyxcclxuICAgICAgICBpbmNsdWRlUGVvcGxlSW5EZXNpZ25zLFxyXG4gICAgICAgIHVzZUxvY2FsTGFuZ3VhZ2UsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ+KchSBHZW1pbmkgMi41IGVuaGFuY2VkIGRlc2lnbiBnZW5lcmF0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICAgIGNvbnNvbGUubG9nKGDwn46vIFF1YWxpdHkgU2NvcmU6ICR7cmVzdWx0LnF1YWxpdHlTY29yZX0vMTBgKTtcclxuICAgICAgY29uc29sZS5sb2coYOKaoSBQcm9jZXNzaW5nIFRpbWU6ICR7cmVzdWx0LnByb2Nlc3NpbmdUaW1lfW1zYCk7XHJcblxyXG4gICAgfSBjYXRjaCAoZ2VtaW5pMjVFcnJvcikge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBHZW1pbmkgMi41IGdlbmVyYXRpb24gZmFpbGVkLCBmYWxsaW5nIGJhY2sgdG8gT3BlbkFJOicsIGdlbWluaTI1RXJyb3IpO1xyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+agCBVc2luZyBPcGVuQUkgR1BULUltYWdlIDEgZm9yIGVuaGFuY2VkIGRlc2lnbiBnZW5lcmF0aW9uLi4uJyk7XHJcbiAgICAgICAgY29uc3QgeyBnZW5lcmF0ZUVuaGFuY2VkRGVzaWduV2l0aEZhbGxiYWNrIH0gPSBhd2FpdCBpbXBvcnQoJ0AvYWkvb3BlbmFpLWVuaGFuY2VkLWRlc2lnbicpO1xyXG5cclxuICAgICAgICByZXN1bHQgPSBhd2FpdCBnZW5lcmF0ZUVuaGFuY2VkRGVzaWduV2l0aEZhbGxiYWNrKHtcclxuICAgICAgICAgIGJ1c2luZXNzVHlwZSxcclxuICAgICAgICAgIHBsYXRmb3JtLFxyXG4gICAgICAgICAgdmlzdWFsU3R5bGUsXHJcbiAgICAgICAgICBpbWFnZVRleHQ6IGZpbmFsSW1hZ2VUZXh0LFxyXG4gICAgICAgICAgYnJhbmRQcm9maWxlLFxyXG4gICAgICAgICAgYnJhbmRDb25zaXN0ZW5jeSxcclxuICAgICAgICAgIGFydGlmYWN0SW5zdHJ1Y3Rpb25zLFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZygn4pyFIE9wZW5BSSBHUFQtSW1hZ2UgMSBlbmhhbmNlZCBkZXNpZ24gZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseScpO1xyXG4gICAgICB9IGNhdGNoIChvcGVuYWlFcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIE9wZW5BSSBnZW5lcmF0aW9uIGFsc28gZmFpbGVkLCBmYWxsaW5nIGJhY2sgdG8gR2VtaW5pIDIuMCBIRDonLCBvcGVuYWlFcnJvcik7XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5qAIFVzaW5nIEdlbWluaSAyLjAgRmxhc2ggSEQgZm9yIGVuaGFuY2VkIGRlc2lnbiBnZW5lcmF0aW9uLi4uJyk7XHJcbiAgICAgICAgY29uc3QgeyBnZW5lcmF0ZUdlbWluaUhERW5oYW5jZWREZXNpZ25XaXRoRmFsbGJhY2sgfSA9IGF3YWl0IGltcG9ydCgnQC9haS9nZW1pbmktaGQtZW5oYW5jZWQtZGVzaWduJyk7XHJcblxyXG4gICAgICAgIHJlc3VsdCA9IGF3YWl0IGdlbmVyYXRlR2VtaW5pSERFbmhhbmNlZERlc2lnbldpdGhGYWxsYmFjayh7XHJcbiAgICAgICAgICBidXNpbmVzc1R5cGUsXHJcbiAgICAgICAgICBwbGF0Zm9ybSxcclxuICAgICAgICAgIHZpc3VhbFN0eWxlLFxyXG4gICAgICAgICAgaW1hZ2VUZXh0OiBmaW5hbEltYWdlVGV4dCxcclxuICAgICAgICAgIGJyYW5kUHJvZmlsZSxcclxuICAgICAgICAgIGJyYW5kQ29uc2lzdGVuY3ksXHJcbiAgICAgICAgICBhcnRpZmFjdEluc3RydWN0aW9ucyxcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBHZW1pbmkgMi4wIEhEIGVuaGFuY2VkIGRlc2lnbiBnZW5lcmF0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zb2xlLmxvZygn8J+UlyBJbWFnZSBVUkw6JywgcmVzdWx0LmltYWdlVXJsKTtcclxuICAgIGNvbnNvbGUubG9nKCfirZAgUXVhbGl0eSBTY29yZTonLCByZXN1bHQucXVhbGl0eVNjb3JlKTtcclxuICAgIGNvbnNvbGUubG9nKCfwn46vIEVuaGFuY2VtZW50cyBBcHBsaWVkOicsIHJlc3VsdC5lbmhhbmNlbWVudHNBcHBsaWVkKTtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBpbWFnZVVybDogcmVzdWx0LmltYWdlVXJsLFxyXG4gICAgICBxdWFsaXR5U2NvcmU6IHJlc3VsdC5xdWFsaXR5U2NvcmUsXHJcbiAgICAgIGVuaGFuY2VtZW50c0FwcGxpZWQ6IHJlc3VsdC5lbmhhbmNlbWVudHNBcHBsaWVkLFxyXG4gICAgICBwcm9jZXNzaW5nVGltZTogcmVzdWx0LnByb2Nlc3NpbmdUaW1lXHJcbiAgICB9O1xyXG5cclxuXHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZW5lcmF0aW5nIGVuaGFuY2VkIGRlc2lnbjpcIiwgZXJyb3IpO1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKChlcnJvciBhcyBFcnJvcikubWVzc2FnZSk7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogR2VuZXJhdGUgZW5oYW5jZWQgZGVzaWduIHNwZWNpZmljYWxseSB1c2luZyBHZW1pbmkgMi4wIEZsYXNoIEhEXHJcbiAqIFRoaXMgYWN0aW9uIGZvcmNlcyB0aGUgdXNlIG9mIEdlbWluaSBIRCBmb3IgbWF4aW11bSBxdWFsaXR5XHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVHZW1pbmlIRERlc2lnbkFjdGlvbihcclxuICBidXNpbmVzc1R5cGU6IHN0cmluZyxcclxuICBwbGF0Zm9ybTogc3RyaW5nLFxyXG4gIHZpc3VhbFN0eWxlOiBzdHJpbmcsXHJcbiAgaW1hZ2VUZXh0OiBzdHJpbmcsXHJcbiAgYnJhbmRQcm9maWxlOiBCcmFuZFByb2ZpbGUsXHJcbiAgYnJhbmRDb25zaXN0ZW5jeT86IHtcclxuICAgIHN0cmljdENvbnNpc3RlbmN5OiBib29sZWFuO1xyXG4gICAgZm9sbG93QnJhbmRDb2xvcnM6IGJvb2xlYW47XHJcbiAgfSxcclxuICBhcnRpZmFjdEluc3RydWN0aW9ucz86IHN0cmluZ1xyXG4pOiBQcm9taXNlPFBvc3RWYXJpYW50PiB7XHJcbiAgdHJ5IHtcclxuICAgIGlmICghYnJhbmRQcm9maWxlKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignQnJhbmQgcHJvZmlsZSBpcyByZXF1aXJlZCBmb3IgR2VtaW5pIEhEIGRlc2lnbiBnZW5lcmF0aW9uJyk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coJ/CfjqggR2VtaW5pIEhEIERlc2lnbiBHZW5lcmF0aW9uIFN0YXJ0ZWQnKTtcclxuICAgIGNvbnNvbGUubG9nKCctIEJ1c2luZXNzIFR5cGU6JywgYnVzaW5lc3NUeXBlKTtcclxuICAgIGNvbnNvbGUubG9nKCctIFBsYXRmb3JtOicsIHBsYXRmb3JtKTtcclxuICAgIGNvbnNvbGUubG9nKCctIFZpc3VhbCBTdHlsZTonLCB2aXN1YWxTdHlsZSk7XHJcbiAgICBjb25zb2xlLmxvZygnLSBJbWFnZSBUZXh0OicsIGltYWdlVGV4dCk7XHJcbiAgICBjb25zb2xlLmxvZygnLSBCcmFuZCBQcm9maWxlOicsIGJyYW5kUHJvZmlsZS5idXNpbmVzc05hbWUpO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKCfwn5qAIFVzaW5nIEdlbWluaSAyLjAgRmxhc2ggSEQgZm9yIGVuaGFuY2VkIGRlc2lnbiBnZW5lcmF0aW9uLi4uJyk7XHJcbiAgICBjb25zdCB7IGdlbmVyYXRlR2VtaW5pSERFbmhhbmNlZERlc2lnbldpdGhGYWxsYmFjayB9ID0gYXdhaXQgaW1wb3J0KCdAL2FpL2dlbWluaS1oZC1lbmhhbmNlZC1kZXNpZ24nKTtcclxuXHJcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBnZW5lcmF0ZUdlbWluaUhERW5oYW5jZWREZXNpZ25XaXRoRmFsbGJhY2soe1xyXG4gICAgICBidXNpbmVzc1R5cGUsXHJcbiAgICAgIHBsYXRmb3JtLFxyXG4gICAgICB2aXN1YWxTdHlsZSxcclxuICAgICAgaW1hZ2VUZXh0LFxyXG4gICAgICBicmFuZFByb2ZpbGUsXHJcbiAgICAgIGJyYW5kQ29uc2lzdGVuY3ksXHJcbiAgICAgIGFydGlmYWN0SW5zdHJ1Y3Rpb25zLFxyXG4gICAgfSk7XHJcblxyXG4gICAgY29uc29sZS5sb2coJ+KchSBHZW1pbmkgSEQgZW5oYW5jZWQgZGVzaWduIGdlbmVyYXRlZCBzdWNjZXNzZnVsbHknKTtcclxuICAgIGNvbnNvbGUubG9nKCfwn5SXIEltYWdlIFVSTDonLCByZXN1bHQuaW1hZ2VVcmwpO1xyXG4gICAgY29uc29sZS5sb2coJ+KtkCBRdWFsaXR5IFNjb3JlOicsIHJlc3VsdC5xdWFsaXR5U2NvcmUpO1xyXG4gICAgY29uc29sZS5sb2coJ/Cfjq8gRW5oYW5jZW1lbnRzIEFwcGxpZWQ6JywgcmVzdWx0LmVuaGFuY2VtZW50c0FwcGxpZWQpO1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIHBsYXRmb3JtLFxyXG4gICAgICBpbWFnZVVybDogcmVzdWx0LmltYWdlVXJsLFxyXG4gICAgICBjYXB0aW9uOiBpbWFnZVRleHQsXHJcbiAgICAgIGhhc2h0YWdzOiBbXSxcclxuICAgIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBpbiBHZW1pbmkgSEQgZGVzaWduIGdlbmVyYXRpb246JywgZXJyb3IpO1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKGBHZW1pbmkgSEQgZGVzaWduIGdlbmVyYXRpb24gZmFpbGVkOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWApO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEdlbmVyYXRlIGNvbnRlbnQgd2l0aCBhcnRpZmFjdCByZWZlcmVuY2VzIChFbmhhbmNlZClcclxuICovXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZW5lcmF0ZUNvbnRlbnRXaXRoQXJ0aWZhY3RzQWN0aW9uKFxyXG4gIHByb2ZpbGU6IEJyYW5kUHJvZmlsZSxcclxuICBwbGF0Zm9ybTogUGxhdGZvcm0sXHJcbiAgYnJhbmRDb25zaXN0ZW5jeT86IHsgc3RyaWN0Q29uc2lzdGVuY3k6IGJvb2xlYW47IGZvbGxvd0JyYW5kQ29sb3JzOiBib29sZWFuIH0sXHJcbiAgYXJ0aWZhY3RJZHM6IHN0cmluZ1tdID0gW10sXHJcbiAgdXNlRW5oYW5jZWREZXNpZ246IGJvb2xlYW4gPSB0cnVlLFxyXG4gIGluY2x1ZGVQZW9wbGVJbkRlc2lnbnM6IGJvb2xlYW4gPSB0cnVlLFxyXG4gIHVzZUxvY2FsTGFuZ3VhZ2U6IGJvb2xlYW4gPSBmYWxzZVxyXG4pOiBQcm9taXNlPEdlbmVyYXRlZFBvc3Q+IHtcclxuICB0cnkge1xyXG4gICAgY29uc29sZS5sb2coJ/CfjqggR2VuZXJhdGluZyBjb250ZW50IHdpdGggYXJ0aWZhY3RzLi4uJyk7XHJcbiAgICBjb25zb2xlLmxvZygnLSBQbGF0Zm9ybTonLCBwbGF0Zm9ybSk7XHJcbiAgICBjb25zb2xlLmxvZygnLSBBcnRpZmFjdHM6JywgYXJ0aWZhY3RJZHMubGVuZ3RoKTtcclxuICAgIGNvbnNvbGUubG9nKCctIEVuaGFuY2VkIERlc2lnbjonLCB1c2VFbmhhbmNlZERlc2lnbik7XHJcblxyXG4gICAgLy8gR2V0IGFjdGl2ZSBhcnRpZmFjdHMgaWYgbm8gc3BlY2lmaWMgYXJ0aWZhY3RzIHByb3ZpZGVkXHJcbiAgICBsZXQgdGFyZ2V0QXJ0aWZhY3RzOiBBcnRpZmFjdFtdID0gW107XHJcblxyXG4gICAgaWYgKGFydGlmYWN0SWRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgLy8gVXNlIHNwZWNpZmllZCBhcnRpZmFjdHNcclxuICAgICAgZm9yIChjb25zdCBhcnRpZmFjdElkIG9mIGFydGlmYWN0SWRzKSB7XHJcbiAgICAgICAgY29uc3QgYXJ0aWZhY3QgPSBhcnRpZmFjdHNTZXJ2aWNlLmdldEFydGlmYWN0KGFydGlmYWN0SWQpO1xyXG4gICAgICAgIGlmIChhcnRpZmFjdCkge1xyXG4gICAgICAgICAgdGFyZ2V0QXJ0aWZhY3RzLnB1c2goYXJ0aWZhY3QpO1xyXG4gICAgICAgICAgYXdhaXQgYXJ0aWZhY3RzU2VydmljZS50cmFja1VzYWdlKGFydGlmYWN0SWQsICdxdWljay1jb250ZW50Jyk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBVc2UgYWN0aXZlIGFydGlmYWN0cywgcHJpb3JpdGl6aW5nIGV4YWN0LXVzZVxyXG4gICAgICBjb25zdCBhY3RpdmVBcnRpZmFjdHMgPSBhcnRpZmFjdHNTZXJ2aWNlLmdldEFjdGl2ZUFydGlmYWN0cygpO1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBBY3RpdmUgYXJ0aWZhY3RzIGZvdW5kOicsIGFjdGl2ZUFydGlmYWN0cy5sZW5ndGgpO1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+TiyBBY3RpdmUgYXJ0aWZhY3RzIGRldGFpbHM6JywgYWN0aXZlQXJ0aWZhY3RzLm1hcChhID0+ICh7XHJcbiAgICAgICAgaWQ6IGEuaWQsXHJcbiAgICAgICAgbmFtZTogYS5uYW1lLFxyXG4gICAgICAgIHR5cGU6IGEudHlwZSxcclxuICAgICAgICB1c2FnZVR5cGU6IGEudXNhZ2VUeXBlLFxyXG4gICAgICAgIGlzQWN0aXZlOiBhLmlzQWN0aXZlLFxyXG4gICAgICAgIGluc3RydWN0aW9uczogYS5pbnN0cnVjdGlvbnNcclxuICAgICAgfSkpKTtcclxuXHJcbiAgICAgIGNvbnN0IGV4YWN0VXNlQXJ0aWZhY3RzID0gYWN0aXZlQXJ0aWZhY3RzLmZpbHRlcihhID0+IGEudXNhZ2VUeXBlID09PSAnZXhhY3QtdXNlJyk7XHJcbiAgICAgIGNvbnN0IHJlZmVyZW5jZUFydGlmYWN0cyA9IGFjdGl2ZUFydGlmYWN0cy5maWx0ZXIoYSA9PiBhLnVzYWdlVHlwZSA9PT0gJ3JlZmVyZW5jZScpO1xyXG5cclxuICAgICAgLy8gUHJpb3JpdGl6ZSBleGFjdC11c2UgYXJ0aWZhY3RzXHJcbiAgICAgIHRhcmdldEFydGlmYWN0cyA9IFsuLi5leGFjdFVzZUFydGlmYWN0cywgLi4ucmVmZXJlbmNlQXJ0aWZhY3RzLnNsaWNlKDAsIDMpXTtcclxuXHJcbiAgICAgIC8vIFRyYWNrIHVzYWdlIGZvciBhY3RpdmUgYXJ0aWZhY3RzXHJcbiAgICAgIGZvciAoY29uc3QgYXJ0aWZhY3Qgb2YgdGFyZ2V0QXJ0aWZhY3RzKSB7XHJcbiAgICAgICAgYXdhaXQgYXJ0aWZhY3RzU2VydmljZS50cmFja1VzYWdlKGFydGlmYWN0LmlkLCAncXVpY2stY29udGVudCcpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coJ/Cfk44gVXNpbmcgYXJ0aWZhY3RzOicsIHRhcmdldEFydGlmYWN0cy5tYXAoYSA9PiBgJHthLm5hbWV9ICgke2EudXNhZ2VUeXBlfSlgKSk7XHJcblxyXG4gICAgLy8gR2VuZXJhdGUgYmFzZSBjb250ZW50IGZpcnN0XHJcbiAgICBjb25zdCBiYXNlUG9zdCA9IGF3YWl0IGdlbmVyYXRlQ29udGVudEFjdGlvbihwcm9maWxlLCBwbGF0Zm9ybSwgYnJhbmRDb25zaXN0ZW5jeSk7XHJcblxyXG4gICAgLy8gSWYgZW5oYW5jZWQgZGVzaWduIGlzIGRpc2FibGVkLCByZXR1cm4gYmFzZSBjb250ZW50XHJcbiAgICBpZiAoIXVzZUVuaGFuY2VkRGVzaWduKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIEVuaGFuY2VkIGRlc2lnbiBkaXNhYmxlZCwgdXNpbmcgYmFzZSBjb250ZW50IGdlbmVyYXRpb24nKTtcclxuICAgICAgcmV0dXJuIGJhc2VQb3N0O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEVuaGFuY2VkIGRlc2lnbiBpcyBlbmFibGVkIC0gYWx3YXlzIHVzZSBlbmhhbmNlZCBnZW5lcmF0aW9uIHJlZ2FyZGxlc3Mgb2YgYXJ0aWZhY3RzXHJcbiAgICBjb25zb2xlLmxvZygn8J+OqCBFbmhhbmNlZCBkZXNpZ24gZW5hYmxlZCAtIHByb2NlZWRpbmcgd2l0aCBlbmhhbmNlZCBnZW5lcmF0aW9uJyk7XHJcbiAgICBjb25zb2xlLmxvZyhg8J+TiiBBcnRpZmFjdHMgYXZhaWxhYmxlOiAke3RhcmdldEFydGlmYWN0cy5sZW5ndGh9YCk7XHJcblxyXG4gICAgaWYgKHRhcmdldEFydGlmYWN0cy5sZW5ndGggPT09IDApIHtcclxuICAgICAgY29uc29sZS5sb2coJ+KcqCBObyBhcnRpZmFjdHMgcHJvdmlkZWQgLSB1c2luZyBlbmhhbmNlZCBkZXNpZ24gd2l0aG91dCBhcnRpZmFjdCBjb250ZXh0Jyk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+OryBVc2luZyBlbmhhbmNlZCBkZXNpZ24gd2l0aCBhcnRpZmFjdCBjb250ZXh0Jyk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gU2VwYXJhdGUgZXhhY3QtdXNlIGFuZCByZWZlcmVuY2UgYXJ0aWZhY3RzXHJcbiAgICBjb25zdCBleGFjdFVzZUFydGlmYWN0cyA9IHRhcmdldEFydGlmYWN0cy5maWx0ZXIoYSA9PiBhLnVzYWdlVHlwZSA9PT0gJ2V4YWN0LXVzZScpO1xyXG4gICAgY29uc3QgcmVmZXJlbmNlQXJ0aWZhY3RzID0gdGFyZ2V0QXJ0aWZhY3RzLmZpbHRlcihhID0+IGEudXNhZ2VUeXBlID09PSAncmVmZXJlbmNlJyk7XHJcblxyXG4gICAgLy8gQ3JlYXRlIGVuaGFuY2VkIGltYWdlIHRleHQgc3RydWN0dXJlIGZyb20gcG9zdCBjb21wb25lbnRzXHJcbiAgICBsZXQgZW5oYW5jZWRJbWFnZVRleHQ6IHsgY2F0Y2h5V29yZHM6IHN0cmluZzsgc3ViaGVhZGxpbmU/OiBzdHJpbmc7IGNhbGxUb0FjdGlvbj86IHN0cmluZyB9ID0ge1xyXG4gICAgICBjYXRjaHlXb3JkczogYmFzZVBvc3QuY2F0Y2h5V29yZHMgfHwgJ0VuZ2FnaW5nIENvbnRlbnQnLFxyXG4gICAgICBzdWJoZWFkbGluZTogYmFzZVBvc3Quc3ViaGVhZGxpbmUsXHJcbiAgICAgIGNhbGxUb0FjdGlvbjogYmFzZVBvc3QuY2FsbFRvQWN0aW9uXHJcbiAgICB9O1xyXG4gICAgbGV0IGVuaGFuY2VkQ29udGVudCA9IGJhc2VQb3N0LmNvbnRlbnQ7XHJcblxyXG4gICAgLy8gQ29sbGVjdCB1c2FnZSBpbnN0cnVjdGlvbnMgZnJvbSBhcnRpZmFjdHNcclxuICAgIGNvbnN0IGFydGlmYWN0SW5zdHJ1Y3Rpb25zID0gdGFyZ2V0QXJ0aWZhY3RzXHJcbiAgICAgIC5maWx0ZXIoYSA9PiBhLmluc3RydWN0aW9ucyAmJiBhLmluc3RydWN0aW9ucy50cmltKCkpXHJcbiAgICAgIC5tYXAoYSA9PiBgLSAke2EubmFtZX06ICR7YS5pbnN0cnVjdGlvbnN9YClcclxuICAgICAgLmpvaW4oJ1xcbicpO1xyXG5cclxuICAgIC8vIENvbGxlY3QgdGV4dCBvdmVybGF5IGluc3RydWN0aW9ucyBmcm9tIHRleHQgYXJ0aWZhY3RzXHJcbiAgICBjb25zdCB0ZXh0T3ZlcmxheUluc3RydWN0aW9ucyA9IGV4YWN0VXNlQXJ0aWZhY3RzXHJcbiAgICAgIC5maWx0ZXIoYSA9PiBhLnRleHRPdmVybGF5Py5pbnN0cnVjdGlvbnMgJiYgYS50ZXh0T3ZlcmxheS5pbnN0cnVjdGlvbnMudHJpbSgpKVxyXG4gICAgICAubWFwKGEgPT4gYC0gJHthLm5hbWV9OiAke2EudGV4dE92ZXJsYXkuaW5zdHJ1Y3Rpb25zfWApXHJcbiAgICAgIC5qb2luKCdcXG4nKTtcclxuXHJcbiAgICAvLyBQcm9jZXNzIGV4YWN0LXVzZSBhcnRpZmFjdHMgZmlyc3QgKGhpZ2hlciBwcmlvcml0eSlcclxuICAgIGlmIChleGFjdFVzZUFydGlmYWN0cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgIGNvbnN0IHByaW1hcnlFeGFjdFVzZSA9IGV4YWN0VXNlQXJ0aWZhY3RzWzBdO1xyXG5cclxuICAgICAgLy8gVXNlIHRleHQgb3ZlcmxheSBpZiBhdmFpbGFibGVcclxuICAgICAgaWYgKHByaW1hcnlFeGFjdFVzZS50ZXh0T3ZlcmxheSkge1xyXG4gICAgICAgIGlmIChwcmltYXJ5RXhhY3RVc2UudGV4dE92ZXJsYXkuaGVhZGxpbmUpIHtcclxuICAgICAgICAgIGVuaGFuY2VkSW1hZ2VUZXh0LmNhdGNoeVdvcmRzID0gcHJpbWFyeUV4YWN0VXNlLnRleHRPdmVybGF5LmhlYWRsaW5lO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfk50gVXNpbmcgaGVhZGxpbmUgZnJvbSBleGFjdC11c2UgYXJ0aWZhY3Q6JywgZW5oYW5jZWRJbWFnZVRleHQuY2F0Y2h5V29yZHMpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKHByaW1hcnlFeGFjdFVzZS50ZXh0T3ZlcmxheS5tZXNzYWdlKSB7XHJcbiAgICAgICAgICBlbmhhbmNlZENvbnRlbnQgPSBwcmltYXJ5RXhhY3RVc2UudGV4dE92ZXJsYXkubWVzc2FnZTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OdIFVzaW5nIG1lc3NhZ2UgZnJvbSBleGFjdC11c2UgYXJ0aWZhY3QnKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIFVzZSBDVEEgZnJvbSBhcnRpZmFjdCBpZiBhdmFpbGFibGVcclxuICAgICAgICBpZiAocHJpbWFyeUV4YWN0VXNlLnRleHRPdmVybGF5LmN0YSkge1xyXG4gICAgICAgICAgZW5oYW5jZWRJbWFnZVRleHQuY2FsbFRvQWN0aW9uID0gcHJpbWFyeUV4YWN0VXNlLnRleHRPdmVybGF5LmN0YTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OdIFVzaW5nIENUQSBmcm9tIGV4YWN0LXVzZSBhcnRpZmFjdDonLCBlbmhhbmNlZEltYWdlVGV4dC5jYWxsVG9BY3Rpb24pO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIFByb2Nlc3MgcmVmZXJlbmNlIGFydGlmYWN0cyBmb3Igc3R5bGUgZ3VpZGFuY2VcclxuICAgIGNvbnN0IGFjdGl2ZURpcmVjdGl2ZXMgPSByZWZlcmVuY2VBcnRpZmFjdHMuZmxhdE1hcChhcnRpZmFjdCA9PlxyXG4gICAgICBhcnRpZmFjdC5kaXJlY3RpdmVzLmZpbHRlcihkaXJlY3RpdmUgPT4gZGlyZWN0aXZlLmFjdGl2ZSlcclxuICAgICk7XHJcblxyXG4gICAgLy8gQXBwbHkgc3R5bGUgcmVmZXJlbmNlIGRpcmVjdGl2ZXNcclxuICAgIGNvbnN0IHN0eWxlRGlyZWN0aXZlcyA9IGFjdGl2ZURpcmVjdGl2ZXMuZmlsdGVyKGQgPT4gZC50eXBlID09PSAnc3R5bGUtcmVmZXJlbmNlJyk7XHJcbiAgICBsZXQgdmlzdWFsU3R5bGVPdmVycmlkZSA9IHByb2ZpbGUudmlzdWFsU3R5bGUgfHwgJ21vZGVybic7XHJcbiAgICBpZiAoc3R5bGVEaXJlY3RpdmVzLmxlbmd0aCA+IDApIHtcclxuICAgICAgY29uc29sZS5sb2coJ/CfjqggQXBwbHlpbmcgc3R5bGUgcmVmZXJlbmNlcyBmcm9tIGFydGlmYWN0cycpO1xyXG4gICAgICBjb25zdCBwcmltYXJ5U3R5bGVEaXJlY3RpdmUgPSBzdHlsZURpcmVjdGl2ZXMuZmluZChkID0+IGQucHJpb3JpdHkgPj0gNyk7XHJcbiAgICAgIGlmIChwcmltYXJ5U3R5bGVEaXJlY3RpdmUpIHtcclxuICAgICAgICB2aXN1YWxTdHlsZU92ZXJyaWRlID0gJ2FydGlmYWN0LWluc3BpcmVkJztcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+OqCBVc2luZyBhcnRpZmFjdC1pbnNwaXJlZCB2aXN1YWwgc3R5bGUnKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIENvbWJpbmUgYWxsIGluc3RydWN0aW9uc1xyXG4gICAgY29uc3QgYWxsSW5zdHJ1Y3Rpb25zID0gW2FydGlmYWN0SW5zdHJ1Y3Rpb25zLCB0ZXh0T3ZlcmxheUluc3RydWN0aW9uc11cclxuICAgICAgLmZpbHRlcihCb29sZWFuKVxyXG4gICAgICAuam9pbignXFxuJyk7XHJcblxyXG4gICAgLy8gR2VuZXJhdGUgZW5oYW5jZWQgZGVzaWduIHdpdGggYXJ0aWZhY3QgY29udGV4dFxyXG4gICAgY29uc3QgZW5oYW5jZWRSZXN1bHQgPSBhd2FpdCBnZW5lcmF0ZUVuaGFuY2VkRGVzaWduQWN0aW9uKFxyXG4gICAgICBwcm9maWxlLmJ1c2luZXNzVHlwZSB8fCAnYnVzaW5lc3MnLFxyXG4gICAgICBwbGF0Zm9ybS50b0xvd2VyQ2FzZSgpLFxyXG4gICAgICB2aXN1YWxTdHlsZU92ZXJyaWRlLFxyXG4gICAgICBlbmhhbmNlZEltYWdlVGV4dCxcclxuICAgICAgcHJvZmlsZSxcclxuICAgICAgdHJ1ZSxcclxuICAgICAgYnJhbmRDb25zaXN0ZW5jeSxcclxuICAgICAgYWxsSW5zdHJ1Y3Rpb25zIHx8IHVuZGVmaW5lZCxcclxuICAgICAgaW5jbHVkZVBlb3BsZUluRGVzaWducyxcclxuICAgICAgdXNlTG9jYWxMYW5ndWFnZVxyXG4gICAgKTtcclxuXHJcbiAgICAvLyBDcmVhdGUgZW5oYW5jZWQgcG9zdCB3aXRoIGFydGlmYWN0IG1ldGFkYXRhXHJcbiAgICBjb25zdCBlbmhhbmNlZFBvc3Q6IEdlbmVyYXRlZFBvc3QgPSB7XHJcbiAgICAgIC4uLmJhc2VQb3N0LFxyXG4gICAgICBpZDogRGF0ZS5ub3coKS50b1N0cmluZygpLFxyXG4gICAgICB2YXJpYW50czogW3tcclxuICAgICAgICBwbGF0Zm9ybTogcGxhdGZvcm0sXHJcbiAgICAgICAgaW1hZ2VVcmw6IGVuaGFuY2VkUmVzdWx0LmltYWdlVXJsXHJcbiAgICAgIH1dLFxyXG4gICAgICBjb250ZW50OiB0YXJnZXRBcnRpZmFjdHMubGVuZ3RoID4gMFxyXG4gICAgICAgID8gYCR7ZW5oYW5jZWRDb250ZW50fVxcblxcbuKcqCBFbmhhbmNlZCB3aXRoIEFJKyB1c2luZyAke3RhcmdldEFydGlmYWN0cy5sZW5ndGh9IHJlZmVyZW5jZSR7dGFyZ2V0QXJ0aWZhY3RzLmxlbmd0aCAhPT0gMSA/ICdzJyA6ICcnfSAoUXVhbGl0eTogJHtlbmhhbmNlZFJlc3VsdC5xdWFsaXR5U2NvcmV9LzEwKWBcclxuICAgICAgICA6IGAke2VuaGFuY2VkQ29udGVudH1cXG5cXG7inKggRW5oYW5jZWQgd2l0aCBBSSsgRGVzaWduIEdlbmVyYXRpb24gKFF1YWxpdHk6ICR7ZW5oYW5jZWRSZXN1bHQucXVhbGl0eVNjb3JlfS8xMClgLFxyXG4gICAgICBkYXRlOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIC8vIEFkZCBhcnRpZmFjdCBtZXRhZGF0YVxyXG4gICAgICBtZXRhZGF0YToge1xyXG4gICAgICAgIC4uLmJhc2VQb3N0Lm1ldGFkYXRhLFxyXG4gICAgICAgIHJlZmVyZW5jZWRBcnRpZmFjdHM6IHRhcmdldEFydGlmYWN0cy5tYXAoYSA9PiAoe1xyXG4gICAgICAgICAgaWQ6IGEuaWQsXHJcbiAgICAgICAgICBuYW1lOiBhLm5hbWUsXHJcbiAgICAgICAgICB0eXBlOiBhLnR5cGUsXHJcbiAgICAgICAgICBjYXRlZ29yeTogYS5jYXRlZ29yeVxyXG4gICAgICAgIH0pKSxcclxuICAgICAgICBhY3RpdmVEaXJlY3RpdmVzOiBhY3RpdmVEaXJlY3RpdmVzLm1hcChkID0+ICh7XHJcbiAgICAgICAgICBpZDogZC5pZCxcclxuICAgICAgICAgIHR5cGU6IGQudHlwZSxcclxuICAgICAgICAgIGxhYmVsOiBkLmxhYmVsLFxyXG4gICAgICAgICAgcHJpb3JpdHk6IGQucHJpb3JpdHlcclxuICAgICAgICB9KSlcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBjb25zb2xlLmxvZygn4pyFIEVuaGFuY2VkIGNvbnRlbnQgd2l0aCBhcnRpZmFjdHMgZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseScpO1xyXG4gICAgcmV0dXJuIGVuaGFuY2VkUG9zdDtcclxuXHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZW5lcmF0aW5nIGNvbnRlbnQgd2l0aCBhcnRpZmFjdHM6XCIsIGVycm9yKTtcclxuICAgIHRocm93IG5ldyBFcnJvcigoZXJyb3IgYXMgRXJyb3IpLm1lc3NhZ2UpO1xyXG4gIH1cclxufVxyXG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjhSQXlCc0IifQ==
}}),
"[project]/src/app/data:7738c2 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"78f16784e7e21db600c083fc17664954c78d989188":"generateContentAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateContentAction": (()=>generateContentAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateContentAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("78f16784e7e21db600c083fc17664954c78d989188", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateContentAction"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/app/data:3ba49a [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"707f98327e60040c1b2ee19b7969f1295904475702":"generateVideoContentAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateVideoContentAction": (()=>generateVideoContentAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateVideoContentAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("707f98327e60040c1b2ee19b7969f1295904475702", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateVideoContentAction"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/app/data:c92492 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f79b801f803feb5e76e403a11c5c7baf170b29d06":"generateCreativeAssetAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateCreativeAssetAction": (()=>generateCreativeAssetAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateCreativeAssetAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("7f79b801f803feb5e76e403a11c5c7baf170b29d06", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateCreativeAssetAction"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/app/data:2fe01c [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f491863d2325193ec3f40e1aa06031f7511fddf7d":"generateEnhancedDesignAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateEnhancedDesignAction": (()=>generateEnhancedDesignAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateEnhancedDesignAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("7f491863d2325193ec3f40e1aa06031f7511fddf7d", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateEnhancedDesignAction"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/app/data:624089 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f20bd895b2f9b19034d2b89ca4aad1c2c7dd696c8":"generateGeminiHDDesignAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateGeminiHDDesignAction": (()=>generateGeminiHDDesignAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateGeminiHDDesignAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("7f20bd895b2f9b19034d2b89ca4aad1c2c7dd696c8", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateGeminiHDDesignAction"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/app/data:052bf2 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f2ccbac81b04d4b1a191d767822e0665844079bf0":"generateContentWithArtifactsAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateContentWithArtifactsAction": (()=>generateContentWithArtifactsAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateContentWithArtifactsAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("7f2ccbac81b04d4b1a191d767822e0665844079bf0", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateContentWithArtifactsAction"); //# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4vYWN0aW9ucy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvYXBwL2FjdGlvbnMudHNcclxuXCJ1c2Ugc2VydmVyXCI7XHJcblxyXG5pbXBvcnQgeyBhbmFseXplQnJhbmQgYXMgYW5hbHl6ZUJyYW5kRmxvdywgQnJhbmRBbmFseXNpc1Jlc3VsdCB9IGZyb20gXCJAL2FpL2Zsb3dzL2FuYWx5emUtYnJhbmRcIjtcclxuaW1wb3J0IHsgZ2VuZXJhdGVQb3N0RnJvbVByb2ZpbGUgYXMgZ2VuZXJhdGVQb3N0RnJvbVByb2ZpbGVGbG93IH0gZnJvbSBcIkAvYWkvZmxvd3MvZ2VuZXJhdGUtcG9zdC1mcm9tLXByb2ZpbGVcIjtcclxuaW1wb3J0IHsgZ2VuZXJhdGVWaWRlb1Bvc3QgYXMgZ2VuZXJhdGVWaWRlb1Bvc3RGbG93IH0gZnJvbSBcIkAvYWkvZmxvd3MvZ2VuZXJhdGUtdmlkZW8tcG9zdFwiO1xyXG5pbXBvcnQgeyBnZW5lcmF0ZUNyZWF0aXZlQXNzZXQgYXMgZ2VuZXJhdGVDcmVhdGl2ZUFzc2V0RmxvdyB9IGZyb20gXCJAL2FpL2Zsb3dzL2dlbmVyYXRlLWNyZWF0aXZlLWFzc2V0XCI7XHJcbmltcG9ydCB0eXBlIHsgQnJhbmRQcm9maWxlLCBHZW5lcmF0ZWRQb3N0LCBQbGF0Zm9ybSwgQ3JlYXRpdmVBc3NldCB9IGZyb20gXCJAL2xpYi90eXBlc1wiO1xyXG5pbXBvcnQgeyBhcnRpZmFjdHNTZXJ2aWNlIH0gZnJvbSBcIkAvbGliL3NlcnZpY2VzL2FydGlmYWN0cy1zZXJ2aWNlXCI7XHJcbmltcG9ydCB0eXBlIHsgQXJ0aWZhY3QgfSBmcm9tIFwiQC9saWIvdHlwZXMvYXJ0aWZhY3RzXCI7XHJcbmltcG9ydCB7IGdlbmVyYXRlRW5oYW5jZWREZXNpZ24gfSBmcm9tIFwiQC9haS9nZW1pbmktMi41LWRlc2lnblwiO1xyXG5pbXBvcnQgeyBnZW5lcmF0ZVJldm8yQ29udGVudEFjdGlvbiwgZ2VuZXJhdGVSZXZvMkNyZWF0aXZlQXNzZXRBY3Rpb24gfSBmcm9tIFwiQC9hcHAvYWN0aW9ucy9yZXZvLTItYWN0aW9uc1wiO1xyXG5cclxuXHJcbi8vIC0tLSBBSSBGbG93IEFjdGlvbnMgLS0tXHJcblxyXG50eXBlIEFuYWx5c2lzUmVzdWx0ID0ge1xyXG4gIHN1Y2Nlc3M6IHRydWU7XHJcbiAgZGF0YTogQnJhbmRBbmFseXNpc1Jlc3VsdDtcclxufSB8IHtcclxuICBzdWNjZXNzOiBmYWxzZTtcclxuICBlcnJvcjogc3RyaW5nO1xyXG4gIGVycm9yVHlwZTogJ2Jsb2NrZWQnIHwgJ3RpbWVvdXQnIHwgJ2Vycm9yJztcclxufTtcclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBhbmFseXplQnJhbmRBY3Rpb24oXHJcbiAgd2Vic2l0ZVVybDogc3RyaW5nLFxyXG4gIGRlc2lnbkltYWdlVXJpczogc3RyaW5nW10sXHJcbik6IFByb21pc2U8QW5hbHlzaXNSZXN1bHQ+IHtcclxuICB0cnkge1xyXG4gICAgY29uc29sZS5sb2coXCLwn5SNIFN0YXJ0aW5nIGJyYW5kIGFuYWx5c2lzIGZvciBVUkw6XCIsIHdlYnNpdGVVcmwpO1xyXG4gICAgY29uc29sZS5sb2coXCLwn5a877iPIERlc2lnbiBpbWFnZXMgY291bnQ6XCIsIGRlc2lnbkltYWdlVXJpcy5sZW5ndGgpO1xyXG5cclxuICAgIC8vIFZhbGlkYXRlIFVSTCBmb3JtYXRcclxuICAgIGlmICghd2Vic2l0ZVVybCB8fCAhd2Vic2l0ZVVybC50cmltKCkpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgICBlcnJvcjogXCJXZWJzaXRlIFVSTCBpcyByZXF1aXJlZFwiLFxyXG4gICAgICAgIGVycm9yVHlwZTogJ2Vycm9yJ1xyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEVuc3VyZSBVUkwgaGFzIHByb3RvY29sXHJcbiAgICBsZXQgdmFsaWRVcmwgPSB3ZWJzaXRlVXJsLnRyaW0oKTtcclxuICAgIGlmICghdmFsaWRVcmwuc3RhcnRzV2l0aCgnaHR0cDovLycpICYmICF2YWxpZFVybC5zdGFydHNXaXRoKCdodHRwczovLycpKSB7XHJcbiAgICAgIHZhbGlkVXJsID0gJ2h0dHBzOi8vJyArIHZhbGlkVXJsO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGFuYWx5emVCcmFuZEZsb3coe1xyXG4gICAgICB3ZWJzaXRlVXJsOiB2YWxpZFVybCxcclxuICAgICAgZGVzaWduSW1hZ2VVcmlzOiBkZXNpZ25JbWFnZVVyaXMgfHwgW11cclxuICAgIH0pO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKFwi4pyFIEJyYW5kIGFuYWx5c2lzIHJlc3VsdDpcIiwgSlNPTi5zdHJpbmdpZnkocmVzdWx0LCBudWxsLCAyKSk7XHJcbiAgICBjb25zb2xlLmxvZyhcIvCflI0gUmVzdWx0IHR5cGU6XCIsIHR5cGVvZiByZXN1bHQpO1xyXG4gICAgY29uc29sZS5sb2coXCLwn5SNIFJlc3VsdCBrZXlzOlwiLCByZXN1bHQgPyBPYmplY3Qua2V5cyhyZXN1bHQpIDogXCJObyByZXN1bHRcIik7XHJcblxyXG4gICAgaWYgKCFyZXN1bHQpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgICBlcnJvcjogXCJBbmFseXNpcyByZXR1cm5lZCBlbXB0eSByZXN1bHRcIixcclxuICAgICAgICBlcnJvclR5cGU6ICdlcnJvcidcclxuICAgICAgfTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICBkYXRhOiByZXN1bHRcclxuICAgIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCLinYwgRXJyb3IgYW5hbHl6aW5nIGJyYW5kOlwiLCBlcnJvcik7XHJcblxyXG4gICAgLy8gUmV0dXJuIHN0cnVjdHVyZWQgZXJyb3IgcmVzcG9uc2UgaW5zdGVhZCBvZiB0aHJvd2luZ1xyXG4gICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBcIlVua25vd24gZXJyb3Igb2NjdXJyZWRcIjtcclxuXHJcbiAgICBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdmZXRjaCcpIHx8IGVycm9yTWVzc2FnZS5pbmNsdWRlcygnNDAzJykgfHwgZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdibG9ja2VkJykpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgICBlcnJvcjogXCJXZWJzaXRlIGJsb2NrcyBhdXRvbWF0ZWQgYWNjZXNzLiBUaGlzIGlzIGNvbW1vbiBmb3Igc2VjdXJpdHkgcmVhc29ucy5cIixcclxuICAgICAgICBlcnJvclR5cGU6ICdibG9ja2VkJ1xyXG4gICAgICB9O1xyXG4gICAgfSBlbHNlIGlmIChlcnJvck1lc3NhZ2UuaW5jbHVkZXMoJ3RpbWVvdXQnKSkge1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxyXG4gICAgICAgIGVycm9yOiBcIldlYnNpdGUgYW5hbHlzaXMgdGltZWQgb3V0LiBQbGVhc2UgdHJ5IGFnYWluIG9yIGNoZWNrIGlmIHRoZSB3ZWJzaXRlIGlzIGFjY2Vzc2libGUuXCIsXHJcbiAgICAgICAgZXJyb3JUeXBlOiAndGltZW91dCdcclxuICAgICAgfTtcclxuICAgIH0gZWxzZSBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdDT1JTJykpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgICBlcnJvcjogXCJXZWJzaXRlIGJsb2NrcyBhdXRvbWF0ZWQgYWNjZXNzLiBUaGlzIGlzIGNvbW1vbiBmb3Igc2VjdXJpdHkgcmVhc29ucy5cIixcclxuICAgICAgICBlcnJvclR5cGU6ICdibG9ja2VkJ1xyXG4gICAgICB9O1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgICBlcnJvcjogYEFuYWx5c2lzIGZhaWxlZDogJHtlcnJvck1lc3NhZ2V9YCxcclxuICAgICAgICBlcnJvclR5cGU6ICdlcnJvcidcclxuICAgICAgfTtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbmNvbnN0IGdldEFzcGVjdFJhdGlvRm9yUGxhdGZvcm0gPSAocGxhdGZvcm06IFBsYXRmb3JtKTogc3RyaW5nID0+IHtcclxuICBzd2l0Y2ggKHBsYXRmb3JtKSB7XHJcbiAgICBjYXNlICdJbnN0YWdyYW0nOlxyXG4gICAgICByZXR1cm4gJzE6MSc7IC8vIFNxdWFyZVxyXG4gICAgY2FzZSAnRmFjZWJvb2snOlxyXG4gICAgICByZXR1cm4gJzE2OjknOyAvLyBMYW5kc2NhcGUgLSBGYWNlYm9vayBwb3N0cyBhcmUgbGFuZHNjYXBlIGZvcm1hdFxyXG4gICAgY2FzZSAnVHdpdHRlcic6XHJcbiAgICAgIHJldHVybiAnMTY6OSc7IC8vIExhbmRzY2FwZVxyXG4gICAgY2FzZSAnTGlua2VkSW4nOlxyXG4gICAgICByZXR1cm4gJzE2OjknOyAvLyBMYW5kc2NhcGUgLSBMaW5rZWRJbiBwb3N0cyBhcmUgbGFuZHNjYXBlIGZvcm1hdFxyXG4gICAgZGVmYXVsdDpcclxuICAgICAgcmV0dXJuICcxOjEnO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdlbmVyYXRlQ29udGVudEFjdGlvbihcclxuICBwcm9maWxlOiBCcmFuZFByb2ZpbGUsXHJcbiAgcGxhdGZvcm06IFBsYXRmb3JtLFxyXG4gIGJyYW5kQ29uc2lzdGVuY3k/OiB7IHN0cmljdENvbnNpc3RlbmN5OiBib29sZWFuOyBmb2xsb3dCcmFuZENvbG9yczogYm9vbGVhbiB9LFxyXG4gIHVzZUxvY2FsTGFuZ3VhZ2U6IGJvb2xlYW4gPSBmYWxzZVxyXG4pOiBQcm9taXNlPEdlbmVyYXRlZFBvc3Q+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpO1xyXG4gICAgY29uc3QgZGF5T2ZXZWVrID0gdG9kYXkudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHsgd2Vla2RheTogJ2xvbmcnIH0pO1xyXG4gICAgY29uc3QgY3VycmVudERhdGUgPSB0b2RheS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywgeyB5ZWFyOiAnbnVtZXJpYycsIG1vbnRoOiAnbG9uZycsIGRheTogJ251bWVyaWMnIH0pO1xyXG5cclxuICAgIC8vIEFwcGx5IGJyYW5kIGNvbnNpc3RlbmN5IGxvZ2ljXHJcbiAgICBjb25zdCBlZmZlY3RpdmVEZXNpZ25FeGFtcGxlcyA9IGJyYW5kQ29uc2lzdGVuY3k/LnN0cmljdENvbnNpc3RlbmN5XHJcbiAgICAgID8gKHByb2ZpbGUuZGVzaWduRXhhbXBsZXMgfHwgW10pXHJcbiAgICAgIDogW107IC8vIERvbid0IHVzZSBkZXNpZ24gZXhhbXBsZXMgaWYgbm90IHN0cmljdCBjb25zaXN0ZW5jeVxyXG5cclxuICAgIC8vIEVuaGFuY2VkIGJyYW5kIHByb2ZpbGUgZGF0YSBleHRyYWN0aW9uXHJcbiAgICBjb25zdCBlbmhhbmNlZFByb2ZpbGUgPSB7XHJcbiAgICAgIC4uLnByb2ZpbGUsXHJcbiAgICAgIC8vIEVuc3VyZSBicmFuZCBjb2xvcnMgYXJlIGF2YWlsYWJsZVxyXG4gICAgICBwcmltYXJ5Q29sb3I6IHByb2ZpbGUucHJpbWFyeUNvbG9yIHx8ICcjM0I4MkY2JyxcclxuICAgICAgYWNjZW50Q29sb3I6IHByb2ZpbGUuYWNjZW50Q29sb3IgfHwgJyMxMEI5ODEnLFxyXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IHByb2ZpbGUuYmFja2dyb3VuZENvbG9yIHx8ICcjRjhGQUZDJyxcclxuICAgICAgLy8gRXh0cmFjdCBzZXJ2aWNlcyBpbmZvcm1hdGlvblxyXG4gICAgICBzZXJ2aWNlc0FycmF5OiB0eXBlb2YgcHJvZmlsZS5zZXJ2aWNlcyA9PT0gJ3N0cmluZydcclxuICAgICAgICA/IHByb2ZpbGUuc2VydmljZXMuc3BsaXQoJ1xcbicpLmZpbHRlcihzID0+IHMudHJpbSgpKVxyXG4gICAgICAgIDogQXJyYXkuaXNBcnJheShwcm9maWxlLnNlcnZpY2VzKVxyXG4gICAgICAgICAgPyBwcm9maWxlLnNlcnZpY2VzLm1hcChzID0+IHR5cGVvZiBzID09PSAnc3RyaW5nJyA/IHMgOiBzLm5hbWUgfHwgcy5kZXNjcmlwdGlvbiB8fCAnJylcclxuICAgICAgICAgIDogW10sXHJcbiAgICAgIC8vIEV4dHJhY3QgY29udGFjdCBpbmZvcm1hdGlvbiBmb3IgYnJhbmQgY29udGV4dFxyXG4gICAgICBjb250YWN0SW5mbzogcHJvZmlsZS5jb250YWN0SW5mbyB8fCB7fSxcclxuICAgICAgc29jaWFsTWVkaWE6IHByb2ZpbGUuc29jaWFsTWVkaWEgfHwge30sXHJcbiAgICB9O1xyXG5cclxuICAgIC8vIENvbnZlcnQgYXJyYXlzIHRvIG5ld2xpbmUtc2VwYXJhdGVkIHN0cmluZ3MgZm9yIEFJIHByb2Nlc3NpbmdcclxuICAgIGNvbnN0IGtleUZlYXR1cmVzU3RyaW5nID0gQXJyYXkuaXNBcnJheShwcm9maWxlLmtleUZlYXR1cmVzKVxyXG4gICAgICA/IHByb2ZpbGUua2V5RmVhdHVyZXMuam9pbignXFxuJylcclxuICAgICAgOiBwcm9maWxlLmtleUZlYXR1cmVzIHx8ICcnO1xyXG5cclxuICAgIGNvbnN0IGNvbXBldGl0aXZlQWR2YW50YWdlc1N0cmluZyA9IEFycmF5LmlzQXJyYXkocHJvZmlsZS5jb21wZXRpdGl2ZUFkdmFudGFnZXMpXHJcbiAgICAgID8gcHJvZmlsZS5jb21wZXRpdGl2ZUFkdmFudGFnZXMuam9pbignXFxuJylcclxuICAgICAgOiBwcm9maWxlLmNvbXBldGl0aXZlQWR2YW50YWdlcyB8fCAnJztcclxuXHJcbiAgICAvLyBDb252ZXJ0IHNlcnZpY2VzIGFycmF5IHRvIG5ld2xpbmUtc2VwYXJhdGVkIHN0cmluZ1xyXG4gICAgY29uc3Qgc2VydmljZXNTdHJpbmcgPSBBcnJheS5pc0FycmF5KHByb2ZpbGUuc2VydmljZXMpXHJcbiAgICAgID8gcHJvZmlsZS5zZXJ2aWNlcy5tYXAoc2VydmljZSA9PlxyXG4gICAgICAgIHR5cGVvZiBzZXJ2aWNlID09PSAnb2JqZWN0JyAmJiBzZXJ2aWNlLm5hbWVcclxuICAgICAgICAgID8gYCR7c2VydmljZS5uYW1lfTogJHtzZXJ2aWNlLmRlc2NyaXB0aW9uIHx8ICcnfWBcclxuICAgICAgICAgIDogc2VydmljZVxyXG4gICAgICApLmpvaW4oJ1xcbicpXHJcbiAgICAgIDogcHJvZmlsZS5zZXJ2aWNlcyB8fCAnJztcclxuXHJcblxyXG5cclxuICAgIGNvbnN0IHBvc3REZXRhaWxzID0gYXdhaXQgZ2VuZXJhdGVQb3N0RnJvbVByb2ZpbGVGbG93KHtcclxuICAgICAgYnVzaW5lc3NOYW1lOiBlbmhhbmNlZFByb2ZpbGUuYnVzaW5lc3NOYW1lLFxyXG4gICAgICBidXNpbmVzc1R5cGU6IGVuaGFuY2VkUHJvZmlsZS5idXNpbmVzc1R5cGUsXHJcbiAgICAgIGxvY2F0aW9uOiBlbmhhbmNlZFByb2ZpbGUubG9jYXRpb24sXHJcbiAgICAgIHdyaXRpbmdUb25lOiBlbmhhbmNlZFByb2ZpbGUud3JpdGluZ1RvbmUsXHJcbiAgICAgIGNvbnRlbnRUaGVtZXM6IGVuaGFuY2VkUHJvZmlsZS5jb250ZW50VGhlbWVzLFxyXG4gICAgICB2aXN1YWxTdHlsZTogZW5oYW5jZWRQcm9maWxlLnZpc3VhbFN0eWxlLFxyXG4gICAgICBsb2dvRGF0YVVybDogZW5oYW5jZWRQcm9maWxlLmxvZ29EYXRhVXJsLFxyXG4gICAgICBkZXNpZ25FeGFtcGxlczogZWZmZWN0aXZlRGVzaWduRXhhbXBsZXMsIC8vIFVzZSBkZXNpZ24gZXhhbXBsZXMgYmFzZWQgb24gY29uc2lzdGVuY3kgcHJlZmVyZW5jZVxyXG4gICAgICBwcmltYXJ5Q29sb3I6IGVuaGFuY2VkUHJvZmlsZS5wcmltYXJ5Q29sb3IsXHJcbiAgICAgIGFjY2VudENvbG9yOiBlbmhhbmNlZFByb2ZpbGUuYWNjZW50Q29sb3IsXHJcbiAgICAgIGJhY2tncm91bmRDb2xvcjogZW5oYW5jZWRQcm9maWxlLmJhY2tncm91bmRDb2xvcixcclxuICAgICAgZGF5T2ZXZWVrLFxyXG4gICAgICBjdXJyZW50RGF0ZSxcclxuICAgICAgdmFyaWFudHM6IFt7XHJcbiAgICAgICAgcGxhdGZvcm06IHBsYXRmb3JtLFxyXG4gICAgICAgIGFzcGVjdFJhdGlvOiBnZXRBc3BlY3RSYXRpb0ZvclBsYXRmb3JtKHBsYXRmb3JtKSxcclxuICAgICAgfV0sXHJcbiAgICAgIC8vIFBhc3MgbmV3IGRldGFpbGVkIGZpZWxkc1xyXG4gICAgICBzZXJ2aWNlczogc2VydmljZXNTdHJpbmcsXHJcbiAgICAgIHRhcmdldEF1ZGllbmNlOiBlbmhhbmNlZFByb2ZpbGUudGFyZ2V0QXVkaWVuY2UsXHJcbiAgICAgIGtleUZlYXR1cmVzOiBrZXlGZWF0dXJlc1N0cmluZyxcclxuICAgICAgY29tcGV0aXRpdmVBZHZhbnRhZ2VzOiBjb21wZXRpdGl2ZUFkdmFudGFnZXNTdHJpbmcsXHJcbiAgICAgIC8vIFBhc3MgYnJhbmQgY29uc2lzdGVuY3kgcHJlZmVyZW5jZXNcclxuICAgICAgYnJhbmRDb25zaXN0ZW5jeTogYnJhbmRDb25zaXN0ZW5jeSB8fCB7IHN0cmljdENvbnNpc3RlbmN5OiBmYWxzZSwgZm9sbG93QnJhbmRDb2xvcnM6IHRydWUgfSxcclxuICAgICAgLy8gRW5oYW5jZWQgYnJhbmQgY29udGV4dFxyXG4gICAgICB3ZWJzaXRlVXJsOiBlbmhhbmNlZFByb2ZpbGUud2Vic2l0ZVVybCxcclxuICAgICAgZGVzY3JpcHRpb246IGVuaGFuY2VkUHJvZmlsZS5kZXNjcmlwdGlvbixcclxuICAgICAgY29udGFjdEluZm86IGVuaGFuY2VkUHJvZmlsZS5jb250YWN0SW5mbyxcclxuICAgICAgc29jaWFsTWVkaWE6IGVuaGFuY2VkUHJvZmlsZS5zb2NpYWxNZWRpYSxcclxuICAgICAgLy8gTGFuZ3VhZ2UgcHJlZmVyZW5jZXNcclxuICAgICAgdXNlTG9jYWxMYW5ndWFnZTogdXNlTG9jYWxMYW5ndWFnZSxcclxuICAgIH0pO1xyXG5cclxuICAgIGNvbnN0IG5ld1Bvc3Q6IEdlbmVyYXRlZFBvc3QgPSB7XHJcbiAgICAgIGlkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIGRhdGU6IHRvZGF5LnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIGNvbnRlbnQ6IHBvc3REZXRhaWxzLmNvbnRlbnQsXHJcbiAgICAgIGhhc2h0YWdzOiBwb3N0RGV0YWlscy5oYXNodGFncyxcclxuICAgICAgc3RhdHVzOiAnZ2VuZXJhdGVkJyxcclxuICAgICAgdmFyaWFudHM6IHBvc3REZXRhaWxzLnZhcmlhbnRzLFxyXG4gICAgICBjYXRjaHlXb3JkczogcG9zdERldGFpbHMuY2F0Y2h5V29yZHMsXHJcbiAgICAgIHN1YmhlYWRsaW5lOiBwb3N0RGV0YWlscy5zdWJoZWFkbGluZSxcclxuICAgICAgY2FsbFRvQWN0aW9uOiBwb3N0RGV0YWlscy5jYWxsVG9BY3Rpb24sXHJcbiAgICAgIC8vIEluY2x1ZGUgZW5oYW5jZWQgQUkgZmVhdHVyZXNcclxuICAgICAgY29udGVudFZhcmlhbnRzOiBwb3N0RGV0YWlscy5jb250ZW50VmFyaWFudHMsXHJcbiAgICAgIGhhc2h0YWdBbmFseXNpczogcG9zdERldGFpbHMuaGFzaHRhZ0FuYWx5c2lzLFxyXG4gICAgICAvLyBJbmNsdWRlIGFkdmFuY2VkIEFJIGZlYXR1cmVzXHJcbiAgICAgIG1hcmtldEludGVsbGlnZW5jZTogcG9zdERldGFpbHMubWFya2V0SW50ZWxsaWdlbmNlLFxyXG4gICAgICAvLyBJbmNsdWRlIGxvY2FsIGNvbnRleHQgZmVhdHVyZXNcclxuICAgICAgbG9jYWxDb250ZXh0OiBwb3N0RGV0YWlscy5sb2NhbENvbnRleHQsXHJcbiAgICB9O1xyXG5cclxuICAgIHJldHVybiBuZXdQb3N0O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZ2VuZXJhdGluZyBjb250ZW50OlwiLCBlcnJvcik7XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZ2VuZXJhdGUgY29udGVudC4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci5cIik7XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVWaWRlb0NvbnRlbnRBY3Rpb24oXHJcbiAgcHJvZmlsZTogQnJhbmRQcm9maWxlLFxyXG4gIGNhdGNoeVdvcmRzOiBzdHJpbmcsXHJcbiAgcG9zdENvbnRlbnQ6IHN0cmluZyxcclxuKTogUHJvbWlzZTx7IHZpZGVvVXJsOiBzdHJpbmcgfT4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBnZW5lcmF0ZVZpZGVvUG9zdEZsb3coe1xyXG4gICAgICBidXNpbmVzc1R5cGU6IHByb2ZpbGUuYnVzaW5lc3NUeXBlLFxyXG4gICAgICBsb2NhdGlvbjogcHJvZmlsZS5sb2NhdGlvbixcclxuICAgICAgdmlzdWFsU3R5bGU6IHByb2ZpbGUudmlzdWFsU3R5bGUsXHJcbiAgICAgIGltYWdlVGV4dDogY2F0Y2h5V29yZHMsIC8vIFVzZSBjYXRjaHlXb3JkcyBhcyBpbWFnZVRleHQgZm9yIHZpZGVvIGdlbmVyYXRpb25cclxuICAgICAgcG9zdENvbnRlbnQ6IHBvc3RDb250ZW50LFxyXG4gICAgfSk7XHJcbiAgICByZXR1cm4geyB2aWRlb1VybDogcmVzdWx0LnZpZGVvVXJsIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZW5lcmF0aW5nIHZpZGVvIGNvbnRlbnQ6XCIsIGVycm9yKTtcclxuICAgIC8vIFBhc3MgdGhlIHNwZWNpZmljIGVycm9yIG1lc3NhZ2UgZnJvbSB0aGUgZmxvdyB0byB0aGUgY2xpZW50XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlKTtcclxuICB9XHJcbn1cclxuXHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVDcmVhdGl2ZUFzc2V0QWN0aW9uKFxyXG4gIHByb21wdDogc3RyaW5nLFxyXG4gIG91dHB1dFR5cGU6ICdpbWFnZScgfCAndmlkZW8nLFxyXG4gIHJlZmVyZW5jZUFzc2V0VXJsOiBzdHJpbmcgfCBudWxsLFxyXG4gIHVzZUJyYW5kUHJvZmlsZTogYm9vbGVhbixcclxuICBicmFuZFByb2ZpbGU6IEJyYW5kUHJvZmlsZSB8IG51bGwsXHJcbiAgbWFza0RhdGFVcmw6IHN0cmluZyB8IG51bGwgfCB1bmRlZmluZWQsXHJcbiAgYXNwZWN0UmF0aW86ICcxNjo5JyB8ICc5OjE2JyB8IHVuZGVmaW5lZFxyXG4pOiBQcm9taXNlPENyZWF0aXZlQXNzZXQ+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZ2VuZXJhdGVDcmVhdGl2ZUFzc2V0Rmxvdyh7XHJcbiAgICAgIHByb21wdCxcclxuICAgICAgb3V0cHV0VHlwZSxcclxuICAgICAgcmVmZXJlbmNlQXNzZXRVcmwsXHJcbiAgICAgIHVzZUJyYW5kUHJvZmlsZSxcclxuICAgICAgYnJhbmRQcm9maWxlOiB1c2VCcmFuZFByb2ZpbGUgPyBicmFuZFByb2ZpbGUgOiBudWxsLFxyXG4gICAgICBtYXNrRGF0YVVybCxcclxuICAgICAgYXNwZWN0UmF0aW8sXHJcbiAgICB9KTtcclxuICAgIHJldHVybiByZXN1bHQ7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZW5lcmF0aW5nIGNyZWF0aXZlIGFzc2V0OlwiLCBlcnJvcik7XHJcbiAgICAvLyBBbHdheXMgcGFzcyB0aGUgc3BlY2lmaWMgZXJyb3IgbWVzc2FnZSBmcm9tIHRoZSBmbG93IHRvIHRoZSBjbGllbnQuXHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlKTtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZW5lcmF0ZUVuaGFuY2VkRGVzaWduQWN0aW9uKFxyXG4gIGJ1c2luZXNzVHlwZTogc3RyaW5nLFxyXG4gIHBsYXRmb3JtOiBzdHJpbmcsXHJcbiAgdmlzdWFsU3R5bGU6IHN0cmluZyxcclxuICBpbWFnZVRleHQ6IHN0cmluZyB8IHsgY2F0Y2h5V29yZHM6IHN0cmluZzsgc3ViaGVhZGxpbmU/OiBzdHJpbmc7IGNhbGxUb0FjdGlvbj86IHN0cmluZyB9LFxyXG4gIGJyYW5kUHJvZmlsZT86IEJyYW5kUHJvZmlsZSxcclxuICBlbmFibGVFbmhhbmNlbWVudHM6IGJvb2xlYW4gPSB0cnVlLFxyXG4gIGJyYW5kQ29uc2lzdGVuY3k/OiB7IHN0cmljdENvbnNpc3RlbmN5OiBib29sZWFuOyBmb2xsb3dCcmFuZENvbG9yczogYm9vbGVhbiB9LFxyXG4gIGFydGlmYWN0SW5zdHJ1Y3Rpb25zPzogc3RyaW5nLFxyXG4gIGluY2x1ZGVQZW9wbGVJbkRlc2lnbnM6IGJvb2xlYW4gPSB0cnVlLFxyXG4gIHVzZUxvY2FsTGFuZ3VhZ2U6IGJvb2xlYW4gPSBmYWxzZVxyXG4pOiBQcm9taXNlPHtcclxuICBpbWFnZVVybDogc3RyaW5nO1xyXG4gIHF1YWxpdHlTY29yZTogbnVtYmVyO1xyXG4gIGVuaGFuY2VtZW50c0FwcGxpZWQ6IHN0cmluZ1tdO1xyXG4gIHByb2Nlc3NpbmdUaW1lOiBudW1iZXI7XHJcbn0+IHtcclxuICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xyXG4gIGNvbnN0IGVuaGFuY2VtZW50c0FwcGxpZWQ6IHN0cmluZ1tdID0gW107XHJcblxyXG4gIHRyeSB7XHJcbiAgICBpZiAoIWJyYW5kUHJvZmlsZSkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0JyYW5kIHByb2ZpbGUgaXMgcmVxdWlyZWQgZm9yIGVuaGFuY2VkIGRlc2lnbiBnZW5lcmF0aW9uJyk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gSGFuZGxlIGJvdGggb2xkIHN0cmluZyBmb3JtYXQgYW5kIG5ldyBvYmplY3QgZm9ybWF0XHJcbiAgICBsZXQgZmluYWxJbWFnZVRleHQ6IHN0cmluZztcclxuICAgIGlmICh0eXBlb2YgaW1hZ2VUZXh0ID09PSAnc3RyaW5nJykge1xyXG4gICAgICBmaW5hbEltYWdlVGV4dCA9IGltYWdlVGV4dDtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIENvbWJpbmUgY2F0Y2h5IHdvcmRzLCBzdWJoZWFkbGluZSwgYW5kIGNhbGwtdG8tYWN0aW9uXHJcbiAgICAgIGNvbnN0IGNvbXBvbmVudHMgPSBbaW1hZ2VUZXh0LmNhdGNoeVdvcmRzXTtcclxuICAgICAgaWYgKGltYWdlVGV4dC5zdWJoZWFkbGluZSAmJiBpbWFnZVRleHQuc3ViaGVhZGxpbmUudHJpbSgpKSB7XHJcbiAgICAgICAgY29tcG9uZW50cy5wdXNoKGltYWdlVGV4dC5zdWJoZWFkbGluZS50cmltKCkpO1xyXG4gICAgICB9XHJcbiAgICAgIGlmIChpbWFnZVRleHQuY2FsbFRvQWN0aW9uICYmIGltYWdlVGV4dC5jYWxsVG9BY3Rpb24udHJpbSgpKSB7XHJcbiAgICAgICAgY29tcG9uZW50cy5wdXNoKGltYWdlVGV4dC5jYWxsVG9BY3Rpb24udHJpbSgpKTtcclxuICAgICAgfVxyXG4gICAgICBmaW5hbEltYWdlVGV4dCA9IGNvbXBvbmVudHMuam9pbignXFxuJyk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coJ/CfjqggRW5oYW5jZWQgRGVzaWduIEdlbmVyYXRpb24gU3RhcnRlZCcpO1xyXG4gICAgY29uc29sZS5sb2coJy0gQnVzaW5lc3MgVHlwZTonLCBidXNpbmVzc1R5cGUpO1xyXG4gICAgY29uc29sZS5sb2coJy0gUGxhdGZvcm06JywgcGxhdGZvcm0pO1xyXG4gICAgY29uc29sZS5sb2coJy0gVmlzdWFsIFN0eWxlOicsIHZpc3VhbFN0eWxlKTtcclxuICAgIGNvbnNvbGUubG9nKCctIEltYWdlIFRleHQ6JywgZmluYWxJbWFnZVRleHQpO1xyXG4gICAgY29uc29sZS5sb2coJy0gQnJhbmQgUHJvZmlsZTonLCBicmFuZFByb2ZpbGUuYnVzaW5lc3NOYW1lKTtcclxuICAgIGNvbnNvbGUubG9nKCctIEVuaGFuY2VtZW50cyBFbmFibGVkOicsIGVuYWJsZUVuaGFuY2VtZW50cyk7XHJcblxyXG4gICAgLy8gVHJ5IEdlbWluaSAyLjUgZmlyc3QgKGJlc3QgcXVhbGl0eSksIHRoZW4gZmFsbGJhY2sgdG8gT3BlbkFJLCB0aGVuIEdlbWluaSAyLjAgSERcclxuICAgIGxldCByZXN1bHQ7XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coJ/CfmoAgVXNpbmcgR2VtaW5pIDIuNSBQcm8gZm9yIHN1cGVyaW9yIGRlc2lnbiBnZW5lcmF0aW9uLi4uJyk7XHJcblxyXG4gICAgICByZXN1bHQgPSBhd2FpdCBnZW5lcmF0ZUVuaGFuY2VkRGVzaWduKHtcclxuICAgICAgICBidXNpbmVzc1R5cGUsXHJcbiAgICAgICAgcGxhdGZvcm0sXHJcbiAgICAgICAgdmlzdWFsU3R5bGUsXHJcbiAgICAgICAgaW1hZ2VUZXh0OiBmaW5hbEltYWdlVGV4dCxcclxuICAgICAgICBicmFuZFByb2ZpbGUsXHJcbiAgICAgICAgYnJhbmRDb25zaXN0ZW5jeSxcclxuICAgICAgICBhcnRpZmFjdEluc3RydWN0aW9ucyxcclxuICAgICAgICBpbmNsdWRlUGVvcGxlSW5EZXNpZ25zLFxyXG4gICAgICAgIHVzZUxvY2FsTGFuZ3VhZ2UsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ+KchSBHZW1pbmkgMi41IGVuaGFuY2VkIGRlc2lnbiBnZW5lcmF0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICAgIGNvbnNvbGUubG9nKGDwn46vIFF1YWxpdHkgU2NvcmU6ICR7cmVzdWx0LnF1YWxpdHlTY29yZX0vMTBgKTtcclxuICAgICAgY29uc29sZS5sb2coYOKaoSBQcm9jZXNzaW5nIFRpbWU6ICR7cmVzdWx0LnByb2Nlc3NpbmdUaW1lfW1zYCk7XHJcblxyXG4gICAgfSBjYXRjaCAoZ2VtaW5pMjVFcnJvcikge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBHZW1pbmkgMi41IGdlbmVyYXRpb24gZmFpbGVkLCBmYWxsaW5nIGJhY2sgdG8gT3BlbkFJOicsIGdlbWluaTI1RXJyb3IpO1xyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+agCBVc2luZyBPcGVuQUkgR1BULUltYWdlIDEgZm9yIGVuaGFuY2VkIGRlc2lnbiBnZW5lcmF0aW9uLi4uJyk7XHJcbiAgICAgICAgY29uc3QgeyBnZW5lcmF0ZUVuaGFuY2VkRGVzaWduV2l0aEZhbGxiYWNrIH0gPSBhd2FpdCBpbXBvcnQoJ0AvYWkvb3BlbmFpLWVuaGFuY2VkLWRlc2lnbicpO1xyXG5cclxuICAgICAgICByZXN1bHQgPSBhd2FpdCBnZW5lcmF0ZUVuaGFuY2VkRGVzaWduV2l0aEZhbGxiYWNrKHtcclxuICAgICAgICAgIGJ1c2luZXNzVHlwZSxcclxuICAgICAgICAgIHBsYXRmb3JtLFxyXG4gICAgICAgICAgdmlzdWFsU3R5bGUsXHJcbiAgICAgICAgICBpbWFnZVRleHQ6IGZpbmFsSW1hZ2VUZXh0LFxyXG4gICAgICAgICAgYnJhbmRQcm9maWxlLFxyXG4gICAgICAgICAgYnJhbmRDb25zaXN0ZW5jeSxcclxuICAgICAgICAgIGFydGlmYWN0SW5zdHJ1Y3Rpb25zLFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZygn4pyFIE9wZW5BSSBHUFQtSW1hZ2UgMSBlbmhhbmNlZCBkZXNpZ24gZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseScpO1xyXG4gICAgICB9IGNhdGNoIChvcGVuYWlFcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIE9wZW5BSSBnZW5lcmF0aW9uIGFsc28gZmFpbGVkLCBmYWxsaW5nIGJhY2sgdG8gR2VtaW5pIDIuMCBIRDonLCBvcGVuYWlFcnJvcik7XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5qAIFVzaW5nIEdlbWluaSAyLjAgRmxhc2ggSEQgZm9yIGVuaGFuY2VkIGRlc2lnbiBnZW5lcmF0aW9uLi4uJyk7XHJcbiAgICAgICAgY29uc3QgeyBnZW5lcmF0ZUdlbWluaUhERW5oYW5jZWREZXNpZ25XaXRoRmFsbGJhY2sgfSA9IGF3YWl0IGltcG9ydCgnQC9haS9nZW1pbmktaGQtZW5oYW5jZWQtZGVzaWduJyk7XHJcblxyXG4gICAgICAgIHJlc3VsdCA9IGF3YWl0IGdlbmVyYXRlR2VtaW5pSERFbmhhbmNlZERlc2lnbldpdGhGYWxsYmFjayh7XHJcbiAgICAgICAgICBidXNpbmVzc1R5cGUsXHJcbiAgICAgICAgICBwbGF0Zm9ybSxcclxuICAgICAgICAgIHZpc3VhbFN0eWxlLFxyXG4gICAgICAgICAgaW1hZ2VUZXh0OiBmaW5hbEltYWdlVGV4dCxcclxuICAgICAgICAgIGJyYW5kUHJvZmlsZSxcclxuICAgICAgICAgIGJyYW5kQ29uc2lzdGVuY3ksXHJcbiAgICAgICAgICBhcnRpZmFjdEluc3RydWN0aW9ucyxcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBHZW1pbmkgMi4wIEhEIGVuaGFuY2VkIGRlc2lnbiBnZW5lcmF0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zb2xlLmxvZygn8J+UlyBJbWFnZSBVUkw6JywgcmVzdWx0LmltYWdlVXJsKTtcclxuICAgIGNvbnNvbGUubG9nKCfirZAgUXVhbGl0eSBTY29yZTonLCByZXN1bHQucXVhbGl0eVNjb3JlKTtcclxuICAgIGNvbnNvbGUubG9nKCfwn46vIEVuaGFuY2VtZW50cyBBcHBsaWVkOicsIHJlc3VsdC5lbmhhbmNlbWVudHNBcHBsaWVkKTtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBpbWFnZVVybDogcmVzdWx0LmltYWdlVXJsLFxyXG4gICAgICBxdWFsaXR5U2NvcmU6IHJlc3VsdC5xdWFsaXR5U2NvcmUsXHJcbiAgICAgIGVuaGFuY2VtZW50c0FwcGxpZWQ6IHJlc3VsdC5lbmhhbmNlbWVudHNBcHBsaWVkLFxyXG4gICAgICBwcm9jZXNzaW5nVGltZTogcmVzdWx0LnByb2Nlc3NpbmdUaW1lXHJcbiAgICB9O1xyXG5cclxuXHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZW5lcmF0aW5nIGVuaGFuY2VkIGRlc2lnbjpcIiwgZXJyb3IpO1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKChlcnJvciBhcyBFcnJvcikubWVzc2FnZSk7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogR2VuZXJhdGUgZW5oYW5jZWQgZGVzaWduIHNwZWNpZmljYWxseSB1c2luZyBHZW1pbmkgMi4wIEZsYXNoIEhEXHJcbiAqIFRoaXMgYWN0aW9uIGZvcmNlcyB0aGUgdXNlIG9mIEdlbWluaSBIRCBmb3IgbWF4aW11bSBxdWFsaXR5XHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVHZW1pbmlIRERlc2lnbkFjdGlvbihcclxuICBidXNpbmVzc1R5cGU6IHN0cmluZyxcclxuICBwbGF0Zm9ybTogc3RyaW5nLFxyXG4gIHZpc3VhbFN0eWxlOiBzdHJpbmcsXHJcbiAgaW1hZ2VUZXh0OiBzdHJpbmcsXHJcbiAgYnJhbmRQcm9maWxlOiBCcmFuZFByb2ZpbGUsXHJcbiAgYnJhbmRDb25zaXN0ZW5jeT86IHtcclxuICAgIHN0cmljdENvbnNpc3RlbmN5OiBib29sZWFuO1xyXG4gICAgZm9sbG93QnJhbmRDb2xvcnM6IGJvb2xlYW47XHJcbiAgfSxcclxuICBhcnRpZmFjdEluc3RydWN0aW9ucz86IHN0cmluZ1xyXG4pOiBQcm9taXNlPFBvc3RWYXJpYW50PiB7XHJcbiAgdHJ5IHtcclxuICAgIGlmICghYnJhbmRQcm9maWxlKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignQnJhbmQgcHJvZmlsZSBpcyByZXF1aXJlZCBmb3IgR2VtaW5pIEhEIGRlc2lnbiBnZW5lcmF0aW9uJyk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coJ/CfjqggR2VtaW5pIEhEIERlc2lnbiBHZW5lcmF0aW9uIFN0YXJ0ZWQnKTtcclxuICAgIGNvbnNvbGUubG9nKCctIEJ1c2luZXNzIFR5cGU6JywgYnVzaW5lc3NUeXBlKTtcclxuICAgIGNvbnNvbGUubG9nKCctIFBsYXRmb3JtOicsIHBsYXRmb3JtKTtcclxuICAgIGNvbnNvbGUubG9nKCctIFZpc3VhbCBTdHlsZTonLCB2aXN1YWxTdHlsZSk7XHJcbiAgICBjb25zb2xlLmxvZygnLSBJbWFnZSBUZXh0OicsIGltYWdlVGV4dCk7XHJcbiAgICBjb25zb2xlLmxvZygnLSBCcmFuZCBQcm9maWxlOicsIGJyYW5kUHJvZmlsZS5idXNpbmVzc05hbWUpO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKCfwn5qAIFVzaW5nIEdlbWluaSAyLjAgRmxhc2ggSEQgZm9yIGVuaGFuY2VkIGRlc2lnbiBnZW5lcmF0aW9uLi4uJyk7XHJcbiAgICBjb25zdCB7IGdlbmVyYXRlR2VtaW5pSERFbmhhbmNlZERlc2lnbldpdGhGYWxsYmFjayB9ID0gYXdhaXQgaW1wb3J0KCdAL2FpL2dlbWluaS1oZC1lbmhhbmNlZC1kZXNpZ24nKTtcclxuXHJcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBnZW5lcmF0ZUdlbWluaUhERW5oYW5jZWREZXNpZ25XaXRoRmFsbGJhY2soe1xyXG4gICAgICBidXNpbmVzc1R5cGUsXHJcbiAgICAgIHBsYXRmb3JtLFxyXG4gICAgICB2aXN1YWxTdHlsZSxcclxuICAgICAgaW1hZ2VUZXh0LFxyXG4gICAgICBicmFuZFByb2ZpbGUsXHJcbiAgICAgIGJyYW5kQ29uc2lzdGVuY3ksXHJcbiAgICAgIGFydGlmYWN0SW5zdHJ1Y3Rpb25zLFxyXG4gICAgfSk7XHJcblxyXG4gICAgY29uc29sZS5sb2coJ+KchSBHZW1pbmkgSEQgZW5oYW5jZWQgZGVzaWduIGdlbmVyYXRlZCBzdWNjZXNzZnVsbHknKTtcclxuICAgIGNvbnNvbGUubG9nKCfwn5SXIEltYWdlIFVSTDonLCByZXN1bHQuaW1hZ2VVcmwpO1xyXG4gICAgY29uc29sZS5sb2coJ+KtkCBRdWFsaXR5IFNjb3JlOicsIHJlc3VsdC5xdWFsaXR5U2NvcmUpO1xyXG4gICAgY29uc29sZS5sb2coJ/Cfjq8gRW5oYW5jZW1lbnRzIEFwcGxpZWQ6JywgcmVzdWx0LmVuaGFuY2VtZW50c0FwcGxpZWQpO1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIHBsYXRmb3JtLFxyXG4gICAgICBpbWFnZVVybDogcmVzdWx0LmltYWdlVXJsLFxyXG4gICAgICBjYXB0aW9uOiBpbWFnZVRleHQsXHJcbiAgICAgIGhhc2h0YWdzOiBbXSxcclxuICAgIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBpbiBHZW1pbmkgSEQgZGVzaWduIGdlbmVyYXRpb246JywgZXJyb3IpO1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKGBHZW1pbmkgSEQgZGVzaWduIGdlbmVyYXRpb24gZmFpbGVkOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWApO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEdlbmVyYXRlIGNvbnRlbnQgd2l0aCBhcnRpZmFjdCByZWZlcmVuY2VzIChFbmhhbmNlZClcclxuICovXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZW5lcmF0ZUNvbnRlbnRXaXRoQXJ0aWZhY3RzQWN0aW9uKFxyXG4gIHByb2ZpbGU6IEJyYW5kUHJvZmlsZSxcclxuICBwbGF0Zm9ybTogUGxhdGZvcm0sXHJcbiAgYnJhbmRDb25zaXN0ZW5jeT86IHsgc3RyaWN0Q29uc2lzdGVuY3k6IGJvb2xlYW47IGZvbGxvd0JyYW5kQ29sb3JzOiBib29sZWFuIH0sXHJcbiAgYXJ0aWZhY3RJZHM6IHN0cmluZ1tdID0gW10sXHJcbiAgdXNlRW5oYW5jZWREZXNpZ246IGJvb2xlYW4gPSB0cnVlLFxyXG4gIGluY2x1ZGVQZW9wbGVJbkRlc2lnbnM6IGJvb2xlYW4gPSB0cnVlLFxyXG4gIHVzZUxvY2FsTGFuZ3VhZ2U6IGJvb2xlYW4gPSBmYWxzZVxyXG4pOiBQcm9taXNlPEdlbmVyYXRlZFBvc3Q+IHtcclxuICB0cnkge1xyXG4gICAgY29uc29sZS5sb2coJ/CfjqggR2VuZXJhdGluZyBjb250ZW50IHdpdGggYXJ0aWZhY3RzLi4uJyk7XHJcbiAgICBjb25zb2xlLmxvZygnLSBQbGF0Zm9ybTonLCBwbGF0Zm9ybSk7XHJcbiAgICBjb25zb2xlLmxvZygnLSBBcnRpZmFjdHM6JywgYXJ0aWZhY3RJZHMubGVuZ3RoKTtcclxuICAgIGNvbnNvbGUubG9nKCctIEVuaGFuY2VkIERlc2lnbjonLCB1c2VFbmhhbmNlZERlc2lnbik7XHJcblxyXG4gICAgLy8gR2V0IGFjdGl2ZSBhcnRpZmFjdHMgaWYgbm8gc3BlY2lmaWMgYXJ0aWZhY3RzIHByb3ZpZGVkXHJcbiAgICBsZXQgdGFyZ2V0QXJ0aWZhY3RzOiBBcnRpZmFjdFtdID0gW107XHJcblxyXG4gICAgaWYgKGFydGlmYWN0SWRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgLy8gVXNlIHNwZWNpZmllZCBhcnRpZmFjdHNcclxuICAgICAgZm9yIChjb25zdCBhcnRpZmFjdElkIG9mIGFydGlmYWN0SWRzKSB7XHJcbiAgICAgICAgY29uc3QgYXJ0aWZhY3QgPSBhcnRpZmFjdHNTZXJ2aWNlLmdldEFydGlmYWN0KGFydGlmYWN0SWQpO1xyXG4gICAgICAgIGlmIChhcnRpZmFjdCkge1xyXG4gICAgICAgICAgdGFyZ2V0QXJ0aWZhY3RzLnB1c2goYXJ0aWZhY3QpO1xyXG4gICAgICAgICAgYXdhaXQgYXJ0aWZhY3RzU2VydmljZS50cmFja1VzYWdlKGFydGlmYWN0SWQsICdxdWljay1jb250ZW50Jyk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBVc2UgYWN0aXZlIGFydGlmYWN0cywgcHJpb3JpdGl6aW5nIGV4YWN0LXVzZVxyXG4gICAgICBjb25zdCBhY3RpdmVBcnRpZmFjdHMgPSBhcnRpZmFjdHNTZXJ2aWNlLmdldEFjdGl2ZUFydGlmYWN0cygpO1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBBY3RpdmUgYXJ0aWZhY3RzIGZvdW5kOicsIGFjdGl2ZUFydGlmYWN0cy5sZW5ndGgpO1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+TiyBBY3RpdmUgYXJ0aWZhY3RzIGRldGFpbHM6JywgYWN0aXZlQXJ0aWZhY3RzLm1hcChhID0+ICh7XHJcbiAgICAgICAgaWQ6IGEuaWQsXHJcbiAgICAgICAgbmFtZTogYS5uYW1lLFxyXG4gICAgICAgIHR5cGU6IGEudHlwZSxcclxuICAgICAgICB1c2FnZVR5cGU6IGEudXNhZ2VUeXBlLFxyXG4gICAgICAgIGlzQWN0aXZlOiBhLmlzQWN0aXZlLFxyXG4gICAgICAgIGluc3RydWN0aW9uczogYS5pbnN0cnVjdGlvbnNcclxuICAgICAgfSkpKTtcclxuXHJcbiAgICAgIGNvbnN0IGV4YWN0VXNlQXJ0aWZhY3RzID0gYWN0aXZlQXJ0aWZhY3RzLmZpbHRlcihhID0+IGEudXNhZ2VUeXBlID09PSAnZXhhY3QtdXNlJyk7XHJcbiAgICAgIGNvbnN0IHJlZmVyZW5jZUFydGlmYWN0cyA9IGFjdGl2ZUFydGlmYWN0cy5maWx0ZXIoYSA9PiBhLnVzYWdlVHlwZSA9PT0gJ3JlZmVyZW5jZScpO1xyXG5cclxuICAgICAgLy8gUHJpb3JpdGl6ZSBleGFjdC11c2UgYXJ0aWZhY3RzXHJcbiAgICAgIHRhcmdldEFydGlmYWN0cyA9IFsuLi5leGFjdFVzZUFydGlmYWN0cywgLi4ucmVmZXJlbmNlQXJ0aWZhY3RzLnNsaWNlKDAsIDMpXTtcclxuXHJcbiAgICAgIC8vIFRyYWNrIHVzYWdlIGZvciBhY3RpdmUgYXJ0aWZhY3RzXHJcbiAgICAgIGZvciAoY29uc3QgYXJ0aWZhY3Qgb2YgdGFyZ2V0QXJ0aWZhY3RzKSB7XHJcbiAgICAgICAgYXdhaXQgYXJ0aWZhY3RzU2VydmljZS50cmFja1VzYWdlKGFydGlmYWN0LmlkLCAncXVpY2stY29udGVudCcpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coJ/Cfk44gVXNpbmcgYXJ0aWZhY3RzOicsIHRhcmdldEFydGlmYWN0cy5tYXAoYSA9PiBgJHthLm5hbWV9ICgke2EudXNhZ2VUeXBlfSlgKSk7XHJcblxyXG4gICAgLy8gR2VuZXJhdGUgYmFzZSBjb250ZW50IGZpcnN0XHJcbiAgICBjb25zdCBiYXNlUG9zdCA9IGF3YWl0IGdlbmVyYXRlQ29udGVudEFjdGlvbihwcm9maWxlLCBwbGF0Zm9ybSwgYnJhbmRDb25zaXN0ZW5jeSk7XHJcblxyXG4gICAgLy8gSWYgZW5oYW5jZWQgZGVzaWduIGlzIGRpc2FibGVkLCByZXR1cm4gYmFzZSBjb250ZW50XHJcbiAgICBpZiAoIXVzZUVuaGFuY2VkRGVzaWduKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIEVuaGFuY2VkIGRlc2lnbiBkaXNhYmxlZCwgdXNpbmcgYmFzZSBjb250ZW50IGdlbmVyYXRpb24nKTtcclxuICAgICAgcmV0dXJuIGJhc2VQb3N0O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEVuaGFuY2VkIGRlc2lnbiBpcyBlbmFibGVkIC0gYWx3YXlzIHVzZSBlbmhhbmNlZCBnZW5lcmF0aW9uIHJlZ2FyZGxlc3Mgb2YgYXJ0aWZhY3RzXHJcbiAgICBjb25zb2xlLmxvZygn8J+OqCBFbmhhbmNlZCBkZXNpZ24gZW5hYmxlZCAtIHByb2NlZWRpbmcgd2l0aCBlbmhhbmNlZCBnZW5lcmF0aW9uJyk7XHJcbiAgICBjb25zb2xlLmxvZyhg8J+TiiBBcnRpZmFjdHMgYXZhaWxhYmxlOiAke3RhcmdldEFydGlmYWN0cy5sZW5ndGh9YCk7XHJcblxyXG4gICAgaWYgKHRhcmdldEFydGlmYWN0cy5sZW5ndGggPT09IDApIHtcclxuICAgICAgY29uc29sZS5sb2coJ+KcqCBObyBhcnRpZmFjdHMgcHJvdmlkZWQgLSB1c2luZyBlbmhhbmNlZCBkZXNpZ24gd2l0aG91dCBhcnRpZmFjdCBjb250ZXh0Jyk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+OryBVc2luZyBlbmhhbmNlZCBkZXNpZ24gd2l0aCBhcnRpZmFjdCBjb250ZXh0Jyk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gU2VwYXJhdGUgZXhhY3QtdXNlIGFuZCByZWZlcmVuY2UgYXJ0aWZhY3RzXHJcbiAgICBjb25zdCBleGFjdFVzZUFydGlmYWN0cyA9IHRhcmdldEFydGlmYWN0cy5maWx0ZXIoYSA9PiBhLnVzYWdlVHlwZSA9PT0gJ2V4YWN0LXVzZScpO1xyXG4gICAgY29uc3QgcmVmZXJlbmNlQXJ0aWZhY3RzID0gdGFyZ2V0QXJ0aWZhY3RzLmZpbHRlcihhID0+IGEudXNhZ2VUeXBlID09PSAncmVmZXJlbmNlJyk7XHJcblxyXG4gICAgLy8gQ3JlYXRlIGVuaGFuY2VkIGltYWdlIHRleHQgc3RydWN0dXJlIGZyb20gcG9zdCBjb21wb25lbnRzXHJcbiAgICBsZXQgZW5oYW5jZWRJbWFnZVRleHQ6IHsgY2F0Y2h5V29yZHM6IHN0cmluZzsgc3ViaGVhZGxpbmU/OiBzdHJpbmc7IGNhbGxUb0FjdGlvbj86IHN0cmluZyB9ID0ge1xyXG4gICAgICBjYXRjaHlXb3JkczogYmFzZVBvc3QuY2F0Y2h5V29yZHMgfHwgJ0VuZ2FnaW5nIENvbnRlbnQnLFxyXG4gICAgICBzdWJoZWFkbGluZTogYmFzZVBvc3Quc3ViaGVhZGxpbmUsXHJcbiAgICAgIGNhbGxUb0FjdGlvbjogYmFzZVBvc3QuY2FsbFRvQWN0aW9uXHJcbiAgICB9O1xyXG4gICAgbGV0IGVuaGFuY2VkQ29udGVudCA9IGJhc2VQb3N0LmNvbnRlbnQ7XHJcblxyXG4gICAgLy8gQ29sbGVjdCB1c2FnZSBpbnN0cnVjdGlvbnMgZnJvbSBhcnRpZmFjdHNcclxuICAgIGNvbnN0IGFydGlmYWN0SW5zdHJ1Y3Rpb25zID0gdGFyZ2V0QXJ0aWZhY3RzXHJcbiAgICAgIC5maWx0ZXIoYSA9PiBhLmluc3RydWN0aW9ucyAmJiBhLmluc3RydWN0aW9ucy50cmltKCkpXHJcbiAgICAgIC5tYXAoYSA9PiBgLSAke2EubmFtZX06ICR7YS5pbnN0cnVjdGlvbnN9YClcclxuICAgICAgLmpvaW4oJ1xcbicpO1xyXG5cclxuICAgIC8vIENvbGxlY3QgdGV4dCBvdmVybGF5IGluc3RydWN0aW9ucyBmcm9tIHRleHQgYXJ0aWZhY3RzXHJcbiAgICBjb25zdCB0ZXh0T3ZlcmxheUluc3RydWN0aW9ucyA9IGV4YWN0VXNlQXJ0aWZhY3RzXHJcbiAgICAgIC5maWx0ZXIoYSA9PiBhLnRleHRPdmVybGF5Py5pbnN0cnVjdGlvbnMgJiYgYS50ZXh0T3ZlcmxheS5pbnN0cnVjdGlvbnMudHJpbSgpKVxyXG4gICAgICAubWFwKGEgPT4gYC0gJHthLm5hbWV9OiAke2EudGV4dE92ZXJsYXkuaW5zdHJ1Y3Rpb25zfWApXHJcbiAgICAgIC5qb2luKCdcXG4nKTtcclxuXHJcbiAgICAvLyBQcm9jZXNzIGV4YWN0LXVzZSBhcnRpZmFjdHMgZmlyc3QgKGhpZ2hlciBwcmlvcml0eSlcclxuICAgIGlmIChleGFjdFVzZUFydGlmYWN0cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgIGNvbnN0IHByaW1hcnlFeGFjdFVzZSA9IGV4YWN0VXNlQXJ0aWZhY3RzWzBdO1xyXG5cclxuICAgICAgLy8gVXNlIHRleHQgb3ZlcmxheSBpZiBhdmFpbGFibGVcclxuICAgICAgaWYgKHByaW1hcnlFeGFjdFVzZS50ZXh0T3ZlcmxheSkge1xyXG4gICAgICAgIGlmIChwcmltYXJ5RXhhY3RVc2UudGV4dE92ZXJsYXkuaGVhZGxpbmUpIHtcclxuICAgICAgICAgIGVuaGFuY2VkSW1hZ2VUZXh0LmNhdGNoeVdvcmRzID0gcHJpbWFyeUV4YWN0VXNlLnRleHRPdmVybGF5LmhlYWRsaW5lO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfk50gVXNpbmcgaGVhZGxpbmUgZnJvbSBleGFjdC11c2UgYXJ0aWZhY3Q6JywgZW5oYW5jZWRJbWFnZVRleHQuY2F0Y2h5V29yZHMpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKHByaW1hcnlFeGFjdFVzZS50ZXh0T3ZlcmxheS5tZXNzYWdlKSB7XHJcbiAgICAgICAgICBlbmhhbmNlZENvbnRlbnQgPSBwcmltYXJ5RXhhY3RVc2UudGV4dE92ZXJsYXkubWVzc2FnZTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OdIFVzaW5nIG1lc3NhZ2UgZnJvbSBleGFjdC11c2UgYXJ0aWZhY3QnKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIFVzZSBDVEEgZnJvbSBhcnRpZmFjdCBpZiBhdmFpbGFibGVcclxuICAgICAgICBpZiAocHJpbWFyeUV4YWN0VXNlLnRleHRPdmVybGF5LmN0YSkge1xyXG4gICAgICAgICAgZW5oYW5jZWRJbWFnZVRleHQuY2FsbFRvQWN0aW9uID0gcHJpbWFyeUV4YWN0VXNlLnRleHRPdmVybGF5LmN0YTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OdIFVzaW5nIENUQSBmcm9tIGV4YWN0LXVzZSBhcnRpZmFjdDonLCBlbmhhbmNlZEltYWdlVGV4dC5jYWxsVG9BY3Rpb24pO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIFByb2Nlc3MgcmVmZXJlbmNlIGFydGlmYWN0cyBmb3Igc3R5bGUgZ3VpZGFuY2VcclxuICAgIGNvbnN0IGFjdGl2ZURpcmVjdGl2ZXMgPSByZWZlcmVuY2VBcnRpZmFjdHMuZmxhdE1hcChhcnRpZmFjdCA9PlxyXG4gICAgICBhcnRpZmFjdC5kaXJlY3RpdmVzLmZpbHRlcihkaXJlY3RpdmUgPT4gZGlyZWN0aXZlLmFjdGl2ZSlcclxuICAgICk7XHJcblxyXG4gICAgLy8gQXBwbHkgc3R5bGUgcmVmZXJlbmNlIGRpcmVjdGl2ZXNcclxuICAgIGNvbnN0IHN0eWxlRGlyZWN0aXZlcyA9IGFjdGl2ZURpcmVjdGl2ZXMuZmlsdGVyKGQgPT4gZC50eXBlID09PSAnc3R5bGUtcmVmZXJlbmNlJyk7XHJcbiAgICBsZXQgdmlzdWFsU3R5bGVPdmVycmlkZSA9IHByb2ZpbGUudmlzdWFsU3R5bGUgfHwgJ21vZGVybic7XHJcbiAgICBpZiAoc3R5bGVEaXJlY3RpdmVzLmxlbmd0aCA+IDApIHtcclxuICAgICAgY29uc29sZS5sb2coJ/CfjqggQXBwbHlpbmcgc3R5bGUgcmVmZXJlbmNlcyBmcm9tIGFydGlmYWN0cycpO1xyXG4gICAgICBjb25zdCBwcmltYXJ5U3R5bGVEaXJlY3RpdmUgPSBzdHlsZURpcmVjdGl2ZXMuZmluZChkID0+IGQucHJpb3JpdHkgPj0gNyk7XHJcbiAgICAgIGlmIChwcmltYXJ5U3R5bGVEaXJlY3RpdmUpIHtcclxuICAgICAgICB2aXN1YWxTdHlsZU92ZXJyaWRlID0gJ2FydGlmYWN0LWluc3BpcmVkJztcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+OqCBVc2luZyBhcnRpZmFjdC1pbnNwaXJlZCB2aXN1YWwgc3R5bGUnKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIENvbWJpbmUgYWxsIGluc3RydWN0aW9uc1xyXG4gICAgY29uc3QgYWxsSW5zdHJ1Y3Rpb25zID0gW2FydGlmYWN0SW5zdHJ1Y3Rpb25zLCB0ZXh0T3ZlcmxheUluc3RydWN0aW9uc11cclxuICAgICAgLmZpbHRlcihCb29sZWFuKVxyXG4gICAgICAuam9pbignXFxuJyk7XHJcblxyXG4gICAgLy8gR2VuZXJhdGUgZW5oYW5jZWQgZGVzaWduIHdpdGggYXJ0aWZhY3QgY29udGV4dFxyXG4gICAgY29uc3QgZW5oYW5jZWRSZXN1bHQgPSBhd2FpdCBnZW5lcmF0ZUVuaGFuY2VkRGVzaWduQWN0aW9uKFxyXG4gICAgICBwcm9maWxlLmJ1c2luZXNzVHlwZSB8fCAnYnVzaW5lc3MnLFxyXG4gICAgICBwbGF0Zm9ybS50b0xvd2VyQ2FzZSgpLFxyXG4gICAgICB2aXN1YWxTdHlsZU92ZXJyaWRlLFxyXG4gICAgICBlbmhhbmNlZEltYWdlVGV4dCxcclxuICAgICAgcHJvZmlsZSxcclxuICAgICAgdHJ1ZSxcclxuICAgICAgYnJhbmRDb25zaXN0ZW5jeSxcclxuICAgICAgYWxsSW5zdHJ1Y3Rpb25zIHx8IHVuZGVmaW5lZCxcclxuICAgICAgaW5jbHVkZVBlb3BsZUluRGVzaWducyxcclxuICAgICAgdXNlTG9jYWxMYW5ndWFnZVxyXG4gICAgKTtcclxuXHJcbiAgICAvLyBDcmVhdGUgZW5oYW5jZWQgcG9zdCB3aXRoIGFydGlmYWN0IG1ldGFkYXRhXHJcbiAgICBjb25zdCBlbmhhbmNlZFBvc3Q6IEdlbmVyYXRlZFBvc3QgPSB7XHJcbiAgICAgIC4uLmJhc2VQb3N0LFxyXG4gICAgICBpZDogRGF0ZS5ub3coKS50b1N0cmluZygpLFxyXG4gICAgICB2YXJpYW50czogW3tcclxuICAgICAgICBwbGF0Zm9ybTogcGxhdGZvcm0sXHJcbiAgICAgICAgaW1hZ2VVcmw6IGVuaGFuY2VkUmVzdWx0LmltYWdlVXJsXHJcbiAgICAgIH1dLFxyXG4gICAgICBjb250ZW50OiB0YXJnZXRBcnRpZmFjdHMubGVuZ3RoID4gMFxyXG4gICAgICAgID8gYCR7ZW5oYW5jZWRDb250ZW50fVxcblxcbuKcqCBFbmhhbmNlZCB3aXRoIEFJKyB1c2luZyAke3RhcmdldEFydGlmYWN0cy5sZW5ndGh9IHJlZmVyZW5jZSR7dGFyZ2V0QXJ0aWZhY3RzLmxlbmd0aCAhPT0gMSA/ICdzJyA6ICcnfSAoUXVhbGl0eTogJHtlbmhhbmNlZFJlc3VsdC5xdWFsaXR5U2NvcmV9LzEwKWBcclxuICAgICAgICA6IGAke2VuaGFuY2VkQ29udGVudH1cXG5cXG7inKggRW5oYW5jZWQgd2l0aCBBSSsgRGVzaWduIEdlbmVyYXRpb24gKFF1YWxpdHk6ICR7ZW5oYW5jZWRSZXN1bHQucXVhbGl0eVNjb3JlfS8xMClgLFxyXG4gICAgICBkYXRlOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIC8vIEFkZCBhcnRpZmFjdCBtZXRhZGF0YVxyXG4gICAgICBtZXRhZGF0YToge1xyXG4gICAgICAgIC4uLmJhc2VQb3N0Lm1ldGFkYXRhLFxyXG4gICAgICAgIHJlZmVyZW5jZWRBcnRpZmFjdHM6IHRhcmdldEFydGlmYWN0cy5tYXAoYSA9PiAoe1xyXG4gICAgICAgICAgaWQ6IGEuaWQsXHJcbiAgICAgICAgICBuYW1lOiBhLm5hbWUsXHJcbiAgICAgICAgICB0eXBlOiBhLnR5cGUsXHJcbiAgICAgICAgICBjYXRlZ29yeTogYS5jYXRlZ29yeVxyXG4gICAgICAgIH0pKSxcclxuICAgICAgICBhY3RpdmVEaXJlY3RpdmVzOiBhY3RpdmVEaXJlY3RpdmVzLm1hcChkID0+ICh7XHJcbiAgICAgICAgICBpZDogZC5pZCxcclxuICAgICAgICAgIHR5cGU6IGQudHlwZSxcclxuICAgICAgICAgIGxhYmVsOiBkLmxhYmVsLFxyXG4gICAgICAgICAgcHJpb3JpdHk6IGQucHJpb3JpdHlcclxuICAgICAgICB9KSlcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBjb25zb2xlLmxvZygn4pyFIEVuaGFuY2VkIGNvbnRlbnQgd2l0aCBhcnRpZmFjdHMgZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseScpO1xyXG4gICAgcmV0dXJuIGVuaGFuY2VkUG9zdDtcclxuXHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZW5lcmF0aW5nIGNvbnRlbnQgd2l0aCBhcnRpZmFjdHM6XCIsIGVycm9yKTtcclxuICAgIHRocm93IG5ldyBFcnJvcigoZXJyb3IgYXMgRXJyb3IpLm1lc3NhZ2UpO1xyXG4gIH1cclxufVxyXG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjhTQXFkc0IifQ==
}}),
"[project]/src/app/actions.ts [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "analyzeBrandAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$8fd537__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["analyzeBrandAction"]),
    "generateContentAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$7738c2__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateContentAction"]),
    "generateContentWithArtifactsAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$052bf2__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateContentWithArtifactsAction"]),
    "generateCreativeAssetAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$c92492__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateCreativeAssetAction"]),
    "generateEnhancedDesignAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$2fe01c__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateEnhancedDesignAction"]),
    "generateGeminiHDDesignAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$624089__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateGeminiHDDesignAction"]),
    "generateVideoContentAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$3ba49a__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateVideoContentAction"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$8fd537__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:8fd537 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$7738c2__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:7738c2 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$3ba49a__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:3ba49a [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$c92492__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:c92492 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$2fe01c__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:2fe01c [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$624089__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:624089 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$052bf2__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:052bf2 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/app/actions.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "analyzeBrandAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["analyzeBrandAction"]),
    "generateContentAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateContentAction"]),
    "generateContentWithArtifactsAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateContentWithArtifactsAction"]),
    "generateCreativeAssetAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateCreativeAssetAction"]),
    "generateEnhancedDesignAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateEnhancedDesignAction"]),
    "generateGeminiHDDesignAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateGeminiHDDesignAction"]),
    "generateVideoContentAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateVideoContentAction"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-ssr] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=src_app_b6caa8fe._.js.map