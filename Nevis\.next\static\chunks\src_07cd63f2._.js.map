{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/auth/VerifyEmail.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useRef, useState, useEffect } from 'react';\r\n\r\ntype VerifyEmailProps = {\r\n  email: string;\r\n  onSuccess: () => void;\r\n  onClose?: () => void;\r\n  type?: 'signup' | 'reset' | string;\r\n};\r\n\r\nexport default function VerifyEmail({ email, onSuccess, onClose, type = 'signup' }: VerifyEmailProps) {\r\n  const [code, setCode] = useState<string[]>(['', '', '', '', '']);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [resendLoading, setResendLoading] = useState(false);\r\n  const [countdown, setCountdown] = useState(0);\r\n  const modalRef = useRef<HTMLDivElement | null>(null);\r\n\r\n  useEffect(() => {\r\n    // Focus first input on open\r\n    const first = document.querySelector<HTMLInputElement>('input[name=\"code-0\"]');\r\n    first?.focus();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (countdown > 0) {\r\n      const t = setTimeout(() => setCountdown(countdown - 1), 1000);\r\n      return () => clearTimeout(t);\r\n    }\r\n  }, [countdown]);\r\n\r\n  const handleChange = (index: number, value: string) => {\r\n    if (!/^\\d*$/.test(value)) return;\r\n    const newCode = [...code];\r\n    newCode[index] = value.slice(0, 1);\r\n    setCode(newCode);\r\n\r\n    if (value && index < 4) {\r\n      const next = document.querySelector<HTMLInputElement>(`input[name=\"code-${index + 1}\"]`);\r\n      next?.focus();\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {\r\n    if (e.key === 'Backspace' && !code[index] && index > 0) {\r\n      const prev = document.querySelector<HTMLInputElement>(`input[name=\"code-${index - 1}\"]`);\r\n      if (prev) {\r\n        prev.focus();\r\n        const updated = [...code];\r\n        updated[index - 1] = '';\r\n        setCode(updated);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {\r\n    e.preventDefault();\r\n    const text = e.clipboardData.getData('Text').trim();\r\n    if (/^\\d{5}$/.test(text)) {\r\n      setCode(text.split(''));\r\n      const last = document.querySelector<HTMLInputElement>('input[name=\"code-4\"]');\r\n      last?.focus();\r\n    }\r\n  };\r\n\r\n  const handleVerify = async (e?: React.FormEvent) => {\r\n    e?.preventDefault();\r\n    const verificationCode = code.join('');\r\n    if (verificationCode.length !== 5) {\r\n      alert('Enter 5-digit code');\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      const res = await fetch('/api/auth/verify-code', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ email, code: verificationCode, type }),\r\n      });\r\n      const body = await res.json();\r\n      if (res.ok && body.ok) {\r\n        onSuccess();\r\n        onClose?.();\r\n      } else {\r\n        alert(body?.error || 'Invalid code');\r\n        setCode(['', '', '', '', '']);\r\n      }\r\n    } catch (err) {\r\n      console.error('Verification error', err);\r\n      alert('Verification failed');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleResend = async () => {\r\n    if (resendLoading || countdown > 0) return;\r\n    setResendLoading(true);\r\n    try {\r\n      const res = await fetch('/api/auth/resend-code', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ email, type }),\r\n      });\r\n      const body = await res.json();\r\n      if (res.ok && body.ok) {\r\n        setCode(['', '', '', '', '']);\r\n        setCountdown(30);\r\n      } else {\r\n        alert(body?.error || 'Failed to resend code');\r\n      }\r\n    } catch (err) {\r\n      console.error('Resend error', err);\r\n      alert('Failed to resend code');\r\n    } finally {\r\n      setResendLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={modalRef}\r\n      onClick={(e) => {\r\n        // Fix: only close when clicking the backdrop itself (target equals the element with the handler)\r\n        // use currentTarget for a robust comparison\r\n        if (e.target === e.currentTarget) {\r\n          onClose?.();\r\n        }\r\n      }}\r\n      className=\"fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex justify-center items-center z-50 p-4\"\r\n    >\r\n      <div className=\"bg-gradient-to-br from-gray-900 to-black rounded-2xl p-8 max-w-md w-full relative animate-fadeIn border border-gray-700 shadow-2xl\">\r\n        <button\r\n          type=\"button\"\r\n          onClick={() => onClose?.()}\r\n          className=\"absolute top-4 right-4 text-gray-400 hover:text-white transition\"\r\n          aria-label=\"Close modal\"\r\n        >\r\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" className=\"text-xl\">\r\n            <path d=\"M6 6L18 18M6 18L18 6\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n          </svg>\r\n        </button>\r\n\r\n        <div className=\"text-center space-y-6\">\r\n          <h2 className=\"text-2xl font-bold text-white\">\r\n            {type === 'reset' ? 'Verify Reset Code' : 'Verify Email'}\r\n          </h2>\r\n\r\n          <div className=\"space-y-2\">\r\n            <p className=\"text-gray-300\">Please enter the verification code sent to</p>\r\n            <p className=\"font-medium text-yellow-400 break-all\">{email}</p>\r\n          </div>\r\n\r\n          <form onSubmit={handleVerify} className=\"space-y-6\">\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex justify-center gap-2\">\r\n                {code.map((digit, index) => (\r\n                  <input\r\n                    key={index}\r\n                    name={`code-${index}`}\r\n                    type=\"text\"\r\n                    inputMode=\"numeric\"\r\n                    maxLength={1}\r\n                    className=\"w-12 h-12 bg-gray-800 text-white text-center text-xl rounded-lg border border-gray-700 focus:outline-none focus:ring-2 focus:ring-yellow-400\"\r\n                    value={digit}\r\n                    onChange={(e) => handleChange(index, e.target.value)}\r\n                    onKeyDown={(e) => handleKeyDown(e, index)}\r\n                    onPaste={index === 0 ? handlePaste : undefined}\r\n                    disabled={isLoading}\r\n                  />\r\n                ))}\r\n              </div>\r\n              <p className=\"text-gray-400 text-sm\">Haven't received the code? Check your spam folder</p>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col sm:flex-row gap-4\">\r\n              <button\r\n                type=\"submit\"\r\n                disabled={isLoading || code.join('').length !== 5}\r\n                className=\"flex-1 bg-yellow-500 text-black font-semibold px-6 py-3 rounded-lg hover:bg-yellow-400 active:bg-yellow-600 transition-all transform active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg\"\r\n              >\r\n                {isLoading ? (\r\n                  <div className=\"flex items-center justify-center gap-2\">\r\n                    <svg className=\"animate-spin h-5 w-5\" viewBox=\"0 0 24 24\">\r\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" fill=\"none\" />\r\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\r\n                    </svg>\r\n                    Verifying...\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"flex items-center justify-center gap-2\">\r\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                      <path d=\"M20 6L9 17l-5-5\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n                    </svg>\r\n                    Verify Code\r\n                  </div>\r\n                )}\r\n              </button>\r\n\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleResend}\r\n                disabled={resendLoading || countdown > 0}\r\n                className=\"text-yellow-400 hover:text-yellow-300 font-medium disabled:opacity-50 disabled:cursor-not-allowed transition\"\r\n              >\r\n                {resendLoading ? (\r\n                  <div className=\"flex items-center justify-center gap-2\">\r\n                    <svg className=\"animate-spin h-4 w-4\" viewBox=\"0 0 24 24\">\r\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" fill=\"none\" />\r\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\r\n                    </svg>\r\n                    Sending...\r\n                  </div>\r\n                ) : countdown > 0 ? (\r\n                  `Resend in ${countdown}s`\r\n                ) : (\r\n                  'Resend Code'\r\n                )}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAWe,SAAS,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,QAAQ,EAAoB;;IAClG,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAI;QAAI;QAAI;QAAI;KAAG;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,4BAA4B;YAC5B,MAAM,QAAQ,SAAS,aAAa,CAAmB;YACvD,OAAO;QACT;gCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,YAAY,GAAG;gBACjB,MAAM,IAAI;+CAAW,IAAM,aAAa,YAAY;8CAAI;gBACxD;6CAAO,IAAM,aAAa;;YAC5B;QACF;gCAAG;QAAC;KAAU;IAEd,MAAM,eAAe,CAAC,OAAe;QACnC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ;QAC1B,MAAM,UAAU;eAAI;SAAK;QACzB,OAAO,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;QAChC,QAAQ;QAER,IAAI,SAAS,QAAQ,GAAG;YACtB,MAAM,OAAO,SAAS,aAAa,CAAmB,CAAC,iBAAiB,EAAE,QAAQ,EAAE,EAAE,CAAC;YACvF,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,CAAC,GAA0C;QAC/D,IAAI,EAAE,GAAG,KAAK,eAAe,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,GAAG;YACtD,MAAM,OAAO,SAAS,aAAa,CAAmB,CAAC,iBAAiB,EAAE,QAAQ,EAAE,EAAE,CAAC;YACvF,IAAI,MAAM;gBACR,KAAK,KAAK;gBACV,MAAM,UAAU;uBAAI;iBAAK;gBACzB,OAAO,CAAC,QAAQ,EAAE,GAAG;gBACrB,QAAQ;YACV;QACF;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAChB,MAAM,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC,QAAQ,IAAI;QACjD,IAAI,UAAU,IAAI,CAAC,OAAO;YACxB,QAAQ,KAAK,KAAK,CAAC;YACnB,MAAM,OAAO,SAAS,aAAa,CAAmB;YACtD,MAAM;QACR;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,GAAG;QACH,MAAM,mBAAmB,KAAK,IAAI,CAAC;QACnC,IAAI,iBAAiB,MAAM,KAAK,GAAG;YACjC,MAAM;YACN;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,yBAAyB;gBAC/C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO,MAAM;oBAAkB;gBAAK;YAC7D;YACA,MAAM,OAAO,MAAM,IAAI,IAAI;YAC3B,IAAI,IAAI,EAAE,IAAI,KAAK,EAAE,EAAE;gBACrB;gBACA;YACF,OAAO;gBACL,MAAM,MAAM,SAAS;gBACrB,QAAQ;oBAAC;oBAAI;oBAAI;oBAAI;oBAAI;iBAAG;YAC9B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,iBAAiB,YAAY,GAAG;QACpC,iBAAiB;QACjB,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,yBAAyB;gBAC/C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAK;YACrC;YACA,MAAM,OAAO,MAAM,IAAI,IAAI;YAC3B,IAAI,IAAI,EAAE,IAAI,KAAK,EAAE,EAAE;gBACrB,QAAQ;oBAAC;oBAAI;oBAAI;oBAAI;oBAAI;iBAAG;gBAC5B,aAAa;YACf,OAAO;gBACL,MAAM,MAAM,SAAS;YACvB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM;QACR,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,SAAS,CAAC;YACR,iGAAiG;YACjG,4CAA4C;YAC5C,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;gBAChC;YACF;QACF;QACA,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,MAAK;oBACL,SAAS,IAAM;oBACf,WAAU;oBACV,cAAW;8BAEX,cAAA,6LAAC;wBAAI,OAAM;wBAAK,QAAO;wBAAK,SAAQ;wBAAY,MAAK;wBAAO,OAAM;wBAA6B,WAAU;kCACvG,cAAA,6LAAC;4BAAK,GAAE;4BAAuB,QAAO;4BAAe,aAAY;4BAAM,eAAc;4BAAQ,gBAAe;;;;;;;;;;;;;;;;8BAIhH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,SAAS,UAAU,sBAAsB;;;;;;sCAG5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;8CAC7B,6LAAC;oCAAE,WAAU;8CAAyC;;;;;;;;;;;;sCAGxD,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,6LAAC;oDAEC,MAAM,CAAC,KAAK,EAAE,OAAO;oDACrB,MAAK;oDACL,WAAU;oDACV,WAAW;oDACX,WAAU;oDACV,OAAO;oDACP,UAAU,CAAC,IAAM,aAAa,OAAO,EAAE,MAAM,CAAC,KAAK;oDACnD,WAAW,CAAC,IAAM,cAAc,GAAG;oDACnC,SAAS,UAAU,IAAI,cAAc;oDACrC,UAAU;mDAVL;;;;;;;;;;sDAcX,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,UAAU,aAAa,KAAK,IAAI,CAAC,IAAI,MAAM,KAAK;4CAChD,WAAU;sDAET,0BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAAuB,SAAQ;;0EAC5C,6LAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;gEAAI,MAAK;;;;;;0EACjG,6LAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;oDAC/C;;;;;;qEAIR,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;wDAAO,OAAM;kEAChE,cAAA,6LAAC;4DAAK,GAAE;4DAAkB,QAAO;4DAAe,aAAY;4DAAI,eAAc;4DAAQ,gBAAe;;;;;;;;;;;oDACjG;;;;;;;;;;;;sDAMZ,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,iBAAiB,YAAY;4CACvC,WAAU;sDAET,8BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAAuB,SAAQ;;0EAC5C,6LAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;gEAAI,MAAK;;;;;;0EACjG,6LAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;oDAC/C;;;;;;uDAGN,YAAY,IACd,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,GAEzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAvNwB;KAAA", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/auth/LoginForm.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Mail, Lock, Eye, EyeOff } from 'lucide-react';\r\n\r\ntype Props = {\r\n  signInData: { email: string; password: string };\r\n  // dispatcher that accepts either a new state or an updater function\r\n  setSignInData: React.Dispatch<React.SetStateAction<{ email: string; password: string }>>;\r\n  showSignInPassword: boolean;\r\n  // allow passing a boolean or an updater function (prev => !prev)\r\n  setShowSignInPassword: React.Dispatch<React.SetStateAction<boolean>>;\r\n  loading: boolean;\r\n  handleSignIn: (e: React.FormEvent) => Promise<void> | void;\r\n};\r\n\r\nexport default function LoginForm({ signInData, setSignInData, showSignInPassword, setShowSignInPassword, loading, handleSignIn }: Props) {\r\n  const router = useRouter();\r\n  return (\r\n    <form onSubmit={handleSignIn} className=\"space-y-4\">\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"signin-email\">Email</Label>\r\n        <div className=\"relative\">\r\n          <Mail className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\r\n          <Input\r\n            id=\"signin-email\"\r\n            type=\"email\"\r\n            placeholder=\"Enter your email\"\r\n            className=\"pl-10\"\r\n            value={signInData.email}\r\n            onChange={(e) => setSignInData((prev: any) => ({ ...prev, email: e.target.value }))}\r\n            required\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"signin-password\">Password</Label>\r\n        <div className=\"relative\">\r\n          <Lock className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\r\n          <Input\r\n            id=\"signin-password\"\r\n            type={showSignInPassword ? 'text' : 'password'}\r\n            placeholder=\"Enter your password\"\r\n            className=\"pl-10\"\r\n            value={signInData.password}\r\n            onChange={(e) => setSignInData((prev: any) => ({ ...prev, password: e.target.value }))}\r\n            required\r\n          />\r\n          <button\r\n            type=\"button\"\r\n            aria-label={showSignInPassword ? 'Hide password' : 'Show password'}\r\n            onClick={() => {\r\n              // guard: some parents may accidentally pass a boolean instead of a setter\r\n              if (typeof setShowSignInPassword === 'function') {\r\n                // allow functional updater when supported\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                (setShowSignInPassword as any)((s: any) => !s);\r\n              }\r\n            }}\r\n            className=\"absolute right-3 top-2 text-gray-500\"\r\n          >\r\n            {showSignInPassword ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\r\n          </button>\r\n        </div>\r\n        <div className=\"flex justify-end mt-1\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => router.push('/auth/forgot-password')}\r\n            className=\"text-sm text-blue-600 hover:underline\"\r\n          >\r\n            Forgot password?\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <Button type=\"submit\" className=\"w-full\" disabled={loading}>\r\n        {loading ? 'Signing in...' : 'Sign In'}\r\n      </Button>\r\n    </form>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;AAoBe,SAAS,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,OAAO,EAAE,YAAY,EAAS;;IACtI,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAe;;;;;;kCAC9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO,WAAW,KAAK;gCACvB,UAAU,CAAC,IAAM,cAAc,CAAC,OAAc,CAAC;4CAAE,GAAG,IAAI;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACjF,QAAQ;;;;;;;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAkB;;;;;;kCACjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAM,qBAAqB,SAAS;gCACpC,aAAY;gCACZ,WAAU;gCACV,OAAO,WAAW,QAAQ;gCAC1B,UAAU,CAAC,IAAM,cAAc,CAAC,OAAc,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACpF,QAAQ;;;;;;0CAEV,6LAAC;gCACC,MAAK;gCACL,cAAY,qBAAqB,kBAAkB;gCACnD,SAAS;oCACP,0EAA0E;oCAC1E,IAAI,OAAO,0BAA0B,YAAY;wCAC/C,0CAA0C;wCAC1C,8DAA8D;wCAC7D,sBAA8B,CAAC,IAAW,CAAC;oCAC9C;gCACF;gCACA,WAAU;0CAET,mCAAqB,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;yDAAe,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG1E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAML,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,WAAU;gBAAS,UAAU;0BAChD,UAAU,kBAAkB;;;;;;;;;;;;AAIrC;GAjEwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 776, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/auth/SignupForm.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Mail, User, Lock, Eye, EyeOff } from 'lucide-react';\r\n\r\ntype Props = {\r\n  signUpData: { name?: string; email: string; password: string; confirmPassword?: string };\r\n  setSignUpData: React.Dispatch<React.SetStateAction<{ name?: string; email: string; password: string; confirmPassword?: string }>>;\r\n  loading: boolean;\r\n  handleSignUp: (e: React.FormEvent) => Promise<void> | void;\r\n};\r\n\r\nexport default function SignupForm({ signUpData, setSignUpData, loading, handleSignUp }: Props) {\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  return (\r\n    <form onSubmit={handleSignUp} className=\"space-y-4\">\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"signup-fullname\">Full name</Label>\r\n        <div className=\"relative\">\r\n          <User className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\r\n          <Input\r\n            id=\"signup-fullname\"\r\n            type=\"text\"\r\n            placeholder=\"Your full name\"\r\n            className=\"pl-10\"\r\n            value={signUpData.name || ''}\r\n            onChange={(e) => setSignUpData((prev: any) => ({ ...prev, name: e.target.value }))}\r\n            required\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"signup-email\">Email</Label>\r\n        <div className=\"relative\">\r\n          <Mail className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\r\n          <Input\r\n            id=\"signup-email\"\r\n            type=\"email\"\r\n            placeholder=\"Enter your email\"\r\n            className=\"pl-10\"\r\n            value={signUpData.email}\r\n            onChange={(e) => setSignUpData((prev: any) => ({ ...prev, email: e.target.value }))}\r\n            required\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"signup-password\">Password</Label>\r\n        <div className=\"relative\">\r\n          <Lock className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\r\n          <Input\r\n            id=\"signup-password\"\r\n            type={showPassword ? 'text' : 'password'}\r\n            placeholder=\"Create a password\"\r\n            className=\"pl-10\"\r\n            value={signUpData.password}\r\n            onChange={(e) => setSignUpData((prev: any) => ({ ...prev, password: e.target.value }))}\r\n            required\r\n          />\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => setShowPassword((s) => !s)}\r\n            aria-label={showPassword ? 'Hide password' : 'Show password'}\r\n            className=\"absolute right-3 top-2 text-gray-500\"\r\n          >\r\n            {showPassword ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"signup-confirm-password\">Confirm Password</Label>\r\n        <div className=\"relative\">\r\n          <Lock className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\r\n          <Input\r\n            id=\"signup-confirm-password\"\r\n            type={showConfirmPassword ? 'text' : 'password'}\r\n            placeholder=\"Repeat password\"\r\n            className=\"pl-10\"\r\n            value={signUpData.confirmPassword || ''}\r\n            onChange={(e) => setSignUpData((prev: any) => ({ ...prev, confirmPassword: e.target.value }))}\r\n            required\r\n          />\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => setShowConfirmPassword((s) => !s)}\r\n            aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}\r\n            className=\"absolute right-3 top-2 text-gray-500\"\r\n          >\r\n            {showConfirmPassword ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\r\n          </button>\r\n        </div>\r\n        {signUpData.confirmPassword && signUpData.confirmPassword !== signUpData.password && (\r\n          <p className=\"text-red-500 text-sm mt-1\">Passwords do not match</p>\r\n        )}\r\n      </div>\r\n\r\n      <Button\r\n        type=\"submit\"\r\n        className=\"w-full\"\r\n        disabled={loading || (signUpData.confirmPassword !== undefined && signUpData.confirmPassword !== signUpData.password)}\r\n      >\r\n        {loading ? 'Sending code...' : 'Create account'}\r\n      </Button>\r\n    </form>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAee,SAAS,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,YAAY,EAAS;;IAC5F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAkB;;;;;;kCACjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO,WAAW,IAAI,IAAI;gCAC1B,UAAU,CAAC,IAAM,cAAc,CAAC,OAAc,CAAC;4CAAE,GAAG,IAAI;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAChF,QAAQ;;;;;;;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAe;;;;;;kCAC9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO,WAAW,KAAK;gCACvB,UAAU,CAAC,IAAM,cAAc,CAAC,OAAc,CAAC;4CAAE,GAAG,IAAI;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACjF,QAAQ;;;;;;;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAkB;;;;;;kCACjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAM,eAAe,SAAS;gCAC9B,aAAY;gCACZ,WAAU;gCACV,OAAO,WAAW,QAAQ;gCAC1B,UAAU,CAAC,IAAM,cAAc,CAAC,OAAc,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACpF,QAAQ;;;;;;0CAEV,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,gBAAgB,CAAC,IAAM,CAAC;gCACvC,cAAY,eAAe,kBAAkB;gCAC7C,WAAU;0CAET,6BAAe,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;yDAAe,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAKtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAA0B;;;;;;kCACzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAM,sBAAsB,SAAS;gCACrC,aAAY;gCACZ,WAAU;gCACV,OAAO,WAAW,eAAe,IAAI;gCACrC,UAAU,CAAC,IAAM,cAAc,CAAC,OAAc,CAAC;4CAAE,GAAG,IAAI;4CAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAC3F,QAAQ;;;;;;0CAEV,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,uBAAuB,CAAC,IAAM,CAAC;gCAC9C,cAAY,sBAAsB,kBAAkB;gCACpD,WAAU;0CAET,oCAAsB,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;yDAAe,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAG1E,WAAW,eAAe,IAAI,WAAW,eAAe,KAAK,WAAW,QAAQ,kBAC/E,6LAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;0BAI7C,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,WAAU;gBACV,UAAU,WAAY,WAAW,eAAe,KAAK,aAAa,WAAW,eAAe,KAAK,WAAW,QAAQ;0BAEnH,UAAU,oBAAoB;;;;;;;;;;;;AAIvC;GAjGwB;KAAA", "debugId": null}}, {"offset": {"line": 1085, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/auth/page.tsx"], "sourcesContent": [" 'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport {\r\n  Sparkles,\r\n  Zap,\r\n  Mail,\r\n  Lock,\r\n  User,\r\n  ArrowLeft,\r\n  Loader2,\r\n  CheckCircle,\r\n  Eye,\r\n  EyeOff\r\n} from 'lucide-react';\r\nimport { useFirebaseAuth } from '@/hooks/use-firebase-auth';\r\nimport { useToast } from '@/hooks/use-toast';\r\nimport { auth } from '@/lib/firebase/config';\r\nimport VerifyEmail from '@/components/auth/VerifyEmail';\r\nimport LoginForm from '@/components/auth/LoginForm';\r\nimport SignupForm from '@/components/auth/SignupForm';\r\n\r\nexport default function AuthPage() {\r\n  const router = useRouter();\r\n  const { signIn, signUp, signInAnonymous, loading } = useFirebaseAuth();\r\n  const { toast } = useToast();\r\n\r\n  const [signInData, setSignInData] = useState({\r\n    email: '',\r\n    password: ''\r\n  });\r\n\r\n  const [signUpData, setSignUpData] = useState<{\r\n    name?: string;\r\n    email: string;\r\n    password: string;\r\n    confirmPassword?: string;\r\n  }>({\r\n    name: '',\r\n    email: '',\r\n    password: '',\r\n    confirmPassword: ''\r\n  });\r\n\r\n  // Signup verification flow\r\n  const [showVerifyModal, setShowVerifyModal] = useState(false);\r\n  const [pendingSignUp, setPendingSignUp] = useState<typeof signUpData | null>(null);\r\n\r\n  // Password visibility toggles\r\n  const [showSignInPassword, setShowSignInPassword] = useState(false);\r\n  const [showSignUpPassword, setShowSignUpPassword] = useState(false);\r\n  const [showSignUpConfirm, setShowSignUpConfirm] = useState(false);\r\n\r\n  const handleSignIn = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    try {\r\n      await signIn(signInData.email, signInData.password);\r\n      toast({\r\n        title: \"Welcome back!\",\r\n        description: \"You've been signed in successfully.\",\r\n      });\r\n      router.push('/dashboard');\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Sign in failed\",\r\n        description: error instanceof Error ? error.message : \"Please check your credentials and try again.\",\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleSignUp = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (signUpData.password !== signUpData.confirmPassword) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Passwords don't match\",\r\n        description: \"Please make sure your passwords match.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Send verification code first. Do not create user until verified.\r\n      const res = await fetch('/api/auth/send-code', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ email: signUpData.email, type: 'signup' }),\r\n      });\r\n      const body = await res.json();\r\n      if (res.ok && body.ok) {\r\n  setPendingSignUp(signUpData);\r\n  setShowVerifyModal(true);\r\n  toast({ title: 'Verification code sent', description: 'Check your inbox for the 5-digit code.' });\r\n      } else {\r\n        toast({ variant: 'destructive', title: 'Failed to send verification', description: body.error || 'Please try again.' });\r\n      }\r\n    } catch (error) {\r\n      toast({ variant: 'destructive', title: 'Sign up failed', description: error instanceof Error ? error.message : 'Please try again.' });\r\n    }\r\n  };\r\n\r\n  const finishSignUpAfterVerification = async () => {\r\n    if (!pendingSignUp) return;\r\n    try {\r\n      await signUp(pendingSignUp.email, pendingSignUp.password, pendingSignUp.name);\r\n      toast({ title: 'Account created!', description: 'Welcome — your account is ready.' });\r\n      setShowVerifyModal(false);\r\n      setPendingSignUp(null);\r\n      router.push('/dashboard');\r\n    } catch (error) {\r\n      toast({ variant: 'destructive', title: 'Sign up failed', description: error instanceof Error ? error.message : 'Please try again.' });\r\n    }\r\n  };\r\n\r\n  const handleDemoMode = async () => {\r\n    try {\r\n      await signInAnonymous();\r\n      toast({\r\n        title: \"Demo mode activated!\",\r\n        description: \"Welcome to your dashboard! Explore all features without creating an account.\",\r\n      });\r\n      router.push('/dashboard');\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Demo mode failed\",\r\n        description: \"Please try again.\",\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4\">\r\n      {/* Back to Home Button */}\r\n      <Button\r\n        onClick={() => router.push('/')}\r\n        variant=\"ghost\"\r\n        className=\"absolute top-6 left-6 text-gray-600 hover:text-gray-900\"\r\n      >\r\n        <ArrowLeft className=\"w-4 h-4 mr-2\" />\r\n        Back to Home\r\n      </Button>\r\n\r\n      <div className=\"w-full max-w-md\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-8\">\r\n          <div className=\"flex items-center justify-center gap-3 mb-4\">\r\n            <div className=\"w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center\">\r\n              <Sparkles className=\"w-7 h-7 text-white\" />\r\n            </div>\r\n            <span className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\r\n              Crevo\r\n            </span>\r\n          </div>\r\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\r\n            Welcome to Crevo\r\n          </h1>\r\n          <p className=\"text-gray-600\">\r\n            Sign in to your account or create a new one to get started\r\n          </p>\r\n        </div>\r\n\r\n        <Card className=\"border-0 shadow-xl bg-white/80 backdrop-blur-sm\">\r\n          <CardHeader className=\"pb-4\">\r\n            <div className=\"flex items-center justify-center mb-4\">\r\n              <Button\r\n                onClick={handleDemoMode}\r\n                disabled={loading}\r\n                className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\"\r\n                size=\"lg\"\r\n              >\r\n                {loading ? (\r\n                  <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\r\n                ) : (\r\n                  <Zap className=\"w-4 h-4 mr-2\" />\r\n                )}\r\n                Try Demo Mode (No Account Needed)\r\n              </Button>\r\n            </div>\r\n\r\n            <div className=\"relative\">\r\n              <div className=\"absolute inset-0 flex items-center\">\r\n                <Separator className=\"w-full\" />\r\n              </div>\r\n              <div className=\"relative flex justify-center text-xs uppercase\">\r\n                <span className=\"bg-white px-2 text-gray-500\">Or continue with email</span>\r\n              </div>\r\n            </div>\r\n          </CardHeader>\r\n\r\n          <CardContent>\r\n            <Tabs defaultValue=\"signin\" className=\"w-full\">\r\n              <TabsList className=\"grid w-full grid-cols-2\">\r\n                <TabsTrigger value=\"signin\">Sign In</TabsTrigger>\r\n                <TabsTrigger value=\"signup\">Sign Up</TabsTrigger>\r\n              </TabsList>\r\n\r\n              <TabsContent value=\"signin\" className=\"space-y-4 mt-6\">\r\n                <LoginForm\r\n                  signInData={signInData}\r\n                  setSignInData={setSignInData}\r\n                  showSignInPassword={showSignInPassword}\r\n                  setShowSignInPassword={setShowSignInPassword}\r\n                  loading={loading}\r\n                  handleSignIn={handleSignIn}\r\n                />\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"signup\" className=\"space-y-4 mt-6\">\r\n                <SignupForm\r\n                  signUpData={signUpData}\r\n                  setSignUpData={setSignUpData}\r\n                  loading={loading}\r\n                  handleSignUp={handleSignUp}\r\n                />\r\n                {showVerifyModal && pendingSignUp && (\r\n                  <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\r\n                    <VerifyEmail email={pendingSignUp.email} onSuccess={finishSignUpAfterVerification} type=\"signup\" />\r\n                  </div>\r\n                )}\r\n              </TabsContent>\r\n            </Tabs>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Footer */}\r\n        <div className=\"text-center mt-6 text-sm text-gray-500\">\r\n          <p>\r\n            By signing up, you agree to our{' '}\r\n            <a href=\"#\" className=\"text-blue-600 hover:underline\">Terms of Service</a>\r\n            {' '}and{' '}\r\n            <a href=\"#\" className=\"text-blue-600 hover:underline\">Privacy Policy</a>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAYA;AACA;AAEA;AACA;AACA;;;AA3BC;;;;;;;;;;;;;AA6Bc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IACnE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,UAAU;IACZ;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAKxC;QACD,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;IACnB;IAEA,2BAA2B;IAC3B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAE7E,8BAA8B;IAC9B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,OAAO,WAAW,KAAK,EAAE,WAAW,QAAQ;YAClD,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACxD;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,WAAW,QAAQ,KAAK,WAAW,eAAe,EAAE;YACtD,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QAEA,IAAI;YACF,mEAAmE;YACnE,MAAM,MAAM,MAAM,MAAM,uBAAuB;gBAC7C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,OAAO,WAAW,KAAK;oBAAE,MAAM;gBAAS;YACjE;YACA,MAAM,OAAO,MAAM,IAAI,IAAI;YAC3B,IAAI,IAAI,EAAE,IAAI,KAAK,EAAE,EAAE;gBAC3B,iBAAiB;gBACjB,mBAAmB;gBACnB,MAAM;oBAAE,OAAO;oBAA0B,aAAa;gBAAyC;YAC3F,OAAO;gBACL,MAAM;oBAAE,SAAS;oBAAe,OAAO;oBAA+B,aAAa,KAAK,KAAK,IAAI;gBAAoB;YACvH;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBAAE,SAAS;gBAAe,OAAO;gBAAkB,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAAoB;QACrI;IACF;IAEA,MAAM,gCAAgC;QACpC,IAAI,CAAC,eAAe;QACpB,IAAI;YACF,MAAM,OAAO,cAAc,KAAK,EAAE,cAAc,QAAQ,EAAE,cAAc,IAAI;YAC5E,MAAM;gBAAE,OAAO;gBAAoB,aAAa;YAAmC;YACnF,mBAAmB;YACnB,iBAAiB;YACjB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,MAAM;gBAAE,SAAS;gBAAe,OAAO;gBAAkB,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAAoB;QACrI;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM;YACN,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS,IAAM,OAAO,IAAI,CAAC;gBAC3B,SAAQ;gBACR,WAAU;;kCAEV,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAIxC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAK,WAAU;kDAAgG;;;;;;;;;;;;0CAIlH,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAK/B,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;4CACV,MAAK;;gDAEJ,wBACC,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAEnB,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDACf;;;;;;;;;;;;kDAKN,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,wIAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;0CAKpD,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCAAC,cAAa;oCAAS,WAAU;;sDACpC,6LAAC,mIAAA,CAAA,WAAQ;4CAAC,WAAU;;8DAClB,6LAAC,mIAAA,CAAA,cAAW;oDAAC,OAAM;8DAAS;;;;;;8DAC5B,6LAAC,mIAAA,CAAA,cAAW;oDAAC,OAAM;8DAAS;;;;;;;;;;;;sDAG9B,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAS,WAAU;sDACpC,cAAA,6LAAC,0IAAA,CAAA,UAAS;gDACR,YAAY;gDACZ,eAAe;gDACf,oBAAoB;gDACpB,uBAAuB;gDACvB,SAAS;gDACT,cAAc;;;;;;;;;;;sDAIlB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAS,WAAU;;8DACpC,6LAAC,2IAAA,CAAA,UAAU;oDACT,YAAY;oDACZ,eAAe;oDACf,SAAS;oDACT,cAAc;;;;;;gDAEf,mBAAmB,+BAClB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,4IAAA,CAAA,UAAW;wDAAC,OAAO,cAAc,KAAK;wDAAE,WAAW;wDAA+B,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASpG,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;gCAAE;gCAC+B;8CAChC,6LAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAgC;;;;;;gCACrD;gCAAI;gCAAI;8CACT,6LAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlE;GAzNwB;;QACP,qIAAA,CAAA,YAAS;QAC6B,0IAAA,CAAA,kBAAe;QAClD,+HAAA,CAAA,WAAQ;;;KAHJ", "debugId": null}}]}